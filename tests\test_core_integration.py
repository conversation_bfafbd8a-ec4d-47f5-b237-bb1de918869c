#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Core拆分模块集成测试
测试拆分后的模块与主窗口的集成情况
"""

import unittest
import sys
import os
from unittest.mock import Mock, MagicMock, patch
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TestCoreIntegration(unittest.TestCase):
    """Core拆分模块集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试方法前的初始化"""
        # 创建模拟的服务
        self.mock_monitor_service = Mock()
        self.mock_file_service = Mock()
        self.mock_tag_service = Mock()
        self.mock_auto_tag_service = Mock()
        self.mock_ai_manager = Mock()
    
    @patch('smartvault.ui.main_window.monitor_handler.MonitorHandler')
    @patch('smartvault.ui.main_window.ai_integration_handler.AIIntegrationHandler')
    def test_handlers_initialization_in_core(self, mock_ai_handler_class, mock_monitor_handler_class):
        """测试处理器在Core中的初始化"""
        # 创建模拟的处理器实例
        mock_monitor_handler = Mock()
        mock_ai_handler = Mock()
        
        mock_monitor_handler_class.return_value = mock_monitor_handler
        mock_ai_handler_class.return_value = mock_ai_handler
        
        # 模拟主窗口的部分功能
        class MockMainWindow:
            def __init__(self, test_instance):
                self.monitor_service = test_instance.mock_monitor_service
                self.file_service = test_instance.mock_file_service
                self.tag_service = test_instance.mock_tag_service
                self.auto_tag_service = test_instance.mock_auto_tag_service
                self.ai_manager = test_instance.mock_ai_manager
                
                # 初始化处理器（模拟Core中的代码）
                from smartvault.ui.main_window.monitor_handler import MonitorHandler
                from smartvault.ui.main_window.ai_integration_handler import AIIntegrationHandler
                
                self.monitor_handler = MonitorHandler(self)
                self.ai_integration_handler = AIIntegrationHandler(self)
        
        # 创建模拟主窗口
        main_window = MockMainWindow(self)
        
        # 验证处理器被正确初始化
        mock_monitor_handler_class.assert_called_once_with(main_window)
        mock_ai_handler_class.assert_called_once_with(main_window)
        
        self.assertEqual(main_window.monitor_handler, mock_monitor_handler)
        self.assertEqual(main_window.ai_integration_handler, mock_ai_handler)
    
    def test_monitor_handler_signal_connections(self):
        """测试监控处理器的信号连接"""
        from smartvault.ui.main_window.monitor_handler import MonitorHandler
        
        # 创建模拟主窗口
        mock_main_window = Mock()
        mock_main_window.monitor_service = self.mock_monitor_service
        mock_main_window.file_service = self.mock_file_service
        
        # 创建监控处理器
        monitor_handler = MonitorHandler(mock_main_window)
        
        # 模拟信号连接（模拟Core中的代码）
        self.mock_monitor_service.event_callback = monitor_handler.on_monitor_event
        self.mock_file_service.duplicate_suggestion = Mock()
        self.mock_file_service.batch_duplicate_processed = Mock()
        
        # 模拟信号连接
        self.mock_file_service.duplicate_suggestion.connect = Mock()
        self.mock_file_service.batch_duplicate_processed.connect = Mock()
        
        # 执行信号连接
        self.mock_file_service.duplicate_suggestion.connect(monitor_handler.on_duplicate_file_suggestion)
        self.mock_file_service.batch_duplicate_processed.connect(monitor_handler.on_batch_duplicate_processed)
        
        # 验证信号连接
        self.assertEqual(self.mock_monitor_service.event_callback, monitor_handler.on_monitor_event)
        self.mock_file_service.duplicate_suggestion.connect.assert_called_once()
        self.mock_file_service.batch_duplicate_processed.connect.assert_called_once()
    
    def test_ai_handler_signal_connections(self):
        """测试AI处理器的信号连接"""
        from smartvault.ui.main_window.ai_integration_handler import AIIntegrationHandler
        
        # 创建模拟主窗口
        mock_main_window = Mock()
        mock_main_window.file_service = self.mock_file_service
        mock_main_window.tag_service = self.mock_tag_service
        mock_main_window.auto_tag_service = self.mock_auto_tag_service
        mock_main_window.ai_manager = self.mock_ai_manager
        
        # 模拟AI标签面板
        mock_ai_tag_panel = Mock()
        mock_main_window.ai_tag_panel = mock_ai_tag_panel
        
        # 创建AI处理器
        ai_handler = AIIntegrationHandler(mock_main_window)
        
        # 模拟信号连接（模拟Core中的代码）
        mock_ai_tag_panel.tag_accepted = Mock()
        mock_ai_tag_panel.tag_rejected = Mock()
        
        mock_ai_tag_panel.tag_accepted.connect = Mock()
        mock_ai_tag_panel.tag_rejected.connect = Mock()
        
        # 执行信号连接
        mock_ai_tag_panel.tag_accepted.connect(ai_handler.on_ai_tag_accepted)
        mock_ai_tag_panel.tag_rejected.connect(ai_handler.on_ai_tag_rejected)
        
        # 验证信号连接
        mock_ai_tag_panel.tag_accepted.connect.assert_called_once_with(ai_handler.on_ai_tag_accepted)
        mock_ai_tag_panel.tag_rejected.connect.assert_called_once_with(ai_handler.on_ai_tag_rejected)
    
    def test_monitor_handler_startup_integration(self):
        """测试监控处理器启动集成"""
        from smartvault.ui.main_window.monitor_handler import MonitorHandler
        
        # 创建模拟主窗口
        mock_main_window = Mock()
        mock_main_window.monitor_service = self.mock_monitor_service
        mock_main_window.file_service = self.mock_file_service
        
        # 创建监控处理器
        monitor_handler = MonitorHandler(mock_main_window)
        
        # 模拟配置
        with patch('smartvault.utils.config.get_monitor_status') as mock_get_status:
            mock_get_status.return_value = True
            self.mock_monitor_service.get_all_monitors.return_value = []
            
            # 调用启动方法（模拟Core中的调用）
            monitor_handler.start_configured_monitors()
            
            # 验证方法被调用
            mock_get_status.assert_called_once()
            self.mock_monitor_service.get_all_monitors.assert_called_once()
    
    def test_ai_handler_initialization_integration(self):
        """测试AI处理器初始化集成"""
        from smartvault.ui.main_window.ai_integration_handler import AIIntegrationHandler
        
        # 创建模拟主窗口
        mock_main_window = Mock()
        mock_main_window.file_service = self.mock_file_service
        mock_main_window.tag_service = self.mock_tag_service
        mock_main_window.auto_tag_service = self.mock_auto_tag_service
        mock_main_window.ai_manager = self.mock_ai_manager
        
        # 创建AI处理器
        ai_handler = AIIntegrationHandler(mock_main_window)
        
        # 模拟AI管理器初始化
        with patch('smartvault.utils.config.load_config') as mock_load_config:
            mock_config = {'ai_enabled': True}
            mock_load_config.return_value = mock_config
            self.mock_ai_manager.initialize.return_value = True
            self.mock_ai_manager.get_status.return_value = {'enabled': True, 'stage': 2, 'status': 'ready'}
            
            # 模拟file_service有db属性
            self.mock_file_service.db = Mock()
            
            # 调用初始化方法（模拟Core中的调用）
            ai_handler.initialize_ai_manager()
            
            # 验证初始化被调用
            self.mock_ai_manager.initialize.assert_called_once()
    
    def test_handlers_method_delegation(self):
        """测试处理器方法委托"""
        from smartvault.ui.main_window.monitor_handler import MonitorHandler
        from smartvault.ui.main_window.ai_integration_handler import AIIntegrationHandler
        
        # 创建模拟主窗口
        mock_main_window = Mock()
        mock_main_window.monitor_service = self.mock_monitor_service
        mock_main_window.file_service = self.mock_file_service
        mock_main_window.tag_service = self.mock_tag_service
        mock_main_window.auto_tag_service = self.mock_auto_tag_service
        mock_main_window.ai_manager = self.mock_ai_manager
        
        # 创建处理器
        monitor_handler = MonitorHandler(mock_main_window)
        ai_handler = AIIntegrationHandler(mock_main_window)
        
        # 模拟Core中的方法委托
        mock_main_window.monitor_handler = monitor_handler
        mock_main_window.ai_integration_handler = ai_handler
        
        # 测试方法委托是否正确设置
        self.assertEqual(mock_main_window.monitor_handler, monitor_handler)
        self.assertEqual(mock_main_window.ai_integration_handler, ai_handler)
        
        # 测试方法是否可调用
        self.assertTrue(hasattr(monitor_handler, 'on_monitor_event'))
        self.assertTrue(hasattr(monitor_handler, 'start_configured_monitors'))
        self.assertTrue(hasattr(ai_handler, 'initialize_ai_manager'))
        self.assertTrue(hasattr(ai_handler, 'update_ai_tag_suggestions'))
    
    def test_error_handling_in_integration(self):
        """测试集成中的错误处理"""
        from smartvault.ui.main_window.monitor_handler import MonitorHandler
        from smartvault.ui.main_window.ai_integration_handler import AIIntegrationHandler
        
        # 创建模拟主窗口
        mock_main_window = Mock()
        mock_main_window.monitor_service = self.mock_monitor_service
        mock_main_window.file_service = self.mock_file_service
        mock_main_window.tag_service = self.mock_tag_service
        mock_main_window.auto_tag_service = self.mock_auto_tag_service
        mock_main_window.ai_manager = self.mock_ai_manager
        
        # 创建处理器
        monitor_handler = MonitorHandler(mock_main_window)
        ai_handler = AIIntegrationHandler(mock_main_window)
        
        # 测试监控处理器错误处理
        self.mock_monitor_service.get_all_monitors.side_effect = Exception("数据库错误")
        
        try:
            monitor_handler.toggle_all_monitors()
            # 应该不抛出异常
        except Exception as e:
            self.fail(f"监控处理器应该处理异常: {e}")
        
        # 测试AI处理器错误处理
        self.mock_ai_manager.initialize.side_effect = Exception("AI服务错误")
        
        try:
            ai_handler.initialize_ai_manager()
            # 应该不抛出异常
        except Exception as e:
            self.fail(f"AI处理器应该处理异常: {e}")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)