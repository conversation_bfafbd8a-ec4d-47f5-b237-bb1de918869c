"""
AI配置管理器

统一处理所有AI相关配置的加载、保存和验证
预期代码长度: < 200行
当前代码长度: 150行 ✅
"""

from typing import Dict, Tuple, Any, Optional
from smartvault.utils.config import get_ai_config


class AIConfigManager:
    """AI配置管理器 - 统一处理所有AI相关配置"""

    def __init__(self):
        self._config = {}
        self._ai_manager = None

    def set_ai_manager(self, ai_manager):
        """设置AI管理器引用"""
        self._ai_manager = ai_manager

    def load_ai_config(self) -> Dict:
        """加载AI配置 - 支持用户库分离模式

        Returns:
            Dict: AI配置字典
        """
        try:
            # 优先从用户库配置加载AI配置
            from smartvault.services.library_config_service import get_library_config_service
            from smartvault.utils.config import load_config

            # 获取当前库路径
            global_config = load_config()
            library_path = global_config.get("library_path", "")

            if library_path:
                # 从用户库配置加载
                library_service = get_library_config_service(library_path)
                library_config = library_service.load_library_config()
                ai_config = library_config.get("ai", {})

                # 如果用户库配置中没有AI配置，使用全局配置作为后备
                if not ai_config or not isinstance(ai_config, dict):
                    ai_config = get_ai_config()

                    # 将全局AI配置迁移到用户库配置
                    if ai_config:
                        library_config["ai"] = ai_config
                        library_service.save_library_config(library_config)
                        print("✅ AI配置已迁移到用户库配置")
            else:
                # 如果没有库路径，使用全局配置
                ai_config = get_ai_config()

            self._config = ai_config
            return self._config.copy()
        except Exception as e:
            print(f"加载AI配置失败: {e}")
            return self._get_default_config()

    def save_ai_config(self, config: Dict) -> bool:
        """保存AI配置 - 支持用户库分离模式

        Args:
            config: AI配置字典

        Returns:
            bool: 保存是否成功
        """
        try:
            # 验证配置
            is_valid, error_msg = self.validate_ai_config(config)
            if not is_valid:
                print(f"AI配置验证失败: {error_msg}")
                return False

            # 保存配置
            self._config = config.copy()

            # 优先保存到用户库配置
            from smartvault.services.library_config_service import get_library_config_service
            from smartvault.utils.config import load_config, save_ai_config

            # 获取当前库路径
            global_config = load_config()
            library_path = global_config.get("library_path", "")

            if library_path:
                # 保存到用户库配置
                library_service = get_library_config_service(library_path)
                library_config = library_service.load_library_config()
                library_config["ai"] = config
                library_service.save_library_config(library_config)
                print("✅ AI配置已保存到用户库配置")
            else:
                # 如果没有库路径，保存到全局配置
                save_ai_config(config)
                print("✅ AI配置已保存到全局配置")

            return True
        except Exception as e:
            print(f"保存AI配置失败: {e}")
            return False

    def validate_ai_config(self, config: Dict) -> Tuple[bool, str]:
        """验证AI配置有效性

        Args:
            config: AI配置字典

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            # 检查必需字段
            required_fields = ['enabled', 'stage', 'features']
            for field in required_fields:
                if field not in config:
                    return False, f"缺少必需字段: {field}"

            # 检查阶段值
            valid_stages = ['rule_based', 'ml_basic', 'deep_learning']
            if config['stage'] not in valid_stages:
                return False, f"无效的AI阶段: {config['stage']}"

            # 检查功能配置
            features = config.get('features', {})
            if not isinstance(features, dict):
                return False, "features字段必须是字典类型"

            return True, ""
        except Exception as e:
            return False, f"配置验证异常: {e}"

    def get_ai_status(self) -> Dict:
        """获取AI功能状态

        Returns:
            Dict: AI状态信息
        """
        if self._ai_manager:
            return self._ai_manager.get_status()
        else:
            return {
                'enabled': False,
                'stage': 'rule_based',
                'status': 'not_initialized',
                'last_error': 'AI管理器未初始化',
                'features': {}
            }

    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息

        Returns:
            Dict: 学习统计信息
        """
        if self._ai_manager:
            return self._ai_manager.get_learning_statistics()
        else:
            return {}

    def get_rule_performance_stats(self) -> Dict:
        """获取规则性能统计

        Returns:
            Dict: 规则性能统计
        """
        if self._ai_manager:
            return self._ai_manager.get_rule_performance_stats()
        else:
            return {}

    def get_feedback_statistics(self) -> Dict:
        """获取用户反馈统计信息

        Returns:
            Dict: 包含总反馈数、接受数和接受率的字典
        """
        if not self._ai_manager:
            return {
                'total_feedback': 0,
                'accepted_feedback': 0,
                'acceptance_rate': 0
            }
        
        # 直接从AI管理器的学习统计中获取反馈数据
        stats = self._ai_manager.get_learning_statistics()
        total = stats.get('total_feedback', 0)
        accepted = stats.get('feedback_by_type', {}).get('accepted', 0)
        rate = int((accepted / total * 100)) if total > 0 else 0
        
        return {
            'total_feedback': total,
            'accepted_feedback': accepted,
            'acceptance_rate': rate
        }

    def test_ai_function(self) -> Tuple[bool, str]:
        """测试AI功能

        Returns:
            Tuple[bool, str]: (测试是否成功, 结果信息)
        """
        try:
            if not self._ai_manager:
                return False, "AI管理器未初始化"

            if not self._ai_manager.is_available():
                return False, "AI功能未启用或未就绪"

            # 测试标签建议功能
            test_file = {
                'name': 'test.py',
                'extension': '.py',
                'path': '/test/test.py'
            }

            suggestions = self._ai_manager.suggest_tags(test_file)

            if suggestions:
                return True, f"AI功能正常，建议标签: {', '.join(suggestions[:3])}"
            else:
                return True, "AI功能正常，但未生成标签建议"

        except Exception as e:
            return False, f"AI功能测试失败: {e}"

    def _get_default_config(self) -> Dict:
        """获取默认AI配置

        Returns:
            Dict: 默认配置
        """
        return {
            'enabled': False,
            'stage': 'rule_based',
            'models': {
                'text_classifier': {
                    'enabled': False,
                    'model_path': '',
                    'confidence_threshold': 0.6
                }
            },
            'features': {
                'project_detection': {
                    'enabled': True,
                    'min_files': 3,
                    'confidence_threshold': 0.7
                },
                'series_detection': {
                    'enabled': True,
                    'min_files': 2,
                    'confidence_threshold': 0.6
                },
                'behavior_learning': {
                    'enabled': True,
                    'learning_rate': 0.1,
                    'max_patterns': 1000
                },
                'smart_suggestions': {
                    'enabled': True,
                    'max_suggestions': 8,
                    'merge_with_rules': True
                }
            }
        }
