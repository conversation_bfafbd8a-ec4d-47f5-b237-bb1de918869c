#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新建文件库的AI模型处理
"""

import sys
import os
import tempfile
import shutil

# 添加项目路径
sys.path.append('d:/PythonProjects2/SmartVault3')

from smartvault.services.library_config_service import LibraryConfigService
from smartvault.utils.config import get_app_data_dir

def test_new_library_ai_handling():
    """测试新建文件库的AI模型处理"""
    print("=== 测试新建文件库的AI模型处理 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"临时目录: {temp_dir}")
    
    try:
        # 新库路径
        new_lib_path = os.path.join(temp_dir, 'TestNewLib')
        print(f"新库路径: {new_lib_path}")
        
        # 检查是否存在全局模型文件
        global_model_path = os.path.join(get_app_data_dir(), "smartvault_ml_model.pkl")
        print(f"全局模型文件存在: {os.path.exists(global_model_path)}")
        if os.path.exists(global_model_path):
            print(f"全局模型文件路径: {global_model_path}")
        
        # 创建文件库配置服务
        service = LibraryConfigService()
        
        # 创建新文件库
        print("\n--- 创建新文件库 ---")
        success, msg = service.create_library_structure(new_lib_path, 'TestNewLib')
        print(f"创建结果: {success}")
        print(f"创建消息: {msg}")
        
        # 检查配置
        print("\n--- 检查配置 ---")
        config = service.load_library_config(new_lib_path)
        ai_config = config.get('ai', {})
        print(f"AI启用状态: {ai_config.get('enabled', False)}")
        
        ml_config = ai_config.get('features', {}).get('ml_basic', {})
        print(f"ML启用状态: {ml_config.get('enabled', False)}")
        print(f"ML模型路径配置: {ml_config.get('model_path', '')}")
        
        # 检查ai_models目录
        print("\n--- 检查ai_models目录 ---")
        ai_models_dir = os.path.join(new_lib_path, 'ai_models')
        print(f"ai_models目录存在: {os.path.exists(ai_models_dir)}")
        
        if os.path.exists(ai_models_dir):
            files = os.listdir(ai_models_dir)
            print(f"ai_models目录内容: {files}")
            
            # 检查模型文件
            model_file = os.path.join(ai_models_dir, 'smartvault_ml_model.pkl')
            print(f"模型文件存在: {os.path.exists(model_file)}")
        else:
            print("ai_models目录不存在")
        
        # 测试切换到新库
        print("\n--- 测试切换到新库 ---")
        switch_config = service.switch_library(new_lib_path)
        print(f"切换后的AI配置: {switch_config.get('ai', {})}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)
        print(f"\n临时目录已清理: {temp_dir}")

if __name__ == "__main__":
    test_new_library_ai_handling()