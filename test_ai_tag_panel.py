#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI标签建议面板功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_tag_panel():
    """测试AI标签建议面板"""
    try:
        print("🧪 开始测试AI标签建议面板...")
        
        # 测试导入
        from smartvault.ui.widgets.ai_tag_suggestion_panel import AITagSuggestionPanel, TagSuggestionItem
        print("✅ AI标签建议面板模块导入成功")
        
        # 测试PySide6依赖
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("✅ PySide6依赖检查通过")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        # 测试创建标签建议项
        item = TagSuggestionItem("Python", 0.9)
        print("✅ TagSuggestionItem创建成功")
        
        # 测试创建AI标签建议面板
        panel = AITagSuggestionPanel()
        print("✅ AITagSuggestionPanel创建成功")
        
        # 测试设置文件选择
        panel.set_file_selection("test_file_id", "test_script.py")
        print("✅ 文件选择设置成功")
        
        # 测试显示建议
        suggestions = [
            ("Python", 0.95),
            ("代码", 0.85),
            ("脚本", 0.75),
            ("开发", 0.65)
        ]
        panel.show_suggestions(suggestions)
        print("✅ 标签建议显示成功")
        
        # 测试清除建议
        panel.clear_suggestions()
        print("✅ 清除建议功能正常")
        
        # 测试无建议状态
        panel.show_no_suggestions()
        print("✅ 无建议状态显示正常")
        
        print("🎉 AI标签建议面板所有测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_integration():
    """测试主窗口集成"""
    try:
        print("\n🧪 开始测试主窗口集成...")
        
        # 测试主窗口核心导入
        from smartvault.ui.main_window.core import MainWindowCore
        print("✅ 主窗口核心模块导入成功")
        
        # 检查AI相关方法是否存在
        methods_to_check = [
            '_update_ai_tag_suggestions',
            '_fetch_ai_suggestions', 
            '_calculate_tag_confidence',
            'on_ai_tag_accepted',
            'on_ai_tag_rejected'
        ]
        
        for method_name in methods_to_check:
            if hasattr(MainWindowCore, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
                return False
        
        print("🎉 主窗口集成测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_manager_integration():
    """测试AI管理器集成"""
    try:
        print("\n🧪 开始测试AI管理器集成...")
        
        # 测试AI管理器导入
        from smartvault.services.ai.ai_manager import AIManager
        print("✅ AI管理器导入成功")
        
        # 创建AI管理器实例
        ai_manager = AIManager()
        print("✅ AI管理器实例创建成功")
        
        # 测试suggest_tags方法
        if hasattr(ai_manager, 'suggest_tags'):
            print("✅ suggest_tags方法存在")
        else:
            print("❌ suggest_tags方法不存在")
            return False
            
        # 测试is_available方法
        if hasattr(ai_manager, 'is_available'):
            print("✅ is_available方法存在")
        else:
            print("❌ is_available方法不存在")
            return False
        
        print("🎉 AI管理器集成测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 SmartVault AI标签建议面板功能测试")
    print("=" * 60)
    
    all_passed = True
    
    # 运行各项测试
    tests = [
        ("AI标签建议面板", test_ai_tag_panel),
        ("主窗口集成", test_main_window_integration),
        ("AI管理器集成", test_ai_manager_integration)
    ]
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！AI标签建议面板功能已成功实现")
        print("\n📋 功能特性:")
        print("  • 智能标签建议面板UI组件")
        print("  • 文件选择时自动显示AI建议")
        print("  • 标签可信度显示和交互")
        print("  • 接受/拒绝标签建议功能")
        print("  • 批量操作支持")
        print("  • 与现有AI管理器集成")
        print("\n🎯 使用方法:")
        print("  1. 启动SmartVault程序")
        print("  2. 确保AI功能已启用")
        print("  3. 选择单个文件查看右侧AI建议面板")
        print("  4. 点击接受/拒绝按钮操作标签建议")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
