#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试在实际新库路径创建文件库
"""

import sys
import os
import shutil

# 添加项目路径
sys.path.append('d:/PythonProjects2/SmartVault3')

from smartvault.services.library_config_service import LibraryConfigService

def test_create_new_library():
    """测试在实际新库路径创建文件库"""
    print("=== 测试在实际新库路径创建文件库 ===")
    
    new_lib_path = 'd:/PythonProjects2/SmartVault3/smartvault/SmartVault_Lib'
    print(f"新库路径: {new_lib_path}")
    
    try:
        # 如果目录已存在，先删除
        if os.path.exists(new_lib_path):
            shutil.rmtree(new_lib_path)
            print(f"已删除现有目录: {new_lib_path}")
        
        # 创建文件库配置服务
        service = LibraryConfigService()
        
        # 创建新文件库
        print("\n--- 创建新文件库 ---")
        success, msg = service.create_library_structure(new_lib_path, 'SmartVault_Lib')
        print(f"创建结果: {success}")
        print(f"创建消息: {msg}")
        
        if success:
            # 检查配置
            print("\n--- 检查配置 ---")
            config = service.load_library_config(new_lib_path)
            ai_config = config.get('ai', {})
            ml_config = ai_config.get('features', {}).get('ml_basic', {})
            
            print(f"AI启用: {ai_config.get('enabled', False)}")
            print(f"ML启用: {ml_config.get('enabled', False)}")
            print(f"模型路径: {ml_config.get('model_path', '')}")
            
            # 检查模型文件
            model_file = os.path.join(new_lib_path, 'ai_models', 'smartvault_ml_model.pkl')
            print(f"模型文件存在: {os.path.exists(model_file)}")
            
            if os.path.exists(model_file):
                file_size = os.path.getsize(model_file)
                print(f"模型文件大小: {file_size} bytes")
            
            # 测试切换到新库
            print("\n--- 测试切换到新库 ---")
            switch_config = service.switch_library(new_lib_path)
            switch_ai_config = switch_config.get('ai', {})
            switch_ml_config = switch_ai_config.get('features', {}).get('ml_basic', {})
            
            print(f"切换后AI启用: {switch_ai_config.get('enabled', False)}")
            print(f"切换后ML启用: {switch_ml_config.get('enabled', False)}")
            print(f"切换后模型路径: {switch_ml_config.get('model_path', '')}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_create_new_library()