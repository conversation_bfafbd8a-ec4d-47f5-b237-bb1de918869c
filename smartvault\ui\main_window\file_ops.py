"""
主窗口文件操作功能
"""

import os
from PySide6.QtWidgets import (
    QMessageBox, QFileDialog, QDialog, QVBoxLayout,
    QLabel, QCheckBox, QDialogButtonBox, QLineEdit
)
from smartvault.ui.dialogs import AddFileDialog


class FileOperationsMixin:
    """文件操作混入类"""

    def show_status_message(self, message, success=True):
        """在状态栏显示高亮消息

        Args:
            message: 要显示的消息
            success: 是否成功消息，True为绿色，False为红色
        """
        status_bar = self.statusBar()
        if success:
            status_bar.setStyleSheet("background-color: #dff0d8; color: #3c763d;")
        else:
            status_bar.setStyleSheet("background-color: #f2dede; color: #a94442;")
        status_bar.showMessage(message)

        # 3秒后恢复默认样式
        from PySide6.QtCore import QTimer
        QTimer.singleShot(3000, lambda: status_bar.setStyleSheet(""))

    def on_add_file(self):
        """添加文件到智能文件库"""
        dialog = AddFileDialog(self)
        result = dialog.exec()

        if result == AddFileDialog.Accepted and dialog.file_list:
            # 获取入库方式
            entry_type = dialog.get_entry_type()

            # 获取选择的设备标签ID
            device_tag_id = dialog.get_selected_device_tag_id()

            # 获取入库方式显示文本
            entry_type_text = {
                "link": "链接",
                "copy": "复制",
                "move": "移动"
            }.get(entry_type, entry_type)

            # 添加文件到智能文件库
            added_files = []
            failed_files = []
            total_files = len(dialog.file_list)

            # 显示进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.start_progress(f"添加文件", total_files, True)

            # 显示进度信息
            self.show_status_message(f"正在添加 {total_files} 个文件...", True)

            for i, file_path in enumerate(dialog.file_list):
                try:
                    # 更新进度条
                    if hasattr(self, 'compact_progress_bar'):
                        self.compact_progress_bar.set_progress(i, f"添加文件 {i+1}/{total_files}")

                    # 如果是文件夹，则提取实际路径
                    if file_path.startswith("文件夹: "):
                        file_path = file_path[5:].strip()  # 移除"文件夹: "前缀

                    # 添加文件
                    file_id = self.file_service.add_file(file_path, entry_type)

                    # 如果选择了设备来源，自动关联设备标签
                    if device_tag_id:
                        self.tag_service.add_tag_to_file(file_id, device_tag_id)

                    added_files.append(file_path)
                except Exception as e:
                    failed_files.append((file_path, str(e)))

            # 完成进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.finish_progress(f"完成添加 {len(added_files)} 个文件", True, 2000)

            # 显示结果
            if added_files:
                # 更新状态栏，使用高亮显示
                self.show_status_message(f"成功{entry_type_text} {len(added_files)} 个文件到智能文件库", True)

                # 刷新文件列表，但不显示加载消息
                self._load_files_silently()

            if failed_files:
                # 显示失败消息，使用高亮显示
                self.show_status_message(f"{len(failed_files)} 个文件{entry_type_text}失败", False)

    def _process_device_files_addition(self, dialog, device_name):
        """处理设备文件添加（专用于设备文件夹创建后的文件添加）

        Args:
            dialog: 文件添加对话框实例
            device_name: 设备名称
        """
        try:
            # 获取入库方式
            entry_type = dialog.get_entry_type()

            # 获取选择的设备标签ID
            device_tag_id = dialog.get_selected_device_tag_id()

            # 获取入库方式显示文本
            entry_type_text = {
                "link": "链接",
                "copy": "复制",
                "move": "移动"
            }.get(entry_type, entry_type)

            # 添加文件到智能文件库
            added_files = []
            failed_files = []
            total_files = len(dialog.file_list)

            # 显示进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.start_progress(f"添加设备文件", total_files, True)

            # 显示进度信息
            self.show_status_message(f"正在从设备 {device_name} 添加 {total_files} 个项目...", True)

            for i, file_path in enumerate(dialog.file_list):
                try:
                    # 更新进度条
                    if hasattr(self, 'compact_progress_bar'):
                        self.compact_progress_bar.set_progress(i, f"添加设备文件 {i+1}/{total_files}")

                    # 检查是否是文件夹
                    is_folder = file_path.startswith("文件夹: ")

                    # 如果是文件夹，则提取实际路径
                    if is_folder:
                        file_path = file_path[5:].strip()  # 移除"文件夹: "前缀

                    # 添加文件
                    result = self.file_service.add_file(file_path, entry_type)

                    # 如果选择了设备来源，自动关联设备标签
                    if device_tag_id:
                        if is_folder:
                            # 文件夹添加返回folder_group_id，需要获取该组的所有文件
                            folder_group_id = result
                            print(f"🏷️ 开始为文件夹组 {folder_group_id} 关联设备标签 {device_tag_id}")
                            folder_files = self._get_files_by_folder_group_id(folder_group_id)
                            print(f"📁 文件夹组包含 {len(folder_files)} 个文件")

                            success_count = 0
                            for file_id in folder_files:
                                try:
                                    self.tag_service.add_tag_to_file(file_id, device_tag_id)
                                    success_count += 1
                                except Exception as e:
                                    print(f"为文件 {file_id} 添加设备标签失败: {e}")

                            print(f"✅ 成功为 {success_count}/{len(folder_files)} 个文件关联设备标签")
                        else:
                            # 单文件添加
                            try:
                                self.tag_service.add_tag_to_file(result, device_tag_id)
                                print(f"✅ 单文件 {result} 设备标签关联成功")
                            except Exception as e:
                                print(f"单文件 {result} 设备标签关联失败: {e}")

                    added_files.append(file_path)
                except Exception as e:
                    failed_files.append((file_path, str(e)))

            # 完成进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.finish_progress(f"完成添加设备文件", True, 2000)

            # 显示结果
            if added_files:
                # 更新状态栏，使用高亮显示
                self.show_status_message(
                    f"成功从设备 {device_name} {entry_type_text} {len(added_files)} 个文件到智能文件库",
                    True
                )

                # 刷新文件列表，但不显示加载消息
                self._load_files_silently()

            if failed_files:
                # 显示失败消息，使用高亮显示
                self.show_status_message(f"{len(failed_files)} 个文件{entry_type_text}失败", False)

        except Exception as e:
            self.show_status_message(f"设备文件添加失败: {e}", False)
            import traceback
            traceback.print_exc()

    def _get_files_by_folder_group_id(self, folder_group_id):
        """根据文件夹组ID获取所有文件ID

        Args:
            folder_group_id: 文件夹组ID

        Returns:
            list: 文件ID列表
        """
        try:
            from smartvault.data.database import Database

            db = Database.create_from_config()
            cursor = db.conn.cursor()

            print(f"🔍 查询文件夹组 {folder_group_id} 的文件...")
            cursor.execute(
                "SELECT id FROM files WHERE folder_group_id = ?",
                (folder_group_id,)
            )

            file_ids = [row[0] for row in cursor.fetchall()]
            print(f"📋 找到 {len(file_ids)} 个文件ID")
            db.close()

            return file_ids

        except Exception as e:
            print(f"获取文件夹组文件失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def on_add_folder(self):
        """添加文件夹到智能文件库"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择要添加的文件夹"
        )

        if not folder:
            return

        # 创建添加文件对话框
        dialog = AddFileDialog(self)
        dialog.file_list.append(folder)
        dialog.file_list_widget.addItem(f"文件夹: {folder}")

        # 显示对话框
        result = dialog.exec()

        if result == AddFileDialog.Accepted:
            # 获取入库方式
            entry_type = dialog.get_entry_type()

            # 获取选择的设备标签ID
            device_tag_id = dialog.get_selected_device_tag_id()

            # 获取入库方式显示文本
            entry_type_text = {
                "link": "链接",
                "copy": "复制",
                "move": "移动"
            }.get(entry_type, entry_type)

            # 添加文件夹到智能文件库
            try:
                # 显示进度条（不确定进度）
                if hasattr(self, 'compact_progress_bar'):
                    self.compact_progress_bar.set_indeterminate(f"添加文件夹")

                # 显示进度信息
                self.show_status_message(f"正在添加文件夹: {folder}...", True)

                # 获取添加前的文件数量
                before_count = self.file_service.get_file_count()

                # 添加文件夹
                folder_group_id = self.file_service.add_file(folder, entry_type)

                # 如果选择了设备来源，为文件夹中的所有文件关联设备标签
                if device_tag_id:
                    folder_files = self._get_files_by_folder_group_id(folder_group_id)
                    for file_id in folder_files:
                        try:
                            self.tag_service.add_tag_to_file(file_id, device_tag_id)
                        except Exception as e:
                            print(f"为文件 {file_id} 添加设备标签失败: {e}")

                # 获取添加后的文件数量
                after_count = self.file_service.get_file_count()

                # 计算新增文件数量
                added_count = after_count - before_count

                # 完成进度条
                if hasattr(self, 'compact_progress_bar'):
                    self.compact_progress_bar.finish_progress(f"完成添加文件夹", True, 2000)

                # 更新状态栏，使用高亮显示
                if added_count > 0:
                    self.show_status_message(f"成功{entry_type_text}文件夹，共处理 {added_count} 个文件", True)
                else:
                    self.show_status_message(f"成功{entry_type_text}文件夹，未发现新文件", True)

                # 刷新文件列表，但不显示加载消息
                self._load_files_silently()
            except Exception as e:
                # 显示失败消息，使用高亮显示
                self.show_status_message(f"文件夹{entry_type_text}失败: {e}", False)

    def on_remove_file(self, file_ids=None):
        """从智能文件库移除文件

        Args:
            file_ids: 文件ID列表，如果为None则使用当前选中的文件
        """
        # 获取要移除的文件ID
        if file_ids is None:
            selected_indexes = self.file_view.selectedIndexes()
            if not selected_indexes:
                return

            file_ids = []
            for index in selected_indexes:
                file_id = index.data(self.file_view.model.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

        if not file_ids:
            return

        # 确认移除
        result = QMessageBox.question(
            self,
            "确认移除",
            f"确定要从智能文件库移除选中的 {len(file_ids)} 个文件吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if result == QMessageBox.Yes:
            # 移除文件
            removed_files = []
            failed_files = []
            total_files = len(file_ids)

            # 显示进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.start_progress(f"正在移除 {total_files} 个文件...", total_files, False)

            for i, file_id in enumerate(file_ids):
                try:
                    # 更新进度条
                    if hasattr(self, 'compact_progress_bar'):
                        self.compact_progress_bar.set_progress(i + 1, f"移除文件 {i+1}/{total_files}")

                    self.file_service.remove_file(file_id, delete_physical=False)
                    removed_files.append(file_id)
                except Exception as e:
                    failed_files.append((file_id, str(e)))

            # 完成进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.finish_progress(f"移除完成: {len(removed_files)}/{total_files} 个文件", True, 2000)

            # 刷新整个视图
            self._refresh_view_after_deletion()

            # 显示结果
            if removed_files:
                self.show_status_message(f"已从智能文件库移除 {len(removed_files)} 个文件", True)

            if failed_files:
                self.show_status_message(f"{len(failed_files)} 个文件移除失败", False)

    def on_delete_file(self, file_ids=None):
        """删除文件

        Args:
            file_ids: 文件ID列表，如果为None则使用当前选中的文件
        """
        # 获取要删除的文件ID
        if file_ids is None:
            # 表格视图
            selected_rows = set()
            for index in self.file_view.selectedIndexes():
                selected_rows.add(index.row())

            if not selected_rows:
                return

            file_ids = []
            for row in selected_rows:
                file_id = self.file_view.model.data(
                    self.file_view.model.index(row, 0),
                    self.file_view.model.FileIdRole
                )
                if file_id:
                    file_ids.append(file_id)

        if not file_ids:
            return

        # 加载配置
        from smartvault.utils.config import load_config, save_config
        config = load_config()
        delete_physical = config.get("delete_physical_files", True)

        # 检查文件是否存在
        offline_files = []
        online_files = []

        for file_id in file_ids:
            file_info = self.file_service.get_file_by_id(file_id)
            if not file_info:
                continue

            # 确定文件路径
            file_path = None
            if file_info["entry_type"] == "link":
                file_path = file_info["original_path"]
            else:
                file_path = file_info["library_path"]

            # 检查文件是否存在
            if file_path and os.path.exists(file_path):
                online_files.append(file_id)
            else:
                offline_files.append(file_id)

        # 创建自定义对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("确认删除")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        # 添加提示文本
        message = QLabel(f"确定要删除选中的 {len(file_ids)} 个文件吗？")
        message.setWordWrap(True)
        layout.addWidget(message)

        # 如果有离线文件，显示警告
        if offline_files:
            offline_warning = QLabel(f"注意：{len(offline_files)} 个物理文件不存在/离线，这些文件将仅删除链接信息。")
            offline_warning.setStyleSheet("color: red;")
            offline_warning.setWordWrap(True)
            layout.addWidget(offline_warning)

        # 添加"同时删除物理文件"复选框
        delete_physical_checkbox = QCheckBox("同时删除物理文件")
        delete_physical_checkbox.setChecked(delete_physical)

        # 如果所有文件都离线，禁用复选框
        if len(offline_files) == len(file_ids):
            delete_physical_checkbox.setEnabled(False)
            delete_physical_checkbox.setChecked(False)

        layout.addWidget(delete_physical_checkbox)

        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # 显示对话框
        result = dialog.exec()

        if result == QDialog.Accepted:
            # 保存复选框状态
            delete_physical = delete_physical_checkbox.isChecked()
            config["delete_physical_files"] = delete_physical
            save_config(config)

            # 删除文件
            deleted_files = []
            failed_files = []
            total_files = len(file_ids)

            # 显示进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.start_progress(f"正在删除 {total_files} 个文件...", total_files, False)

            for i, file_id in enumerate(file_ids):
                try:
                    # 更新进度条
                    if hasattr(self, 'compact_progress_bar'):
                        self.compact_progress_bar.set_progress(i + 1, f"删除文件 {i+1}/{total_files}")

                    print(f"正在删除文件ID: {file_id}, 删除物理文件: {delete_physical}")

                    # 获取文件信息，检查文件是否存在
                    file_info = self.file_service.get_file_by_id(file_id)
                    if file_info:
                        file_path = None
                        if file_info["entry_type"] == "link":
                            file_path = file_info["original_path"]
                        else:
                            file_path = file_info["library_path"]

                        # 规范化路径
                        file_path = os.path.normpath(file_path)
                        file_exists = os.path.exists(file_path)

                        print(f"文件路径: {file_path}, 文件存在: {file_exists}")

                        # 如果文件不存在且用户选择了删除物理文件，提示用户
                        if delete_physical and not file_exists:
                            print(f"警告: 物理文件不存在，仅删除数据库记录")

                    # 删除文件
                    self.file_service.remove_file(file_id, delete_physical=delete_physical, use_recycle_bin=True)
                    deleted_files.append(file_id)

                    print(f"文件ID: {file_id} 已成功删除")
                except Exception as e:
                    print(f"删除文件失败: {e}")
                    import traceback
                    traceback.print_exc()
                    failed_files.append((file_id, str(e)))

            # 完成进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.finish_progress(f"删除完成: {len(deleted_files)}/{total_files} 个文件", True, 2000)

            # 刷新整个视图（而不是逐个移除）
            self._refresh_view_after_deletion()

            # 显示结果
            if deleted_files:
                self.show_status_message(f"已删除 {len(deleted_files)} 个文件", True)

            if failed_files:
                self.show_status_message(f"{len(failed_files)} 个文件删除失败", False)

    def _refresh_view_after_deletion(self):
        """删除文件后刷新视图"""
        try:
            # 重新加载文件列表，更新分页信息
            self.load_initial_data(show_message=False)
            print("删除后视图已刷新")
        except Exception as e:
            print(f"删除后刷新视图失败: {e}")

    def _load_files_silently(self):
        """静默加载文件列表（不显示状态栏消息）"""
        try:
            filter_type = self.current_folder_filter.get('type')

            if filter_type == "staging":
                # 🔧 优化：复用缓存的中转文件夹数据，避免重复查询
                if hasattr(self, '_staging_cache') and self._staging_cache is not None:
                    display_items = self._staging_cache
                else:
                    display_items = self.file_service.get_staging_display_items()
                    self._staging_cache = display_items

                # 应用分页
                page_size = self.file_view.get_current_page_size()
                if page_size >= 999999:  # "全部"选项
                    files = display_items
                else:
                    files = display_items[:page_size]
            else:
                # 普通文件加载
                files = self._load_files_with_pagination()

            self.file_view.set_files(files)
            print("文件列表已刷新")
        except Exception as e:
            print(f"加载文件列表失败: {e}")

    def on_rename_file(self, file_id=None):
        """重命名文件

        Args:
            file_id: 文件ID，如果为None则使用当前选中的文件
        """
        # 获取要重命名的文件ID
        if file_id is None:
            selected_indexes = self.file_view.selectedIndexes()
            if not selected_indexes:
                return

            # 只重命名第一个选中的文件
            row = selected_indexes[0].row()
            file_id = self.file_view.model.data(
                self.file_view.model.index(row, 0),
                self.file_view.model.FileIdRole
            )

        if not file_id:
            return

        # 获取文件信息
        file_info = self.file_service.get_file_by_id(file_id)
        if not file_info:
            self.show_status_message("文件不存在", False)
            return

        # 创建自定义重命名对话框

        dialog = QDialog(self)
        dialog.setWindowTitle("重命名文件")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        # 添加提示文本
        label = QLabel("请输入新的文件名:")
        layout.addWidget(label)

        # 添加输入框
        line_edit = QLineEdit(dialog)
        line_edit.setText(file_info["name"])
        layout.addWidget(line_edit)

        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # 设置焦点到输入框并选择文件名部分（不包括扩展名）
        dialog.setFocusProxy(line_edit)
        name = os.path.splitext(file_info["name"])[0]
        line_edit.setSelection(0, len(name))

        # 显示对话框
        result = dialog.exec()

        if result == QDialog.Accepted:
            new_name = line_edit.text()
            if new_name and new_name != file_info["name"]:
                try:
                    # 重命名文件
                    success = self.file_service.rename_file(file_id, new_name)
                    if success:
                        # 更新视图
                        self._load_files_silently()
                        self.show_status_message(f"已重命名文件: {new_name}", True)
                    else:
                        self.show_status_message("重命名文件失败", False)
                except Exception as e:
                    QMessageBox.critical(self, "重命名文件", f"重命名文件失败: {e}")
                    self.show_status_message("重命名文件失败", False)

    def on_move_file(self, file_id=None):
        """移动文件

        Args:
            file_id: 文件ID，如果为None则使用当前选中的文件
        """
        # 获取要移动的文件ID
        if file_id is None:
            selected_indexes = self.file_view.selectedIndexes()
            if not selected_indexes:
                return

            # 只移动第一个选中的文件
            row = selected_indexes[0].row()
            file_id = self.file_view.model.data(
                self.file_view.model.index(row, 0),
                self.file_view.model.FileIdRole
            )

        if not file_id:
            return

        # 获取文件信息
        file_info = self.file_service.get_file_by_id(file_id)
        if not file_info:
            self.show_status_message("文件不存在", False)
            return

        # 确定当前文件路径
        current_path = None
        if file_info["entry_type"] == "link":
            current_path = file_info["original_path"]
        else:
            current_path = file_info["library_path"]

        # 检查文件是否存在
        if not os.path.exists(current_path):
            QMessageBox.warning(
                self,
                "移动文件",
                f"文件不存在: {current_path}\n无法移动离线文件。"
            )
            return

        # 选择目标文件夹
        from PySide6.QtWidgets import QFileDialog
        target_folder = QFileDialog.getExistingDirectory(
            self,
            "选择目标文件夹",
            os.path.dirname(current_path)
        )

        if not target_folder:
            return

        try:
            # 移动文件
            success = self.file_service.move_file_to_folder(file_id, target_folder)
            if success:
                # 更新视图
                self._load_files_silently()
                print(f"文件已移动到: {target_folder}，视图已更新")
                self.show_status_message(f"已移动文件到: {target_folder}", True)
            else:
                self.show_status_message("移动文件失败", False)
        except Exception as e:
            QMessageBox.critical(self, "移动文件", f"移动文件失败: {e}")
            self.show_status_message("移动文件失败", False)

    def on_export_files(self, file_ids):
        """批量导出文件到指定目录

        Args:
            file_ids: 文件ID列表
        """
        if not file_ids:
            return

        # 直接使用简单导出功能
        self._simple_export(file_ids)

    def _simple_export(self, file_ids):
        """简单导出功能（不使用对话框）"""
        try:
            from PySide6.QtWidgets import QFileDialog, QMessageBox
            import os
            import shutil

            # 选择导出目录
            export_dir = QFileDialog.getExistingDirectory(
                self,
                "选择导出目录",
                os.path.expanduser("~/Desktop")
            )

            if not export_dir:
                return

            # 执行导出
            exported_files = []
            failed_files = []
            total_files = len(file_ids)

            # 显示进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.start_progress(f"正在导出 {total_files} 个文件...", total_files, False)

            for i, file_id in enumerate(file_ids):
                try:
                    # 更新进度条
                    if hasattr(self, 'compact_progress_bar'):
                        self.compact_progress_bar.set_progress(i + 1, f"导出文件 {i+1}/{total_files}")

                    # 获取文件信息
                    file_info = self.file_service.get_file_by_id(file_id)
                    if not file_info:
                        failed_files.append((file_id, "文件信息不存在"))
                        continue

                    # 确定源文件路径
                    if file_info["entry_type"] == "link":
                        source_path = file_info["original_path"]
                    else:
                        source_path = file_info["library_path"]

                    # 检查源文件是否存在
                    if not os.path.exists(source_path):
                        failed_files.append((file_info["name"], "源文件不存在"))
                        continue

                    # 确定目标文件路径
                    target_path = os.path.join(export_dir, file_info["name"])

                    # 处理重名文件
                    if os.path.exists(target_path):
                        base_name, ext = os.path.splitext(file_info["name"])
                        counter = 1
                        while os.path.exists(target_path):
                            new_name = f"{base_name}_{counter}{ext}"
                            target_path = os.path.join(export_dir, new_name)
                            counter += 1

                    # 复制文件
                    shutil.copy2(source_path, target_path)
                    exported_files.append(file_info["name"])

                except Exception as e:
                    failed_files.append((file_info.get("name", file_id), str(e)))

            # 完成进度条
            if hasattr(self, 'compact_progress_bar'):
                self.compact_progress_bar.finish_progress(f"导出完成: {len(exported_files)}/{total_files} 个文件", True, 2000)

            # 显示结果
            if exported_files:
                self.show_status_message(f"成功导出 {len(exported_files)} 个文件到 {export_dir}", True)

            if failed_files:
                failed_msg = f"{len(failed_files)} 个文件导出失败"
                self.show_status_message(failed_msg, False)

                # 显示详细错误信息
                if len(failed_files) <= 5:  # 只显示前5个错误
                    error_details = "\n".join([f"• {name}: {error}" for name, error in failed_files])
                    QMessageBox.warning(self, "导出失败", f"以下文件导出失败：\n\n{error_details}")

        except Exception as e:
            QMessageBox.critical(self, "导出文件", f"导出操作失败: {e}")
            self.show_status_message("导出文件失败", False)

    def on_open_file(self, file_id=None):
        """打开文件

        Args:
            file_id: 文件ID，如果为None则使用当前选中的文件
        """
        # 获取要打开的文件ID
        if file_id is None:
            selected_indexes = self.file_view.selectedIndexes()
            if not selected_indexes:
                return

            # 只打开第一个选中的文件
            row = selected_indexes[0].row()
            file_id = self.file_view.model.data(
                self.file_view.model.index(row, 0),
                self.file_view.model.FileIdRole
            )

        if not file_id:
            return

        # 打开文件
        try:
            self.file_service.open_file(file_id)
            self.show_status_message(f"已打开文件", True)
        except Exception as e:
            QMessageBox.critical(self, "打开文件", f"打开文件失败: {e}")
            self.show_status_message("打开文件失败", False)

    def on_manage_file_tags(self, file_ids):
        """管理文件标签

        Args:
            file_ids: 文件ID列表
        """
        try:
            from smartvault.ui.dialogs.file_tag_dialog import FileTagDialog

            # 创建文件标签管理对话框
            dialog = FileTagDialog(file_ids, self)

            # 连接标签变化信号
            dialog.tags_changed.connect(self.on_file_tags_changed)

            # 显示对话框
            dialog.exec()

            # 移除不必要的成功提示，符合"用户干预极简化"原则

        except Exception as e:
            QMessageBox.critical(self, "标签管理", f"打开标签管理对话框失败: {e}")
            self.show_status_message("标签管理失败", False)

    def on_file_tags_changed(self):
        """处理文件标签变化事件"""
        try:
            # 刷新导航面板中的标签列表
            if hasattr(self, 'navigation_panel'):
                self.navigation_panel.refresh_tags()

            # 刷新文件列表中的标签显示
            if hasattr(self, 'file_view'):
                # 刷新表格视图的标签缓存
                if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                    self.file_view.table_view.model.refresh_file_tags()

                # 刷新网格视图的标签缓存
                if hasattr(self.file_view, 'grid_view') and hasattr(self.file_view.grid_view, 'model'):
                    self.file_view.grid_view.model.file_tags_cache.clear()

            self.show_status_message("文件标签已更新", True)

        except Exception as e:
            print(f"处理标签变化失败: {e}")
            self.show_status_message("标签更新失败", False)

    def on_clear_file_tags(self, file_ids):
        """清除文件标签

        Args:
            file_ids: 文件ID列表
        """
        try:
            from PySide6.QtWidgets import QMessageBox
            from smartvault.services.tag_service import TagService

            # 确认对话框
            file_count = len(file_ids)
            if file_count == 1:
                message = "确定要清除该文件的所有标签吗？"
            else:
                message = f"确定要清除这 {file_count} 个文件的所有标签吗？"

            reply = QMessageBox.question(
                self,
                "清除标签",
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 清除标签
            tag_service = TagService()
            cleared_count = 0

            # 性能优化：批量清除标签，避免逐个删除导致的性能问题
            try:
                # 使用批量删除SQL语句
                cursor = tag_service.db.conn.cursor()
                placeholders = ','.join('?' * len(file_ids))
                cursor.execute(
                    f"DELETE FROM file_tags WHERE file_id IN ({placeholders})",
                    file_ids
                )
                cleared_count = cursor.rowcount
                tag_service.db.conn.commit()
            except Exception as e:
                print(f"批量清除标签失败，回退到逐个删除: {e}")
                # 回退到原有方法
                for file_id in file_ids:
                    file_tags = tag_service.get_file_tags(file_id)
                    for tag in file_tags:
                        if tag_service.remove_tag_from_file(file_id, tag['id']):
                            cleared_count += 1

            # 触发标签变化事件
            self.on_file_tags_changed()

            # 显示结果
            if cleared_count > 0:
                if file_count == 1:
                    self.show_status_message("文件标签已清除", True)
                else:
                    self.show_status_message(f"已清除 {file_count} 个文件的标签", True)
            else:
                self.show_status_message("没有标签需要清除", True)

        except Exception as e:
            QMessageBox.critical(self, "清除标签", f"清除标签失败: {e}")
            self.show_status_message("清除标签失败", False)
