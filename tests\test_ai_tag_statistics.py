"""AI标签统计功能测试用例"""
import unittest
import os
import sqlite3
import tempfile
import logging
from datetime import datetime, timedelta
from PySide6.QtWidgets import QApplication

from smartvault.ui.widgets.ai_tag_suggestion_panel import AITagSuggestionPanel
from smartvault.services.ai.ai_manager import AIManager
from smartvault.data.database import Database

class TestAITagStatistics(unittest.TestCase):
    """测试AI标签统计功能"""

    def setUp(self):
        """测试初始化"""
        # 初始化Qt应用
        self.app = QApplication.instance() or QApplication([])
        
        # 创建面板实例
        self.panel = AITagSuggestionPanel()
        
        # 创建临时数据库
        self.db_path = tempfile.mktemp(suffix='.db')
        self.db = Database(self.db_path)
        self.db._init_db()
        
        # 确保ai_feedback表存在
        with self.db.conn:
            cursor = self.db.conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS ai_feedback (
                    id TEXT PRIMARY KEY,
                    analysis_id TEXT NOT NULL,
                    feedback_type TEXT NOT NULL,
                    user_action TEXT,
                    created_at TEXT NOT NULL
                )
            """)
            self.db.conn.commit()
        
        # 初始化AI管理器
        self.ai_manager = AIManager()
        
        # 使用测试数据库初始化
        config = {
            "advanced": {"enable_ai_features": True},
            "ai": {
                "features": {
                    "ml_basic": {"enabled": False}
                }
            }
        }
        self.ai_manager.initialize(config, db=self.db)
        
        # 确保面板使用我们的AI管理器
        self.panel.ai_manager = self.ai_manager
        self.panel.ai_manager.db = self.db  # 确保使用测试数据库
        
        # 初始化logger
        self.logger = logging.getLogger(__name__)
        
        # 添加详细调试信息
        print(f"AI管理器自动连接状态: {getattr(self.ai_manager, '_auto_connect', '未定义')}")
        print(f"测试数据库路径: {self.db_path}")
        print(f"AI管理器数据库连接: {self.ai_manager.db.conn}")
        print(f"面板AI管理器数据库连接: {self.panel.ai_manager.db.conn}")
        print(f"AI管理器初始化状态: {self.ai_manager.initialization_status}")
        print(f"AI管理器是否可用: {self.ai_manager.is_available()}")
        
        # 验证面板是否使用正确的AI管理器
        print(f"面板AI管理器ID: {id(self.panel.ai_manager)}")
        print(f"测试AI管理器ID: {id(self.ai_manager)}")
        
        # 测试数据
        self.test_file_id = "test_file_123"
        self.test_tags = [
            ("文档", 0.9),
            ("图片", 0.8),
            ("视频", 0.7)
        ]

    def test_tag_acceptance_statistics(self):
        """测试标签接受统计"""
        # 设置测试文件
        self.panel.set_file_selection(
            self.test_file_id, 
            "test_file.txt",
            self.test_tags
        )
        
        # 模拟接受第一个标签
        self.panel.on_tag_accepted("文档")
        
        # 确保事务已提交
        self.db.conn.commit()
        
        # 检查数据库记录
        with self.db.conn:
            cursor = self.db.conn.cursor()
            # 验证表内容
            cursor.execute("SELECT * FROM ai_feedback")
            records = cursor.fetchall()
            self.logger.info(f"当前ai_feedback表记录: {records}")
            cursor.execute("""
                SELECT COUNT(*) FROM ai_feedback 
                WHERE feedback_type = 'accepted'
            """)
            count = cursor.fetchone()[0]
            self.assertEqual(1, count, "应记录1条接受反馈")
            
            # 检查统计查询
            cursor.execute("""
                SELECT COUNT(*) as total,
                       SUM(CASE WHEN feedback_type = 'accepted' THEN 1 ELSE 0 END) as accepted
                FROM ai_feedback
            """)
            stats = cursor.fetchone()
            self.assertEqual(1, stats[0], "总反馈数应为1")
            self.assertEqual(1, stats[1], "接受数应为1")

    def test_tag_rejection_statistics(self):
        """测试标签拒绝统计"""
        # 设置测试文件
        self.panel.set_file_selection(
            self.test_file_id, 
            "test_file.txt",
            self.test_tags
        )
        
        # 模拟拒绝第二个标签
        self.panel.on_tag_rejected("图片")
        
        # 检查数据库记录
        with self.db.conn:
            cursor = self.db.conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM ai_feedback 
                WHERE feedback_type = 'rejected'
            """)
            count = cursor.fetchone()[0]
            self.assertEqual(1, count, "应记录1条拒绝反馈")

    def test_multiple_actions_statistics(self):
        """测试多操作统计"""
        # 设置测试文件
        self.panel.set_file_selection(
            self.test_file_id, 
            "test_file.txt",
            self.test_tags
        )
        
        # 模拟多个操作
        self.panel.on_tag_accepted("文档")
        self.panel.on_tag_rejected("图片")
        self.panel.on_tag_accepted("视频")
        
        # 检查统计
        with self.db.conn:
            cursor = self.db.conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) as total,
                       SUM(CASE WHEN feedback_type = 'accepted' THEN 1 ELSE 0 END) as accepted,
                       SUM(CASE WHEN feedback_type = 'rejected' THEN 1 ELSE 0 END) as rejected
                FROM ai_feedback
            """)
            stats = cursor.fetchone()
            self.assertEqual(3, stats[0], "总反馈数应为3")
            self.assertEqual(2, stats[1], "接受数应为2")
            self.assertEqual(1, stats[2], "拒绝数应为1")

    def tearDown(self):
        """测试清理"""
        # 关闭数据库连接
        if hasattr(self, 'db'):
            self.db.conn.close()
        # 删除临时数据库文件
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
        
        # 清理Qt应用
        if hasattr(self, 'app'):
            self.app.quit()

if __name__ == '__main__':
    unittest.main()