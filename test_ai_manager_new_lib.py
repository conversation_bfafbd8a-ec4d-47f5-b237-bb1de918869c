#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI管理器在新库中的工作状态
"""

import sys
import os

# 添加项目路径
sys.path.append('d:/PythonProjects2/SmartVault3')

from smartvault.services.library_config_service import LibraryConfigService
from smartvault.services.ai.ai_manager import AIManager
from smartvault.data.database import Database
from smartvault.services.tag_service import TagService
from smartvault.services.auto_tag_service import AutoTagService

def test_ai_manager_in_new_lib():
    """测试AI管理器在新库中的工作状态"""
    print("=== 测试AI管理器在新库中的工作状态 ===")
    
    new_lib_path = 'd:/PythonProjects2/SmartVault3/smartvault/SmartVault_Lib'
    print(f"新库路径: {new_lib_path}")
    
    try:
        # 切换到新库
        service = LibraryConfigService()
        config = service.switch_library(new_lib_path)
        print(f"切换到新库: {new_lib_path}")
        print(f"AI配置启用状态: {config.get('ai', {}).get('enabled', False)}")
        
        # 初始化数据库和服务
        db_path = os.path.join(new_lib_path, 'data', 'smartvault.db')
        print(f"数据库路径: {db_path}")
        print(f"数据库文件存在: {os.path.exists(db_path)}")
        
        db = Database(db_path)
        tag_service = TagService(db)
        auto_tag_service = AutoTagService(db)
        
        # 初始化AI管理器
        print("\n--- 初始化AI管理器 ---")
        ai_manager = AIManager()
        ai_manager.initialize(config, tag_service, auto_tag_service, db)
        
        # 检查AI管理器状态
        status = ai_manager.get_status()
        print(f"AI管理器状态: {status}")
        
        # 测试标签建议功能
        print("\n--- 测试标签建议功能 ---")
        test_file = {
            'name': 'test.txt',
            'path': '/test/test.txt',
            'size': 1024
        }
        
        suggestions = ai_manager.suggest_tags(test_file)
        print(f"标签建议: {suggestions}")
        
        # 检查模型文件路径
        print("\n--- 检查模型文件 ---")
        ai_config = config.get('ai', {})
        ml_config = ai_config.get('features', {}).get('ml_basic', {})
        model_path = ml_config.get('model_path', '')
        print(f"配置中的模型路径: {model_path}")
        
        if model_path:
            full_model_path = os.path.join(new_lib_path, 'ai_models', model_path)
            print(f"完整模型路径: {full_model_path}")
            print(f"模型文件存在: {os.path.exists(full_model_path)}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ai_manager_in_new_lib()