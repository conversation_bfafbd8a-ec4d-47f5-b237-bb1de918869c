#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查旧库的AI配置
"""

import sys
import os

# 添加项目路径
sys.path.append('d:/PythonProjects2/SmartVault3')

from smartvault.services.library_config_service import LibraryConfigService

def check_old_library_ai():
    """检查旧库的AI配置"""
    print("=== 检查旧库的AI配置 ===")
    
    old_lib_path = 'D:/temp4/SmartVault_Lib'
    print(f"旧库路径: {old_lib_path}")
    
    try:
        service = LibraryConfigService()
        config = service.load_library_config(old_lib_path)
        
        ai_config = config.get('ai', {})
        ml_config = ai_config.get('features', {}).get('ml_basic', {})
        
        print("\n--- AI配置信息 ---")
        print(f"AI启用: {ai_config.get('enabled', False)}")
        print(f"ML启用: {ml_config.get('enabled', False)}")
        print(f"模型路径配置: {ml_config.get('model_path', '')}")
        
        # 检查模型文件
        model_path = ml_config.get('model_path', '')
        if model_path:
            if os.path.isabs(model_path):
                full_path = model_path
                print(f"绝对路径模型: {full_path}")
            else:
                full_path = os.path.join(old_lib_path, 'ai_models', model_path)
                print(f"相对路径模型: {model_path}")
                print(f"完整模型路径: {full_path}")
            
            print(f"模型文件存在: {os.path.exists(full_path)}")
        else:
            print("未配置模型路径")
        
        # 检查ai_models目录
        ai_models_dir = os.path.join(old_lib_path, 'ai_models')
        print(f"\nai_models目录存在: {os.path.exists(ai_models_dir)}")
        if os.path.exists(ai_models_dir):
            files = os.listdir(ai_models_dir)
            print(f"ai_models目录内容: {files}")
        
    except Exception as e:
        print(f"检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_old_library_ai()