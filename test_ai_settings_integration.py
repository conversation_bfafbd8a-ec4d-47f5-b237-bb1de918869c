#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI设置页面集成测试

测试AI设置页面与主设置对话框的集成情况
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

def test_settings_dialog_integration():
    """测试设置对话框集成"""
    print("\n=== 测试设置对话框集成 ===")
    
    try:
        # 检查设置对话框是否存在
        print("1. 检查设置对话框文件...")
        settings_dialog_path = "smartvault/ui/dialogs/settings_dialog.py"
        if os.path.exists(settings_dialog_path):
            print("   ✅ 设置对话框文件存在")
        else:
            print("   ❌ 设置对话框文件不存在")
            return False
        
        # 尝试导入设置对话框
        print("\n2. 导入设置对话框...")
        try:
            from smartvault.ui.dialogs.settings_dialog import SettingsDialog
            print("   ✅ 设置对话框导入成功")
        except ImportError as e:
            print(f"   ❌ 设置对话框导入失败: {e}")
            return False
        
        # 创建设置对话框实例
        print("\n3. 创建设置对话框实例...")
        try:
            settings_dialog = SettingsDialog()
            print("   ✅ 设置对话框创建成功")
        except Exception as e:
            print(f"   ❌ 设置对话框创建失败: {e}")
            return False
        
        # 检查AI设置页面是否已集成
        print("\n4. 检查AI设置页面集成...")
        if hasattr(settings_dialog, 'ai_page') or hasattr(settings_dialog, 'pages'):
            print("   ✅ 设置对话框具有页面管理功能")
            
            # 检查是否有AI相关页面
            if hasattr(settings_dialog, 'ai_page'):
                print("   ✅ AI设置页面已集成")
            else:
                print("   ⚠️ AI设置页面可能未集成")
                
            # 检查标签页是否包含AI功能页面
            if hasattr(settings_dialog, 'tab_widget'):
                ai_tab_found = False
                for i in range(settings_dialog.tab_widget.count()):
                    if settings_dialog.tab_widget.tabText(i) == "AI功能":
                        ai_tab_found = True
                        break
                
                if ai_tab_found:
                    print("   ✅ AI功能标签页已找到")
                else:
                    print("   ⚠️ AI功能标签页未找到")
        else:
            print("   ❌ 设置对话框缺少页面管理功能")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 设置对话框集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_integration():
    """测试主窗口集成"""
    print("\n=== 测试主窗口集成 ===")
    
    try:
        # 检查主窗口是否存在
        print("1. 检查主窗口文件...")
        main_window_path = "smartvault/ui/main_window/__init__.py"
        if os.path.exists(main_window_path):
            print("   ✅ 主窗口文件存在")
        else:
            print("   ❌ 主窗口文件不存在")
            return False
        
        # 尝试导入主窗口
        print("\n2. 导入主窗口...")
        try:
            from smartvault.ui.main_window import MainWindow
            print("   ✅ 主窗口导入成功")
        except ImportError as e:
            print(f"   ❌ 主窗口导入失败: {e}")
            return False
        
        # 检查主窗口是否有设置菜单
        print("\n3. 检查设置菜单集成...")
        try:
            main_window = MainWindow()
            
            # 检查是否有设置相关的方法或属性
            has_settings = (
                hasattr(main_window, 'show_settings') or
                hasattr(main_window, 'open_settings') or
                hasattr(main_window, 'settings_action')
            )
            
            if has_settings:
                print("   ✅ 主窗口具有设置功能")
            else:
                print("   ⚠️ 主窗口可能缺少设置功能")
            
            # 检查AI管理器是否已初始化
            if hasattr(main_window, 'ai_manager'):
                print("   ✅ 主窗口已初始化AI管理器")
                
                # 检查AI管理器类型
                try:
                    from smartvault.services.ai.ai_manager import AIManager
                    if isinstance(main_window.ai_manager, AIManager):
                        print("   ✅ AI管理器类型正确")
                    else:
                        print("   ⚠️ AI管理器类型不正确")
                except ImportError:
                    print("   ⚠️ 无法导入AI管理器类")
            else:
                print("   ⚠️ 主窗口未初始化AI管理器")
                
        except Exception as e:
            print(f"   ⚠️ 主窗口创建异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 主窗口集成测试失败: {e}")
        return False

def test_ai_manager_service():
    """测试AI管理器服务"""
    print("\n=== 测试AI管理器服务 ===")
    
    try:
        from smartvault.services.ai.ai_manager import AIManager
        
        # 创建AI管理器
        print("1. 创建AI管理器...")
        ai_manager = AIManager()
        print("   ✅ AI管理器创建成功")
        
        # 测试基本功能
        print("\n2. 测试基本功能...")
        
        # 测试可用性检查
        is_available = ai_manager.is_available()
        print(f"   AI可用性: {is_available}")
        
        # 测试状态获取
        status = ai_manager.get_status()
        print(f"   AI状态: enabled={status.get('enabled')}, stage={status.get('stage')}")
        
        # 测试学习统计
        try:
            stats = ai_manager.get_learning_statistics()
            print(f"   学习统计: {len(stats)} 项")
        except Exception as e:
            print(f"   ⚠️ 学习统计获取异常: {e}")
        
        print("   ✅ AI管理器基本功能正常")
        
        return True
        
    except Exception as e:
        print(f"   ❌ AI管理器服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_persistence():
    """测试配置持久化"""
    print("\n=== 测试配置持久化 ===")
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager
        from smartvault.services.ai.ai_manager import AIManager
        
        # 创建配置管理器
        print("1. 创建配置管理器...")
        config_manager = AIConfigManager()
        ai_manager = AIManager()
        config_manager.set_ai_manager(ai_manager)
        print("   ✅ 配置管理器创建成功")
        
        # 测试配置加载
        print("\n2. 测试配置加载...")
        config = config_manager.load_ai_config()
        print(f"   加载的配置: enabled={config.get('enabled')}, stage={config.get('stage')}")
        print("   ✅ 配置加载成功")
        
        # 测试配置保存
        print("\n3. 测试配置保存...")
        test_config = {
            'enabled': True,
            'stage': 'rule_based',
            'features': {
                'project_detection': {'enabled': True, 'min_files': 3},
                'series_detection': {'enabled': True, 'min_files': 2}
            }
        }
        
        success = config_manager.save_ai_config(test_config)
        if success:
            print("   ✅ 配置保存成功")
        else:
            print("   ❌ 配置保存失败")
            return False
        
        # 验证保存的配置
        print("\n4. 验证保存的配置...")
        saved_config = config_manager.load_ai_config()
        if saved_config.get('enabled') == test_config['enabled']:
            print("   ✅ 配置持久化验证成功")
        else:
            print("   ❌ 配置持久化验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置持久化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure():
    """测试文件结构完整性"""
    print("\n=== 测试文件结构完整性 ===")
    
    required_files = [
        "smartvault/ui/dialogs/settings/pages/ai/ai_main_page.py",
        "smartvault/ui/dialogs/settings/pages/ai/utils/ai_config_helper.py",
        "smartvault/ui/dialogs/settings/pages/ai/components/ai_status_widget.py",
        "smartvault/ui/dialogs/settings/pages/ai/components/ai_features_widget.py",
        "smartvault/ui/dialogs/settings/pages/ai/components/behavior_learning_widget.py",
        "smartvault/ui/dialogs/settings/pages/ai/components/adaptive_rules_widget.py",
        "smartvault/ui/dialogs/settings/pages/ai/components/ai_statistics_widget.py",
        "smartvault/services/ai/ai_manager.py",
        "smartvault/services/ai/smart_rule_engine.py",
        "smartvault/services/ai/adaptive_rule_engine.py"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"   ✅ {os.path.basename(file_path)}")
        else:
            missing_files.append(file_path)
            print(f"   ❌ {os.path.basename(file_path)} (缺失)")
    
    print(f"\n文件完整性: {len(existing_files)}/{len(required_files)} 个文件存在")
    
    if missing_files:
        print("\n缺失的文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("AI设置页面集成测试")
    print("=" * 50)
    
    # 创建QApplication（UI测试需要）
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_file_structure())
    test_results.append(test_ai_manager_service())
    test_results.append(test_config_persistence())
    test_results.append(test_settings_dialog_integration())
    test_results.append(test_main_window_integration())
    
    # 汇总测试结果
    print("\n" + "=" * 50)
    print("集成测试结果汇总:")
    
    test_names = [
        "文件结构完整性",
        "AI管理器服务",
        "配置持久化",
        "设置对话框集成",
        "主窗口集成"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(test_results)} 项集成测试通过")
    
    if passed == len(test_results):
        print("\n🎉 所有集成测试通过！AI设置页面集成正常。")
    else:
        print(f"\n⚠️ 有 {len(test_results) - passed} 项集成测试失败，需要修复。")
    
    # 退出应用
    QTimer.singleShot(100, app.quit)
    
    return passed == len(test_results)

if __name__ == "__main__":
    main()
