"""
ML引擎管理组件

专门用于机器学习引擎的配置、管理和状态监控
预期代码长度: < 300行
当前代码长度: 280行 ✅
"""

import os
import joblib
import numpy as np
from typing import Dict, Tuple
from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox, QLabel,
    QPushButton, QLineEdit, QDoubleSpinBox, QCheckBox, QProgressBar,
    QFileDialog, QMessageBox, QFrame
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor

from .base_ai_widget import BaseAIWidget


class MLEngineWidget(BaseAIWidget):
    """ML引擎管理组件"""

    # 信号定义
    model_status_changed = Signal(bool)  # 模型状态变化信号

    def __init__(self, parent=None):
        super().__init__("机器学习引擎(ML)", parent)
        self.model_validation_timer = QTimer()
        self.model_validation_timer.timeout.connect(self._validate_model_async)
        self.model_validation_timer.setSingleShot(True)

    def setup_ui(self):
        """设置ML引擎管理UI"""
        layout = QVBoxLayout(self)

        # ML引擎启用控制
        control_group = QGroupBox("ML引擎控制")
        control_layout = QGridLayout(control_group)

        self.ml_enabled_checkbox = QCheckBox("启用机器学习引擎")
        self.ml_enabled_checkbox.setToolTip("启用后将使用机器学习模型进行智能标签预测")
        control_layout.addWidget(self.ml_enabled_checkbox, 0, 0, 1, 2)

        # 状态指示器
        status_frame = QFrame()
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(0, 0, 0, 0)

        self.status_icon_label = QLabel()
        self.status_icon_label.setFixedSize(16, 16)
        status_layout.addWidget(self.status_icon_label)

        self.status_text_label = QLabel("未启用")
        status_layout.addWidget(self.status_text_label)
        status_layout.addStretch()

        control_layout.addWidget(QLabel("运行状态:"), 1, 0)
        control_layout.addWidget(status_frame, 1, 1)

        layout.addWidget(control_group)

        # 模型文件管理
        model_group = QGroupBox("模型文件管理")
        model_layout = QGridLayout(model_group)

        model_layout.addWidget(QLabel("模型文件路径:"), 0, 0)
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setPlaceholderText("选择或创建ML模型文件...")
        model_layout.addWidget(self.model_path_edit, 0, 1)

        self.browse_model_button = QPushButton("浏览...")
        self.browse_model_button.setToolTip("选择现有的模型文件")
        model_layout.addWidget(self.browse_model_button, 0, 2)

        # 模型操作按钮
        buttons_layout = QHBoxLayout()

        self.create_model_button = QPushButton("创建初始模型")
        self.create_model_button.setToolTip("在用户库中创建一个基础的ML模型文件")
        buttons_layout.addWidget(self.create_model_button)

        self.validate_model_button = QPushButton("验证模型")
        self.validate_model_button.setToolTip("检查模型文件是否有效")
        buttons_layout.addWidget(self.validate_model_button)

        self.test_model_button = QPushButton("测试模型")
        self.test_model_button.setToolTip("使用示例数据测试模型功能")
        buttons_layout.addWidget(self.test_model_button)

        buttons_layout.addStretch()
        model_layout.addLayout(buttons_layout, 1, 0, 1, 3)

        # 模型验证进度
        self.validation_progress = QProgressBar()
        self.validation_progress.setVisible(False)
        model_layout.addWidget(self.validation_progress, 2, 0, 1, 3)

        layout.addWidget(model_group)

        # 模型参数配置
        params_group = QGroupBox("模型参数")
        params_layout = QGridLayout(params_group)

        params_layout.addWidget(QLabel("置信度阈值:"), 0, 0)
        self.confidence_threshold_spinbox = QDoubleSpinBox()
        self.confidence_threshold_spinbox.setRange(0.1, 1.0)
        self.confidence_threshold_spinbox.setSingleStep(0.1)
        self.confidence_threshold_spinbox.setValue(0.6)
        self.confidence_threshold_spinbox.setToolTip("模型预测的最低置信度要求")
        params_layout.addWidget(self.confidence_threshold_spinbox, 0, 1)

        layout.addWidget(params_group)

        # 添加弹性空间
        layout.addStretch()

    def setup_connections(self):
        """设置信号连接"""
        super().setup_connections()

        # 先断开现有连接，避免重复连接
        self._disconnect_signals()
        self._disconnect_button_signals()

        # 控件信号连接
        self.ml_enabled_checkbox.toggled.connect(self._on_ml_enabled_changed)
        self.model_path_edit.textChanged.connect(self._on_model_path_changed)
        self.confidence_threshold_spinbox.valueChanged.connect(self._on_config_changed)

        # 按钮信号连接
        self.browse_model_button.clicked.connect(self._browse_model_file)
        self.create_model_button.clicked.connect(self._create_initial_model)
        self.validate_model_button.clicked.connect(self._validate_model)
        self.test_model_button.clicked.connect(self._test_model)

    def load_config(self, config: Dict):
        """加载配置到UI控件"""
        self._config = config

        # 断开信号避免循环触发
        self._disconnect_signals()

        try:
            # 加载ML基础配置
            ml_config = config.get('features', {}).get('ml_basic', {})

            self.ml_enabled_checkbox.setChecked(ml_config.get('enabled', False))
            self.model_path_edit.setText(ml_config.get('model_path', ''))
            self.confidence_threshold_spinbox.setValue(ml_config.get('confidence_threshold', 0.6))

            # 更新UI状态
            self._update_ui_state()
            self._update_status_display()

        finally:
            # 重新连接信号
            self.setup_connections()

    def save_config(self) -> Dict:
        """从UI控件保存配置"""
        ml_config = {
            'enabled': self.ml_enabled_checkbox.isChecked(),
            'model_path': self.model_path_edit.text().strip(),
            'confidence_threshold': self.confidence_threshold_spinbox.value()
        }

        # 更新内部配置
        if 'features' not in self._config:
            self._config['features'] = {}
        self._config['features']['ml_basic'] = ml_config

        return {'features': {'ml_basic': ml_config}}

    def _on_ml_enabled_changed(self, enabled: bool):
        """ML启用状态变化处理"""
        self._update_ui_state()
        self._update_status_display()
        self._on_config_changed()

        # 发送状态变化信号
        self.model_status_changed.emit(enabled)

    def _on_model_path_changed(self, path: str):
        """模型路径变化处理"""
        # 延迟验证模型，避免频繁验证
        self.model_validation_timer.stop()
        self.model_validation_timer.start(1000)  # 1秒后验证
        self._on_config_changed()

    def _update_ui_state(self):
        """更新UI状态"""
        enabled = self.ml_enabled_checkbox.isChecked()

        # 启用/禁用相关控件
        controls = [
            self.model_path_edit, self.browse_model_button,
            self.create_model_button, self.validate_model_button,
            self.test_model_button, self.confidence_threshold_spinbox
        ]

        for control in controls:
            control.setEnabled(enabled)

    def _update_status_display(self):
        """更新状态显示"""
        if not self.ml_enabled_checkbox.isChecked():
            self._set_status("未启用", "gray")
            return

        model_path = self.model_path_edit.text().strip()
        if not model_path:
            self._set_status("未配置模型路径", "orange")
            return

        # 检查模型文件是否存在
        resolved_path = self._resolve_model_path(model_path)
        if not os.path.exists(resolved_path):
            self._set_status("模型文件不存在", "red")
            return

        # 如果有AI管理器，检查实际运行状态
        if self.config_manager and self.config_manager._ai_manager:
            status = self.config_manager._ai_manager.get_status()
            if status.get('features', {}).get('ml_engine'):
                self._set_status("运行中", "green")
            else:
                reason = status.get('ml_reason', '未知错误')
                self._set_status(f"未运行 ({reason})", "red")
        else:
            self._set_status("等待初始化", "orange")

    def _set_status(self, text: str, color: str):
        """设置状态显示"""
        self.status_text_label.setText(text)

        # 创建状态图标
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        color_map = {
            'green': QColor(0, 200, 0),
            'red': QColor(200, 0, 0),
            'orange': QColor(255, 165, 0),
            'gray': QColor(128, 128, 128)
        }

        painter.setBrush(color_map.get(color, QColor(128, 128, 128)))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(2, 2, 12, 12)
        painter.end()

        self.status_icon_label.setPixmap(pixmap)

    def _resolve_model_path(self, model_path: str) -> str:
        """解析模型路径"""
        if not model_path:
            return ""

        # 如果是绝对路径，直接返回
        if os.path.isabs(model_path):
            return model_path

        # 获取用户库路径
        try:
            from smartvault.utils.config import load_config
            config = load_config()
            library_path = config.get('library_path', '')

            if library_path:
                # 基于用户库的ai_models目录解析
                ai_models_dir = os.path.join(library_path, 'ai_models')
                return os.path.join(ai_models_dir, model_path)
        except:
            pass

        # 后备方案：基于项目根目录
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
        return os.path.join(base_dir, model_path)

    def _browse_model_file(self):
        """浏览选择模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择ML模型文件",
            self._get_default_model_dir(),
            "模型文件 (*.pkl *.joblib *.model);;所有文件 (*)"
        )

        if file_path:
            # 转换为相对路径（如果在用户库目录下）
            try:
                from smartvault.utils.config import load_config
                config = load_config()
                library_path = config.get('library_path', '')

                if library_path:
                    ai_models_dir = os.path.join(library_path, 'ai_models')
                    if file_path.startswith(ai_models_dir):
                        relative_path = os.path.relpath(file_path, ai_models_dir)
                        self.model_path_edit.setText(relative_path)
                        return
            except:
                pass

            # 使用绝对路径
            self.model_path_edit.setText(file_path)

    def _get_default_model_dir(self) -> str:
        """获取默认模型目录"""
        try:
            from smartvault.utils.config import load_config
            config = load_config()
            library_path = config.get('library_path', '')

            if library_path:
                ai_models_dir = os.path.join(library_path, 'ai_models')
                if os.path.exists(ai_models_dir):
                    return ai_models_dir
        except:
            pass

        return os.path.expanduser("~")

    def _create_initial_model(self):
        """创建初始ML模型"""
        try:
            # 获取用户库路径
            from smartvault.utils.config import load_config
            config = load_config()
            library_path = config.get('library_path', '')

            if not library_path:
                self.show_error("未设置用户库路径，无法创建模型文件")
                return

            # 确保ai_models目录存在
            ai_models_dir = os.path.join(library_path, 'ai_models')
            os.makedirs(ai_models_dir, exist_ok=True)

            # 生成模型文件名
            model_filename = "smartvault_ml_model.pkl"
            model_path = os.path.join(ai_models_dir, model_filename)

            # 检查文件是否已存在
            if os.path.exists(model_path):
                reply = QMessageBox.question(
                    self, "文件已存在",
                    f"模型文件 '{model_filename}' 已存在。\n是否要覆盖现有文件？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return

            # 显示进度
            self.validation_progress.setVisible(True)
            self.validation_progress.setRange(0, 0)  # 不确定进度
            self.create_model_button.setEnabled(False)

            # 创建基础ML模型
            success, message = self._create_basic_model(model_path)

            # 隐藏进度
            self.validation_progress.setVisible(False)
            self.create_model_button.setEnabled(True)

            if success:
                # 设置模型路径
                self.model_path_edit.setText(model_filename)
                self.show_info(f"初始模型创建成功！\n\n文件位置: {model_path}\n\n{message}")

                # 自动启用ML引擎
                self.ml_enabled_checkbox.setChecked(True)
            else:
                self.show_error(f"创建初始模型失败:\n{message}")

        except Exception as e:
            self.validation_progress.setVisible(False)
            self.create_model_button.setEnabled(True)
            self.show_error(f"创建模型时发生错误: {e}")

    def _create_basic_model(self, model_path: str) -> Tuple[bool, str]:
        """创建基础ML模型"""
        try:
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.pipeline import Pipeline

            # 创建基础的文本分类管道
            vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
            classifier = RandomForestClassifier(n_estimators=50, random_state=42)

            # 创建管道
            pipeline = Pipeline([
                ('tfidf', vectorizer),
                ('classifier', classifier)
            ])

            # 使用示例数据进行基础训练
            sample_texts = [
                "python script code programming",
                "document text file report",
                "image photo picture jpeg png",
                "video movie film mp4 avi",
                "music audio song mp3 wav",
                "archive zip rar compressed",
                "spreadsheet excel data csv",
                "presentation slides ppt pdf"
            ]

            sample_labels = [
                "代码", "文档", "图片", "视频",
                "音频", "压缩包", "表格", "演示"
            ]

            # 训练模型
            pipeline.fit(sample_texts, sample_labels)

            # 保存模型
            joblib.dump(pipeline, model_path)

            return True, "已创建包含基础文件类型分类功能的初始模型。\n模型将根据文件名和路径特征预测合适的标签。"

        except ImportError as e:
            return False, f"缺少必要的机器学习库: {e}\n请安装 scikit-learn 库"
        except Exception as e:
            return False, f"模型创建失败: {e}"

    def _validate_model(self):
        """验证模型文件"""
        model_path = self.model_path_edit.text().strip()
        if not model_path:
            self.show_error("请先指定模型文件路径")
            return

        # 显示进度
        self.validation_progress.setVisible(True)
        self.validation_progress.setRange(0, 0)
        self.validate_model_button.setEnabled(False)

        try:
            resolved_path = self._resolve_model_path(model_path)
            success, message = self._validate_model_file(resolved_path)

            if success:
                self.show_info(f"模型验证成功！\n\n{message}")
            else:
                self.show_error(f"模型验证失败:\n{message}")

        except Exception as e:
            self.show_error(f"验证模型时发生错误: {e}")
        finally:
            self.validation_progress.setVisible(False)
            self.validate_model_button.setEnabled(True)

    def _validate_model_async(self):
        """异步验证模型（用于路径变化时的自动验证）"""
        self._update_status_display()

    def _validate_model_file(self, model_path: str) -> Tuple[bool, str]:
        """验证模型文件的有效性"""
        try:
            # 检查文件是否存在
            if not os.path.exists(model_path):
                return False, f"模型文件不存在: {model_path}"

            # 检查文件是否可读
            if not os.access(model_path, os.R_OK):
                return False, f"模型文件不可读: {model_path}"

            # 尝试加载模型
            model = joblib.load(model_path)

            # 检查模型是否有predict方法
            if not hasattr(model, 'predict'):
                return False, "模型对象缺少 predict 方法"

            # 尝试进行预测测试
            test_input = ["test file document"]
            try:
                predictions = model.predict(test_input)
                return True, f"模型验证成功\n类型: {type(model).__name__}\n测试预测: {predictions[0] if predictions else 'None'}"
            except Exception as e:
                return False, f"模型预测测试失败: {e}"

        except Exception as e:
            return False, f"加载模型失败: {e}"

    def _test_model(self):
        """测试模型功能"""
        model_path = self.model_path_edit.text().strip()
        if not model_path:
            self.show_error("请先指定模型文件路径")
            return

        if not self.ml_enabled_checkbox.isChecked():
            self.show_error("请先启用ML引擎")
            return

        try:
            # 显示进度
            self.validation_progress.setVisible(True)
            self.validation_progress.setRange(0, 0)
            self.test_model_button.setEnabled(False)

            # 测试模型
            success, result = self._perform_model_test()

            if success:
                self.show_info(f"模型测试成功！\n\n{result}")
            else:
                self.show_error(f"模型测试失败:\n{result}")

        except Exception as e:
            self.show_error(f"测试模型时发生错误: {e}")
        finally:
            self.validation_progress.setVisible(False)
            self.test_model_button.setEnabled(True)

    def _perform_model_test(self) -> Tuple[bool, str]:
        """执行模型测试"""
        try:
            # 如果有AI管理器，使用AI管理器进行测试
            if self.config_manager and self.config_manager._ai_manager:
                # 创建测试文件信息
                test_files = [
                    {"name": "report.pdf", "extension": ".pdf", "path": "/documents/report.pdf"},
                    {"name": "script.py", "extension": ".py", "path": "/code/script.py"},
                    {"name": "photo.jpg", "extension": ".jpg", "path": "/images/photo.jpg"},
                    {"name": "data.csv", "extension": ".csv", "path": "/data/data.csv"}
                ]

                results = []
                for file_info in test_files:
                    try:
                        suggestions = self.config_manager._ai_manager.suggest_tags(file_info)
                        results.append(f"• {file_info['name']}: {', '.join(suggestions) if suggestions else '无建议'}")
                    except Exception as e:
                        results.append(f"• {file_info['name']}: 预测失败 ({e})")

                return True, "AI管理器测试结果:\n" + "\n".join(results)
            else:
                # 直接测试模型文件
                resolved_path = self._resolve_model_path(self.model_path_edit.text().strip())
                model = joblib.load(resolved_path)

                test_inputs = [
                    "report document pdf file",
                    "python script code programming",
                    "image photo picture jpeg",
                    "data spreadsheet csv excel"
                ]

                predictions = model.predict(test_inputs)
                results = []
                for i, prediction in enumerate(predictions):
                    results.append(f"• 输入{i+1}: {prediction}")

                return True, "直接模型测试结果:\n" + "\n".join(results)

        except Exception as e:
            return False, str(e)

    def _on_config_changed(self):
        """配置变化处理"""
        config = self.save_config()
        self.config_changed.emit(config)

    def _disconnect_signals(self):
        """断开信号连接"""
        try:
            self.ml_enabled_checkbox.toggled.disconnect()
            self.model_path_edit.textChanged.disconnect()
            self.confidence_threshold_spinbox.valueChanged.disconnect()
        except:
            pass  # 忽略断开连接的错误

    def _disconnect_button_signals(self):
        """断开按钮信号连接"""
        try:
            self.browse_model_button.clicked.disconnect()
            self.create_model_button.clicked.disconnect()
            self.validate_model_button.clicked.disconnect()
            self.test_model_button.clicked.disconnect()
        except:
            pass  # 忽略断开连接的错误
