"""
主窗口核心框架
"""

import os
from datetime import datetime
from PySide6.QtWidgets import Q<PERSON>ainW<PERSON>ow, QWidget, QVBoxLayout, QSplitter, QMessageBox, QApplication
from PySide6.QtCore import Qt, QSettings

from smartvault.ui.widgets import NavigationPanel
from smartvault.ui.views.file_table_view import FileTableViewContainer
from smartvault.services.file import FileService
from smartvault.utils.tree_state_manager import tree_state_manager


class MainWindowCore(QMainWindow):
    """主窗口核心框架类"""

    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        self.setWindowTitle("SmartVault")

        # 启用拖拽功能
        self.setAcceptDrops(True)

        # 初始化服务
        self.file_service = FileService()
        from smartvault.services.tag_service import TagService
        self.tag_service = TagService()
        from smartvault.services.auto_tag_service import AutoTagService
        self.auto_tag_service = AutoTagService()
        from smartvault.services.file_monitor_service import FileMonitorService
        self.monitor_service = FileMonitorService()
        from smartvault.services.clipboard_monitor_service import ClipboardMonitorService
        self.clipboard_service = ClipboardMonitorService()

        # 初始化备份服务
        from smartvault.services.backup_service import get_backup_service
        self.backup_service = get_backup_service()

        # 初始化文件库配置服务
        from smartvault.services.library_config_service import get_library_config_service
        self.library_config_service = get_library_config_service()

        # 初始化剪贴板处理器（需要在信号连接前初始化）
        from .clipboard_handler import ClipboardHandler
        self.clipboard_handler = ClipboardHandler(self)

        # 初始化监控处理器（需要在使用前初始化）
        from .monitor_handler import MonitorHandler
        self.monitor_handler = MonitorHandler(self)

        # 初始化AI管理器
        from smartvault.services.ai.ai_manager import AIManager
        self.ai_manager = AIManager()
        
        # 初始化AI集成处理器
        from .ai_integration_handler import AIIntegrationHandler
        self.ai_integration_handler = AIIntegrationHandler(self)

        # 设置监控事件回调
        self.monitor_service.event_callback = self.monitor_handler.on_monitor_event
        self.clipboard_service.duplicate_found.connect(self.clipboard_handler.on_clipboard_duplicate_found)

        # 设置监控服务的文件服务依赖注入
        self.monitor_service.set_file_service(self.file_service)

        # 连接文件服务的重复文件建议信号
        self.file_service.duplicate_suggestion.connect(self.monitor_handler.on_duplicate_file_suggestion)
        self.file_service.batch_duplicate_processed.connect(self.monitor_handler.on_batch_duplicate_processed)

        # 初始化文件夹筛选状态
        self.current_folder_filter = {'type': None, 'value': None}

        # 初始化剪贴板浮动窗
        from smartvault.ui.widgets.clipboard_floating_widget import ClipboardFloatingWidget
        self.clipboard_floating_widget = ClipboardFloatingWidget()
        self.clipboard_floating_widget.open_file_requested.connect(self.clipboard_handler.on_clipboard_open_file)

        # 初始化UI
        self.init_ui()

        # 初始化菜单和工具栏
        from .menu import MenuManager
        from .toolbar import ToolbarManager
        self.menu_manager = MenuManager(self)
        self.toolbar_manager = ToolbarManager(self)

        # 初始化备份管理器
        from .backup_manager import BackupManager
        self.backup_manager = BackupManager(self)



        # 恢复窗口大小和位置
        self.restore_window_geometry()

        # 应用用户配置
        self.apply_user_config()

        # 加载初始数据（立即执行，用户需要看到文件）
        self.load_initial_data()

        # 延后执行非关键启动任务，提高启动速度
        from PySide6.QtCore import QTimer
        QTimer.singleShot(1000, self._delayed_startup_tasks)

    def _delayed_startup_tasks(self):
        """延后执行的启动任务，提高启动速度"""
        try:
            print("开始执行延后启动任务...")

            # 启动已配置的监控
            self.monitor_handler.start_configured_monitors()

            # 启动剪贴板监控（如果配置启用）
            self.clipboard_handler.start_clipboard_monitor_if_enabled()

            # 设置浮动窗口模式
            self.clipboard_handler.setup_clipboard_float_mode()

            # 启动自动备份服务
            self.backup_manager.start_backup_service()

            # 初始化状态栏备份状态显示
            self.backup_manager.setup_backup_status_display()

            # 初始化自动标签服务配置
            self._initialize_auto_tag_service()

            # 初始化AI管理器（延后执行以提高启动速度）
            self.ai_integration_handler.initialize_ai_manager()

            print("延后启动任务完成")

        except Exception as e:
            print(f"延后启动任务失败: {e}")
            import traceback
            traceback.print_exc()

    def _initialize_auto_tag_service(self):
        """初始化自动标签服务"""
        try:
            print("开始初始化自动标签服务...")

            # 加载配置
            from smartvault.utils.config import load_config
            config = load_config()

            # 加载自动标签规则
            self.auto_tag_service.load_rules_from_config(config)

            print("✅ 自动标签服务初始化成功")

        except Exception as e:
            print(f"自动标签服务初始化失败: {e}")
            import traceback
            traceback.print_exc()



    def init_ui(self):
        """初始化UI组件"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 创建分割器
        self.splitter = QSplitter(Qt.Horizontal)

        # 创建导航面板
        self.navigation_panel = NavigationPanel()
        self.navigation_panel.folder_selected.connect(self.on_folder_selected)
        self.navigation_panel.tag_selected.connect(self.on_tag_selected)
        self.navigation_panel.files_dropped_to_folder.connect(self.on_files_dropped_to_folder)
        self.splitter.addWidget(self.navigation_panel)

        # 创建文件视图
        self.file_view = FileTableViewContainer()
        self.file_view.file_double_clicked.connect(self.on_file_activated)
        self.file_view.file_clicked.connect(self.on_file_selected)
        self.splitter.addWidget(self.file_view)

        # 创建智能标签建议面板
        from smartvault.ui.widgets.ai_tag_suggestion_panel import AITagSuggestionPanel
        self.ai_tag_panel = AITagSuggestionPanel(
            ai_manager=self.ai_manager,
            db=self.file_service.db if hasattr(self.file_service, 'db') else None
        )
        self.ai_tag_panel.tag_accepted.connect(self.ai_integration_handler.on_ai_tag_accepted)
        self.ai_tag_panel.tag_rejected.connect(self.ai_integration_handler.on_ai_tag_rejected)
        self.splitter.addWidget(self.ai_tag_panel)

        # 初始化选择状态
        self.selected_file_count = 0
        self.current_selected_file_id = None

        # 设置分割器比例 (导航:文件视图:AI面板 = 200:600:200)
        self.splitter.setSizes([200, 600, 200])

        # 添加分割器到主布局
        main_layout.addWidget(self.splitter)

        # 设置状态栏
        self.statusBar().showMessage("就绪 - 按F1键打开帮助")

        # 在状态栏添加帮助提示
        from PySide6.QtWidgets import QLabel
        help_hint_label = QLabel("💡 按F1键打开帮助")
        help_hint_label.setStyleSheet("color: #666; font-size: 11px;")
        self.statusBar().addPermanentWidget(help_hint_label)

        # 设置数据加载回调（在UI初始化完成后）
        if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
            self.file_view.table_view.model.set_data_loader_callback(self._load_files_with_pagination)
            self.file_view.table_view.model.set_search_total_count_callback(self._get_search_total_count)
            # 设置标签服务
            self.file_view.table_view.model.set_tag_service(self.tag_service)

        # 设置网格视图的标签服务
        if hasattr(self.file_view, 'grid_view') and hasattr(self.file_view.grid_view, 'model'):
            self.file_view.grid_view.model.set_tag_service(self.tag_service)

    def show_status_message(self, message, is_success=True, timeout=3000):
        """显示状态栏消息

        Args:
            message: 消息内容
            is_success: 是否为成功消息
            timeout: 显示时长（毫秒）
        """
        self.statusBar().showMessage(message, timeout)
        if not is_success:
            print(f"错误: {message}")
        else:
            print(f"状态: {message}")

    def _show_important_status_message(self, message, is_success=True, timeout=8000):
        """显示重要的状态栏消息（不会被普通消息覆盖）

        Args:
            message: 消息内容
            is_success: 是否为成功消息
            timeout: 显示时长（毫秒）
        """
        # 设置重要消息标志
        self._important_message_active = True

        # 显示带样式的状态栏消息
        status_bar = self.statusBar()
        if is_success:
            status_bar.setStyleSheet("background-color: #dff0d8; color: #3c763d; font-weight: bold;")
        else:
            status_bar.setStyleSheet("background-color: #f2dede; color: #a94442; font-weight: bold;")

        status_bar.showMessage(message)

        # 设置定时器清除重要消息标志和样式
        from PySide6.QtCore import QTimer
        def reset_important_message():
            self._important_message_active = False
            status_bar.setStyleSheet("")

        QTimer.singleShot(timeout, reset_important_message)

        print(f"🎯 重要状态消息: {message}")

    def on_library_changed(self, new_library_path):
        """处理文件库切换事件

        Args:
            new_library_path: 新文件库路径
        """
        print(f"主窗口处理文件库切换: {new_library_path}")

        try:
            # 停止当前备份服务
            self.backup_manager.stop_backup_service()

            # 切换文件库配置服务
            self.library_config_service.switch_library(new_library_path)

            # 切换文件服务的数据库连接
            self.file_service.switch_library(new_library_path)

            # 重新初始化备份服务（使用新的文件库路径）
            from smartvault.services.backup_service import get_backup_service
            self.backup_service = get_backup_service()
            self.backup_manager.start_backup_service()

            # 清除模型的所有状态
            if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                model = self.file_view.table_view.model
                model.clear_all_state()  # 使用新的清除状态方法
                # 重新设置回调（确保新的数据库连接生效）
                model.set_data_loader_callback(self._load_files_with_pagination)
                model.set_search_total_count_callback(self._get_search_total_count)
                # 清除搜索条件
                if hasattr(self, 'search_widget'):
                    self.search_widget.clear_search()

            # 重新加载数据
            self.load_initial_data(show_message=False)

            # 显示成功消息
            self.show_status_message(f"已切换到文件库: {new_library_path}", True)

            # 显示重启建议提示
            from PySide6.QtWidgets import QMessageBox
            reply = QMessageBox.information(
                self, "文件库切换完成", 
                f"已成功切换到文件库: {new_library_path}\n\n"
                "💡 建议重新启动SmartVault以确保所有功能正常工作。\n"
                "这样可以避免切换库后可能出现的显示问题。\n\n"
                "是否现在重启程序？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # 重启程序
                import sys
                import subprocess
                import os
                
                # 获取当前脚本路径
                current_script = sys.argv[0]
                if current_script.endswith('.py'):
                    # 如果是Python脚本，使用Python解释器启动
                    subprocess.Popen([sys.executable, current_script])
                else:
                    # 如果是可执行文件，直接启动
                    subprocess.Popen([current_script])
                
                # 关闭当前程序
                self.close()
                return

            print(f"文件库切换完成: {new_library_path}")

        except Exception as e:
            print(f"文件库切换失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_status_message(f"文件库切换失败: {e}", False)



    def _load_files_with_pagination(self, limit=None, offset=0, search_keyword=None, search_column=None):
        """根据分页设置加载文件 - 按需加载版本

        Args:
            limit: 文件数量限制，如果为None则使用当前页面大小设置
            offset: 偏移量
            search_keyword: 搜索关键词
            search_column: 搜索列

        Returns:
            list: 文件列表
        """
        # 如果没有指定limit，使用当前页面大小设置
        if limit is None:
            if hasattr(self, 'file_view') and self.file_view:
                limit = self.file_view.get_current_page_size()
            else:
                limit = 100  # 默认值

        print(f"按需加载文件: limit={limit}, offset={offset}, search='{search_keyword}', column={search_column}")

        # 从数据库按需加载文件（包含文件夹筛选）
        files = self.file_service.get_files(
            limit=limit,
            offset=offset,
            search_keyword=search_keyword,
            search_column=search_column,
            folder_filter_type=self.current_folder_filter.get('type'),
            folder_filter_value=self.current_folder_filter.get('value')
        )

        print(f"实际加载了 {len(files)} 个文件")
        return files

    def _load_files_for_tag_filter(self, limit=100, offset=0, search_keyword=None, search_column=None):
        """为标签筛选加载文件 - 支持分页

        Args:
            limit: 文件数量限制
            offset: 偏移量
            search_keyword: 搜索关键词（暂不支持）
            search_column: 搜索列（暂不支持）

        Returns:
            list: 文件列表
        """
        if not hasattr(self, 'current_tag_filter') or not self.current_tag_filter:
            return []

        try:
            from smartvault.services.tag_service import TagService
            tag_service = TagService()

            print(f"标签筛选分页加载: limit={limit}, offset={offset}, tag_id={self.current_tag_filter}")

            # 获取标签筛选的文件（支持分页）
            files = tag_service.get_files_by_tag_hierarchy(
                self.current_tag_filter,
                limit=limit,
                offset=offset
            )

            print(f"标签筛选加载了 {len(files)} 个文件")
            return files

        except Exception as e:
            print(f"标签筛选分页加载失败: {e}")
            return []

    def _get_search_total_count(self, search_keyword, search_column):
        """获取搜索结果总数

        Args:
            search_keyword: 搜索关键词
            search_column: 搜索列

        Returns:
            int: 搜索结果总数
        """
        return self.file_service.get_file_count(
            search_keyword=search_keyword,
            search_column=search_column,
            folder_filter_type=self.current_folder_filter.get('type'),
            folder_filter_value=self.current_folder_filter.get('value')
        )

    def load_initial_data(self, show_message=True):
        """加载初始数据 - 按需加载版本

        Args:
            show_message: 是否显示状态栏消息
        """
        from PySide6.QtCore import QTimer

        def load_initial_batch():
            """加载初始批次（根据页面大小设置）"""
            try:
                print("开始加载初始数据...")

                # 获取总文件数（包含文件夹筛选）
                total_files = self.file_service.get_file_count(
                    folder_filter_type=self.current_folder_filter.get('type'),
                    folder_filter_value=self.current_folder_filter.get('value')
                )

                # 获取当前页面大小设置
                page_size = self.file_view.get_current_page_size()
                print(f"当前页面大小设置: {page_size}")

                # 根据页面大小设置加载文件（包含文件夹筛选）
                filter_type = self.current_folder_filter.get('type')

                if filter_type == "staging":
                    # 中转文件夹特殊处理：显示文件夹组 + 单独文件
                    display_items = self.file_service.get_staging_display_items()

                    # 应用分页
                    if page_size >= 999999:  # "全部"选项
                        files = display_items
                        print(f"加载中转文件夹所有项目: {len(files)} 个")
                    else:
                        files = display_items[:page_size]
                        print(f"加载中转文件夹前 {page_size} 个项目: {len(files)} 个")

                    # 更新总数为显示项目数
                    total_files = len(display_items)
                else:
                    # 普通文件筛选
                    if page_size >= 999999:  # "全部"选项
                        files = self.file_service.get_files(
                            folder_filter_type=filter_type,
                            folder_filter_value=self.current_folder_filter.get('value')
                        )  # 加载所有文件
                        print(f"加载所有文件: {len(files)} 个")
                    else:
                        files = self.file_service.get_files(
                            limit=page_size,
                            offset=0,
                            folder_filter_type=filter_type,
                            folder_filter_value=self.current_folder_filter.get('value')
                        )
                        print(f"加载前 {page_size} 个文件: {len(files)} 个")

                # 如果没有文件，添加一些示例文件
                if not files and total_files == 0:
                    self.add_sample_files()
                    total_files = self.file_service.get_file_count()
                    # 重新加载文件
                    if page_size >= 999999:
                        files = self.file_service.get_files()
                    else:
                        files = self.file_service.get_files(limit=page_size, offset=0)

                # 先设置总文件数到模型中
                if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                    self.file_view.table_view.model.set_total_files(total_files)

                # 设置文件列表（这会触发update_pagination_info，但现在total_files已经设置了）
                self.file_view.set_files(files)

                # 更新状态栏
                if page_size >= 999999:
                    status_msg = f"已加载所有 {len(files)} 个文件"
                else:
                    status_msg = f"已加载 {len(files)} 个文件 (共 {total_files} 个)"

                if hasattr(self, 'show_status_message'):
                    self.show_status_message(status_msg, True)
                else:
                    self.statusBar().showMessage(status_msg)

                print(f"初始数据加载完成，显示 {len(files)} 个文件，总共 {total_files} 个")
                if page_size < 999999:
                    print("提示：可以通过翻页或修改显示条数来查看更多文件")

            except Exception as e:
                print(f"加载初始数据失败: {e}")
                if hasattr(self, 'compact_progress_bar') and self.compact_progress_bar:
                    self.compact_progress_bar.finish_progress("加载数据失败", True, 2000)
                QMessageBox.critical(self, "加载数据失败", f"加载文件列表失败: {e}")
                self.statusBar().showMessage("加载数据失败")

        # 延迟500毫秒后加载初始数据
        QTimer.singleShot(500, load_initial_batch)



    def add_sample_files(self):
        """添加示例文件"""
        import os
        import datetime
        import uuid

        # 检查示例文件是否存在
        sample_file = os.path.join("test_sample", "test_file.txt")
        if not os.path.exists(sample_file):
            return

        # 添加示例文件
        try:
            # 获取文件信息
            file_stat = os.stat(sample_file)
            file_size = file_stat.st_size
            created_at = datetime.datetime.fromtimestamp(file_stat.st_ctime).isoformat()
            modified_at = datetime.datetime.fromtimestamp(file_stat.st_mtime).isoformat()
            added_at = datetime.datetime.now().isoformat()

            # 创建文件记录
            cursor = self.file_service.db.conn.cursor()

            # 添加第一个示例文件（链接模式）
            file_id1 = str(uuid.uuid4())
            cursor.execute(
                """
                INSERT INTO files (
                    id, name, original_path, library_path, size,
                    created_at, modified_at, added_at, entry_type, is_available
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    file_id1,
                    "test_file.txt",
                    os.path.abspath(sample_file),
                    None,
                    file_size,
                    created_at,
                    modified_at,
                    added_at,
                    "link",
                    1
                )
            )

            # 添加第二个示例文件（复制模式）
            file_id2 = str(uuid.uuid4())
            cursor.execute(
                """
                INSERT INTO files (
                    id, name, original_path, library_path, size,
                    created_at, modified_at, added_at, entry_type, is_available
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    file_id2,
                    "示例文档.txt",
                    os.path.abspath(sample_file),
                    os.path.abspath(sample_file),
                    file_size,
                    created_at,
                    modified_at,
                    added_at,
                    "copy",
                    1
                )
            )

            self.file_service.db.conn.commit()
        except Exception as e:
            print(f"添加示例文件失败: {e}")

    def _diagnose_filter_state(self, context=""):
        """诊断当前筛选状态"""
        print(f"🔍 状态诊断 [{context}]:")
        print(f"  - current_folder_filter: {getattr(self, 'current_folder_filter', 'None')}")
        print(f"  - current_tag_filter: {getattr(self, 'current_tag_filter', 'None')}")

        # 检查模型回调函数
        if hasattr(self, 'file_view') and self.file_view:
            if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                callback = getattr(self.file_view.table_view.model, 'data_loader_callback', None)
                if callback:
                    callback_name = callback.__name__ if hasattr(callback, '__name__') else str(callback)
                    print(f"  - table_view callback: {callback_name}")
                else:
                    print(f"  - table_view callback: None")

            if hasattr(self.file_view, 'grid_view') and hasattr(self.file_view.grid_view, 'model'):
                callback = getattr(self.file_view.grid_view.model, 'data_loader_callback', None)
                if callback:
                    callback_name = callback.__name__ if hasattr(callback, '__name__') else str(callback)
                    print(f"  - grid_view callback: {callback_name}")
                else:
                    print(f"  - grid_view callback: None")

    def _set_files_optimized(self, files):
        """优化的设置文件方法，避免重复数据加载

        Args:
            files: 文件列表
        """
        try:
            # 直接设置文件到模型，不触发applyFilter
            if hasattr(self, 'file_view') and self.file_view:
                # 获取当前活动视图
                current_view = self.file_view.get_current_view()

                # 直接设置文件到表格视图模型（不触发过滤器）
                if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                    model = self.file_view.table_view.model
                    model.beginResetModel()
                    model.files = files
                    model.filtered_files = files.copy()
                    model.visible_files = files.copy()
                    model.endResetModel()

                # 直接设置文件到网格视图模型（不触发过滤器）
                if hasattr(self.file_view, 'grid_view') and hasattr(self.file_view.grid_view, 'model'):
                    model = self.file_view.grid_view.model
                    model.beginResetModel()
                    model.files = files
                    model.filtered_files = files.copy()
                    model.visible_files = files.copy()
                    model.endResetModel()

                # 设置文件到详情视图
                if hasattr(self.file_view, 'details_view'):
                    self.file_view.details_view.set_files(files)

                print(f"✅ 优化设置完成，避免了重复加载")

        except Exception as e:
            print(f"❌ 优化设置失败，回退到普通方法: {e}")
            # 回退到普通方法
            self.file_view.set_files(files)

    def _clear_all_filters(self):
        """清除所有筛选状态"""
        print("🧹 清除所有筛选状态...")

        # 清除文件夹筛选状态
        if hasattr(self, 'current_folder_filter'):
            self.current_folder_filter = {'type': None, 'value': None}

        # 清除标签筛选状态
        if hasattr(self, 'current_tag_filter'):
            self.current_tag_filter = None

        # 🔧 清除缓存
        self._clear_caches()

        # 重置所有模型的回调函数为普通分页
        if hasattr(self, 'file_view') and self.file_view:
            if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                self.file_view.table_view.model.set_data_loader_callback(self._load_files_with_pagination)
                print("  - 已重置table_view回调函数")
            if hasattr(self.file_view, 'grid_view') and hasattr(self.file_view.grid_view, 'model'):
                self.file_view.grid_view.model.set_data_loader_callback(self._load_files_with_pagination)
                print("  - 已重置grid_view回调函数")

    def _clear_caches(self):
        """清除所有缓存"""
        if hasattr(self, '_staging_cache'):
            self._staging_cache = None
        if hasattr(self, '_tag_cache'):
            self._tag_cache.clear()
        print("  - 已清除所有缓存")

    def on_folder_selected(self, folder_id):
        """处理文件夹选中事件

        Args:
            folder_id: 文件夹ID
        """
        print(f"文件夹导航：选中文件夹 '{folder_id}'")

        # 诊断当前状态
        self._diagnose_filter_state(f"文件夹选中前: {folder_id}")

        # 根据文件夹ID应用相应的筛选
        try:
            if folder_id == "all":
                # 显示所有文件 - 确保清除所有筛选状态
                self._clear_all_filters()
                self._apply_folder_filter(None)
                self.statusBar().showMessage("显示所有文件")

            elif folder_id == "recent":
                # 显示最近7天添加的文件
                self._apply_folder_filter("recent")
                self.statusBar().showMessage("显示最近添加的文件")

            elif folder_id == "staging":
                # 中转文件夹模式
                self._apply_folder_filter("staging")
                self.statusBar().showMessage("显示中转文件夹中的文件")

            elif folder_id.startswith("type:"):
                # 按文件类型筛选
                file_type = folder_id.split(":")[1]
                self._apply_folder_filter("type", file_type)
                type_names = {
                    "document": "文档",
                    "image": "图片",
                    "audio": "音频",
                    "video": "视频",
                    "other": "其他"
                }
                self.statusBar().showMessage(f"显示{type_names.get(file_type, file_type)}文件")

            elif folder_id.startswith("entry:"):
                # 按入库方式筛选
                entry_type = folder_id.split(":")[1]
                self._apply_folder_filter("entry", entry_type)
                entry_names = {
                    "link": "链接",
                    "copy": "复制",
                    "move": "移动"
                }
                self.statusBar().showMessage(f"显示{entry_names.get(entry_type, entry_type)}文件")

            elif folder_id.startswith("time:"):
                # 按时间筛选
                time_range = folder_id.split(":")[1]
                self._apply_folder_filter("time", time_range)
                time_names = {
                    "today": "今天",
                    "week": "本周",
                    "month": "本月",
                    "older": "更早"
                }
                self.statusBar().showMessage(f"显示{time_names.get(time_range, time_range)}的文件")

            elif folder_id.startswith("tag:"):
                # 标签筛选（自定义文件夹和设备文件夹）
                tag_id = folder_id.split(":", 1)[1]

                # 🔧 优化：复用现有的标签服务实例，避免重复创建
                tag = self._get_tag_info_cached(tag_id)

                if tag:
                    # 根据标签类型显示不同的状态消息
                    if tag.get('name', '').startswith('💾'):
                        # 设备文件夹
                        device_name = tag['name'].replace('💾 ', '')
                        self.statusBar().showMessage(f"显示设备: {device_name} 的文件")
                    elif tag.get('name', '').startswith('📁'):
                        # 自定义文件夹
                        folder_name = tag['name'].replace('📁 ', '')
                        self.statusBar().showMessage(f"显示文件夹: {folder_name} 的文件")
                    else:
                        # 其他标签
                        self.statusBar().showMessage(f"显示标签: {tag['name']} 的文件")

                self.on_tag_selected(tag_id)

            else:
                # 分类文件夹，不做筛选
                self.statusBar().showMessage(f"选中文件夹: {folder_id}")

        except Exception as e:
            print(f"处理文件夹选择失败: {e}")
            self.statusBar().showMessage("文件夹筛选失败")

    def _apply_folder_filter(self, filter_type, filter_value=None):
        """应用文件夹筛选 - 高性能版本

        Args:
            filter_type: 筛选类型 (None, "recent", "staging", "type", "entry", "time")
            filter_value: 筛选值
        """
        try:
            print(f"🔧 应用文件夹筛选: type={filter_type}, value={filter_value}")

            # 清除标签筛选状态（文件夹筛选与标签筛选互斥）
            if hasattr(self, 'current_tag_filter'):
                self.current_tag_filter = None
                print("  - 已清除标签筛选状态")

            # 🔧 优化：预设置模型状态，避免双重加载
            print(f"🔧 预设置文件夹筛选模型状态...")

            # 🔧 优化：缓存中转文件夹数据，避免重复查询
            if filter_type == "staging":
                # 中转文件夹：使用缓存的显示项目
                if not hasattr(self, '_staging_cache') or self._staging_cache is None:
                    self._staging_cache = self.file_service.get_staging_display_items()
                files = self._staging_cache
                total_files = len(files)
            else:
                # 其他筛选：使用标准查询
                files = self.file_service.get_files(
                    limit=self.file_view.get_current_page_size(),
                    offset=0,
                    folder_filter_type=filter_type,
                    folder_filter_value=filter_value
                )
                total_files = self.file_service.get_file_count(
                    folder_filter_type=filter_type,
                    folder_filter_value=filter_value
                )

            # 预设置模型状态（在设置文件之前）
            if hasattr(self, 'file_view') and self.file_view:
                if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                    # 重置模型回调函数为普通分页
                    self.file_view.table_view.model.set_data_loader_callback(self._load_files_with_pagination)
                    # 设置总文件数
                    self.file_view.table_view.model.set_total_files(total_files)
                    # 重置到第一页
                    self.file_view.table_view.model.goToPage(0)
                    print(f"🔧 表格视图预设置完成: {total_files} 个文件")

                if hasattr(self.file_view, 'grid_view') and hasattr(self.file_view.grid_view, 'model'):
                    # 重置模型回调函数为普通分页
                    self.file_view.grid_view.model.set_data_loader_callback(self._load_files_with_pagination)
                    # 设置总文件数
                    self.file_view.grid_view.model.set_total_files(total_files)
                    # 重置到第一页
                    self.file_view.grid_view.model.goToPage(0)
                    print(f"🔧 网格视图预设置完成: {total_files} 个文件")

            # 保存当前的文件夹筛选状态
            self.current_folder_filter = {
                'type': filter_type,
                'value': filter_value
            }

            # 设置文件到视图（使用优化方法，避免重复加载）
            print(f"🔄 设置文件到视图: {len(files)} 个文件")
            self._set_files_optimized(files)

            # 立即更新分页信息显示
            if hasattr(self.file_view, 'update_pagination_info'):
                self.file_view.update_pagination_info()
                print(f"🔄 分页信息已更新")

            print(f"🚀 快速筛选完成: {len(files)} 个文件 (共 {total_files} 个)")

        except Exception as e:
            print(f"应用文件夹筛选失败: {e}")

    def _get_tag_info_cached(self, tag_id):
        """获取标签信息（带缓存）

        Args:
            tag_id: 标签ID

        Returns:
            dict: 标签信息，如果不存在则返回None
        """
        # 简单的内存缓存
        if not hasattr(self, '_tag_cache'):
            self._tag_cache = {}

        if tag_id not in self._tag_cache:
            # 🔧 优化：复用标签服务实例
            if not hasattr(self, '_tag_service'):
                from smartvault.services.tag_service import TagService
                self._tag_service = TagService()

            self._tag_cache[tag_id] = self._tag_service.get_tag_by_id(tag_id)

        return self._tag_cache[tag_id]

    def on_tag_selected(self, tag_id):
        """处理标签选中事件

        Args:
            tag_id: 标签ID，空字符串表示清除筛选
        """
        try:
            print(f"📥 主窗口：收到标签选择事件 - tag_id: {tag_id}")

            # 诊断当前状态
            self._diagnose_filter_state(f"标签选中前: {tag_id}")

            if not tag_id:  # 清除筛选
                print("🧹 清除标签筛选，显示所有文件")
                self.clear_tag_filter()
                return

            # 清除文件夹筛选状态（标签筛选与文件夹筛选互斥）
            if hasattr(self, 'current_folder_filter'):
                self.current_folder_filter = {'type': None, 'value': None}
                print("  - 已清除文件夹筛选状态")

            print(f"🔍 按标签筛选文件: {tag_id}")

            # 🔧 优化：使用缓存的标签信息
            tag = self._get_tag_info_cached(tag_id)

            if not tag:
                print(f"❌ 标签不存在: {tag_id}")
                self.show_status_message("标签不存在", False)
                return

            print(f"✅ 找到标签: {tag['name']}")

            # 🔧 优化：复用标签服务实例
            # 获取该标签的文件总数
            total_files = self._tag_service.get_files_count_by_tag_hierarchy(tag_id)
            print(f"📊 标签文件总数: {total_files}")

            # 获取当前页面大小
            page_size = self.file_view.get_current_page_size()

            # 获取第一页文件（支持分页）
            if page_size >= 999999:  # "全部"选项
                files = self._tag_service.get_files_by_tag_hierarchy(tag_id)
                print(f"📁 加载所有文件: {len(files)} 个")
            else:
                files = self._tag_service.get_files_by_tag_hierarchy(tag_id, limit=page_size, offset=0)
                print(f"📁 加载第1页文件: {len(files)} 个 (共 {total_files} 个)")

            # 设置标签筛选状态（用于分页回调）
            self.current_tag_filter = tag_id

            # 🔧 关键修复：在设置文件到视图之前，先设置回调函数和总文件数
            print(f"🔧 预设置模型状态...")
            if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                # 设置标签筛选的数据加载回调
                self.file_view.table_view.model.set_data_loader_callback(self._load_files_for_tag_filter)
                self.file_view.table_view.model.set_total_files(total_files)
                # 重置到第一页
                self.file_view.table_view.model.goToPage(0)
                print(f"🔧 表格视图回调和总文件数已预设置: {total_files}")
            if hasattr(self.file_view, 'grid_view') and hasattr(self.file_view.grid_view, 'model'):
                # 设置标签筛选的数据加载回调
                self.file_view.grid_view.model.set_data_loader_callback(self._load_files_for_tag_filter)
                self.file_view.grid_view.model.set_total_files(total_files)
                # 重置到第一页
                self.file_view.grid_view.model.goToPage(0)
                print(f"🔧 网格视图回调和总文件数已预设置: {total_files}")

            # 更新文件视图（使用标签筛选专用方法）
            print(f"🔄 更新文件视图...")
            if hasattr(self.file_view, 'set_files_for_tag_filter'):
                self.file_view.set_files_for_tag_filter(files)
            else:
                self.file_view.set_files(files)

            # 立即更新分页信息显示
            if hasattr(self.file_view, 'update_pagination_info'):
                self.file_view.update_pagination_info()
                print(f"🔄 分页信息已更新")

            print(f"✅ 文件视图更新完成")

            # 更新状态栏
            self.show_status_message(f"标签 '{tag['name']}': {total_files} 个文件", True)

            print(f"🎉 标签筛选完成: {tag['name']}, {len(files)} 个文件")

        except Exception as e:
            print(f"❌ 标签筛选失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_status_message(f"标签筛选失败: {e}", False)

    def clear_tag_filter(self):
        """清除标签筛选"""
        try:
            print("🧹 开始清除标签筛选...")

            # 诊断清除前状态
            self._diagnose_filter_state("清除标签筛选前")

            # 清除标签筛选状态
            if hasattr(self, 'current_tag_filter'):
                self.current_tag_filter = None
                print("  - 已清除current_tag_filter")

            # 重置数据加载回调为普通分页
            if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                self.file_view.table_view.model.set_data_loader_callback(self._load_files_with_pagination)
                print("  - 已重置table_view回调函数")
            if hasattr(self.file_view, 'grid_view') and hasattr(self.file_view.grid_view, 'model'):
                self.file_view.grid_view.model.set_data_loader_callback(self._load_files_with_pagination)
                print("  - 已重置grid_view回调函数")

            # 重新加载所有文件
            print("  - 开始重新加载所有文件...")
            self.load_initial_data(show_message=False)

            # 诊断清除后状态
            self._diagnose_filter_state("清除标签筛选后")

            self.show_status_message("已清除标签筛选", True)
            print("✅ 标签筛选清除完成")
        except Exception as e:
            print(f"❌ 清除标签筛选失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_status_message(f"清除筛选失败: {e}", False)

    def on_file_activated(self, file_id):
        """处理文件激活事件

        Args:
            file_id: 文件ID
        """
        # TODO: 打开文件
        self.statusBar().showMessage(f"打开文件: {file_id}")

    def on_file_selected(self, file_id):
        """处理文件选中事件

        Args:
            file_id: 文件ID
        """
        # 检查是否有重要消息正在显示（如批量处理统计）
        if hasattr(self, '_important_message_active') and self._important_message_active:
            return

        # 更新当前选中的文件ID
        self.current_selected_file_id = file_id

        # 获取文件信息并触发AI标签建议
        self.ai_integration_handler.update_ai_tag_suggestions(file_id)











    def on_file_selection_changed(self, selected_count):
        """处理文件选择变化事件

        Args:
            selected_count: 选中的文件数量
        """
        self.selected_file_count = selected_count

        # 如果没有选中文件或选中多个文件，清除AI标签建议面板
        if selected_count == 0:
            # 没有选中文件时，清除AI面板并显示默认状态
            if hasattr(self, 'ai_tag_panel'):
                self.ai_tag_panel.clear_suggestions()
            self.current_selected_file_id = None

            if not (hasattr(self, '_important_message_active') and self._important_message_active):
                self.statusBar().showMessage("就绪")
        elif selected_count == 1:
            # 选中一个文件（AI建议已在on_file_selected中处理）
            if not (hasattr(self, '_important_message_active') and self._important_message_active):
                self.statusBar().showMessage("已选中 1 个文件")
        else:
            # 选中多个文件时，清除AI面板
            if hasattr(self, 'ai_tag_panel'):
                self.ai_tag_panel.clear_suggestions()
            self.current_selected_file_id = None

            if not (hasattr(self, '_important_message_active') and self._important_message_active):
                self.statusBar().showMessage(f"已选中 {selected_count} 个文件")

        # 更新菜单状态（如果需要的话）
        self._update_menu_states(selected_count)

    def _update_menu_states(self, selected_count):
        """更新菜单状态

        Args:
            selected_count: 选中的文件数量
        """
        # 更新菜单管理器的状态
        if hasattr(self, 'menu_manager'):
            self.menu_manager.update_menu_states(selected_count)

    def on_batch_to_staging(self):
        """批量移入中转文件夹"""
        selected_file_ids = self.file_view.get_selected_file_ids()
        if not selected_file_ids:
            QMessageBox.information(self, "提示", "请先选择要操作的文件")
            return

        try:
            success_count = 0
            for file_id in selected_file_ids:
                if self.file_service.move_to_staging(file_id):
                    success_count += 1

            # 刷新视图
            self.load_initial_data(show_message=False)

            # 显示结果
            if success_count == len(selected_file_ids):
                self.show_status_message(f"已将 {success_count} 个文件移入中转文件夹", True)
            else:
                failed_count = len(selected_file_ids) - success_count
                self.show_status_message(f"成功 {success_count} 个，失败 {failed_count} 个", False)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量移入中转文件夹失败: {e}")

    def on_batch_from_staging(self):
        """批量移出中转文件夹"""
        selected_file_ids = self.file_view.get_selected_file_ids()
        if not selected_file_ids:
            QMessageBox.information(self, "提示", "请先选择要操作的文件")
            return

        try:
            success_count = 0
            for file_id in selected_file_ids:
                if self.file_service.move_from_staging(file_id):
                    success_count += 1

            # 刷新视图
            self.load_initial_data(show_message=False)

            # 显示结果
            if success_count == len(selected_file_ids):
                self.show_status_message(f"已将 {success_count} 个文件移出中转文件夹", True)
            else:
                failed_count = len(selected_file_ids) - success_count
                self.show_status_message(f"成功 {success_count} 个，失败 {failed_count} 个", False)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量移出中转文件夹失败: {e}")

    def on_batch_export(self):
        """批量导出文件"""
        selected_file_ids = self.file_view.get_selected_file_ids()
        if not selected_file_ids:
            QMessageBox.information(self, "提示", "请先选择要导出的文件")
            return

        # 选择导出目录
        from PySide6.QtWidgets import QFileDialog
        export_dir = QFileDialog.getExistingDirectory(
            self,
            "选择导出目录",
            ""
        )

        if not export_dir:
            return

        try:
            success_count = 0
            failed_files = []

            for file_id in selected_file_ids:
                try:
                    # 获取文件信息
                    file_info = self.file_service.get_file_by_id(file_id)
                    if not file_info:
                        failed_files.append(f"文件ID {file_id}: 文件不存在")
                        continue

                    # 确定源文件路径
                    if file_info["entry_type"] in ["copy", "move"]:
                        source_path = file_info["library_path"]
                    else:
                        source_path = file_info["original_path"]

                    if not os.path.exists(source_path):
                        failed_files.append(f"{file_info['name']}: 源文件不存在")
                        continue

                    # 确定目标路径
                    target_path = os.path.join(export_dir, file_info["name"])

                    # 如果目标文件已存在，添加序号
                    counter = 1
                    original_target = target_path
                    while os.path.exists(target_path):
                        name, ext = os.path.splitext(original_target)
                        target_path = f"{name}({counter}){ext}"
                        counter += 1

                    # 复制文件
                    import shutil
                    shutil.copy2(source_path, target_path)
                    success_count += 1

                except Exception as e:
                    failed_files.append(f"{file_info.get('name', file_id)}: {str(e)}")

            # 显示结果
            if success_count == len(selected_file_ids):
                self.show_status_message(f"成功导出 {success_count} 个文件到 {export_dir}", True)
            else:
                failed_count = len(selected_file_ids) - success_count
                message = f"成功导出 {success_count} 个文件，失败 {failed_count} 个"
                if failed_files:
                    message += f"\n失败详情:\n" + "\n".join(failed_files[:5])  # 只显示前5个错误
                    if len(failed_files) > 5:
                        message += f"\n... 还有 {len(failed_files) - 5} 个错误"
                QMessageBox.warning(self, "导出结果", message)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量导出失败: {e}")

    def on_batch_delete(self):
        """批量删除文件"""
        selected_file_ids = self.file_view.get_selected_file_ids()
        if not selected_file_ids:
            QMessageBox.information(self, "提示", "请先选择要删除的文件")
            return

        # 确认删除
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除选中的 {len(selected_file_ids)} 个文件吗？\n\n"
            "注意：这将从文件库中删除文件记录，但不会删除物理文件。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        try:
            success_count = 0
            for file_id in selected_file_ids:
                if self.file_service.remove_file(file_id):
                    success_count += 1

            # 刷新视图
            self.load_initial_data(show_message=False)

            # 显示结果
            if success_count == len(selected_file_ids):
                self.show_status_message(f"已删除 {success_count} 个文件", True)
            else:
                failed_count = len(selected_file_ids) - success_count
                self.show_status_message(f"成功删除 {success_count} 个，失败 {failed_count} 个", False)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"批量删除失败: {e}")

    def on_refresh_view(self):
        """刷新视图"""
        try:
            # 重新加载文件列表
            self.load_initial_data(show_message=False)
            self.show_status_message("视图已刷新", True)
        except Exception as e:
            self.show_status_message(f"刷新视图失败: {e}", False)

    def on_help(self):
        """显示帮助对话框"""
        print("打开帮助对话框...")
        try:
            from smartvault.ui.dialogs.help_dialog import HelpDialog
            print("已导入 HelpDialog 类")
            dialog = HelpDialog(self)
            print("已创建 HelpDialog 实例")
            dialog.exec()
            print("帮助对话框已关闭")
        except Exception as e:
            print(f"帮助对话框打开失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"无法打开帮助对话框: {e}")

    def on_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于 SmartVault",
            "SmartVault 智能文件管理系统\n\n"
            "版本: 3.0.0\n"
            "作者: Mojianghu\n\n"
            "按F1键打开用户帮助"
        )

    def on_tag_management(self):
        """显示标签管理对话框"""
        print("打开标签管理对话框...")
        try:
            from smartvault.ui.dialogs.tag_management_dialog import TagManagementDialog
            print("已导入 TagManagementDialog 类")
            dialog = TagManagementDialog(self)
            print("已创建 TagManagementDialog 实例")

            # 连接标签变化信号
            dialog.tags_changed.connect(self.on_tags_changed)

            result = dialog.exec()
            print(f"对话框结果: {result}")

            # 移除不必要的成功提示，符合"用户干预极简化"原则
        except Exception as e:
            print(f"标签管理对话框打开失败: {e}")
            import traceback
            traceback.print_exc()

    def on_tags_changed(self):
        """处理标签变化事件"""
        print("标签发生变化，刷新相关UI...")

        # 刷新导航面板中的标签列表
        if hasattr(self, 'navigation_panel'):
            self.navigation_panel.refresh_tags()

        # 刷新文件列表中的标签和备注显示
        if hasattr(self, 'file_view'):
            # 刷新表格视图的标签和备注
            if hasattr(self.file_view, 'table_view') and hasattr(self.file_view.table_view, 'model'):
                if hasattr(self.file_view.table_view.model, 'refresh_file_data'):
                    self.file_view.table_view.model.refresh_file_data()
                else:
                    self.file_view.table_view.model.refresh_file_tags()

            # 刷新网格视图的标签和备注
            if hasattr(self.file_view, 'grid_view') and hasattr(self.file_view.grid_view, 'model'):
                if hasattr(self.file_view.grid_view.model, 'refresh_file_data'):
                    self.file_view.grid_view.model.refresh_file_data()
                else:
                    self.file_view.grid_view.model.file_tags_cache.clear()
                    # 触发网格视图重绘
                    self.file_view.grid_view.model.layoutChanged.emit()

        self.show_status_message("标签已更新", True)

    def on_settings(self):
        """显示设置对话框"""
        print("打开设置对话框...")
        try:
            from smartvault.ui.dialogs import SettingsDialog
            print("已导入 SettingsDialog 类")
            dialog = SettingsDialog(self)
            print("已创建 SettingsDialog 实例")

            # 设置监控服务实例
            dialog.set_monitor_service(self.monitor_service)
            print("已设置监控服务实例")

            result = dialog.exec()
            print(f"对话框结果: {result}")

            if result == SettingsDialog.Accepted:
                # 设置已保存，文件库切换由SettingsDialog通过on_library_changed处理
                self.show_status_message("设置已保存", True)

                # 重新加载剪贴板服务配置
                if hasattr(self, 'clipboard_service') and self.clipboard_service:
                    self.clipboard_service.reload_config()

                # 重新设置浮动窗口模式
                self.clipboard_handler.setup_clipboard_float_mode()
        except Exception as e:
            print(f"设置对话框打开失败: {e}")
            import traceback
            traceback.print_exc()

    def restore_window_geometry(self):
        """恢复窗口大小和位置"""
        settings = QSettings("SmartVault", "MainWindow")

        # 恢复窗口大小和位置
        geometry = settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
        else:
            # 默认大小
            self.resize(1024, 768)
            # 居中显示
            screen = QApplication.primaryScreen().geometry()
            self.setGeometry(
                screen.width() // 2 - 512,
                screen.height() // 2 - 384,
                1024, 768
            )

        # 恢复分割器大小
        splitter_sizes = settings.value("splitter_sizes")
        if splitter_sizes and hasattr(self, "splitter"):
            self.splitter.setSizes([int(size) for size in splitter_sizes])

    def apply_user_config(self):
        """应用用户配置"""
        try:
            from smartvault.utils.config import get_ui_config, get_search_config, get_advanced_config

            # 获取配置
            ui_config = get_ui_config()
            search_config = get_search_config()
            advanced_config = get_advanced_config()

            print(f"应用用户配置: UI={ui_config}, 搜索={search_config}")

            # 应用UI配置
            self.apply_ui_config(ui_config)

            # 应用搜索配置
            self.apply_search_config(search_config)

            # 应用高级配置
            self.apply_advanced_config(advanced_config)

        except Exception as e:
            print(f"应用用户配置失败: {e}")

    def apply_ui_config(self, ui_config):
        """应用UI配置

        Args:
            ui_config: UI配置字典
        """
        try:
            # 应用默认视图模式
            default_view_mode = ui_config.get("default_view_mode", "table")
            if hasattr(self, 'file_view') and hasattr(self.file_view, 'switch_view_mode'):
                from smartvault.ui.views.file_table_view import ViewMode
                view_mode_map = {
                    "table": ViewMode.TABLE,
                    "grid": ViewMode.GRID,
                    "details": ViewMode.DETAILS
                }
                if default_view_mode in view_mode_map:
                    self.file_view.switch_view_mode(view_mode_map[default_view_mode])
                    print(f"设置默认视图模式: {default_view_mode}")

            # 应用默认页面大小
            default_page_size = ui_config.get("default_page_size", 100)
            if hasattr(self, 'file_view') and hasattr(self.file_view, 'set_page_size'):
                self.file_view.set_page_size(default_page_size)
                print(f"设置默认页面大小: {default_page_size}")

        except Exception as e:
            print(f"应用UI配置失败: {e}")

    def apply_search_config(self, search_config):
        """应用搜索配置

        Args:
            search_config: 搜索配置字典
        """
        try:
            # 这里可以设置搜索相关的默认值
            # 目前搜索配置主要在搜索时使用，暂时不需要在启动时应用
            print(f"搜索配置已加载: {search_config}")
        except Exception as e:
            print(f"应用搜索配置失败: {e}")

    def apply_advanced_config(self, advanced_config):
        """应用高级配置

        Args:
            advanced_config: 高级配置字典
        """
        try:
            # 应用日志级别
            log_level = advanced_config.get("log_level", "INFO")
            print(f"日志级别设置: {log_level}")

            # 应用缓存设置
            enable_cache = advanced_config.get("enable_cache", True)
            cache_size = advanced_config.get("cache_size_mb", 200)
            print(f"缓存设置: 启用={enable_cache}, 大小={cache_size}MB")

        except Exception as e:
            print(f"应用高级配置失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件（优化版本）

        Args:
            event: 关闭事件
        """
        print("正在关闭应用程序...")

        try:
            # 立即接受关闭事件，让窗口快速消失
            event.accept()

            # 快速保存关键设置
            self._save_window_settings()

            # 快速清理服务（跳过耗时操作）
            self._quick_cleanup()

            print("应用程序已快速关闭")

        except Exception as e:
            print(f"关闭应用程序时出错: {e}")
            # 确保事件被接受
            event.accept()

    def _save_window_settings(self):
        """快速保存窗口设置"""
        try:
            print("🔧 保存应用程序状态...")

            # 🔧 保存所有树控件的展开状态
            if hasattr(self, 'navigation_panel') and self.navigation_panel:
                self.navigation_panel.save_tree_states()

            # 保存窗口几何信息
            settings = QSettings("SmartVault", "MainWindow")
            settings.setValue("geometry", self.saveGeometry())
            if hasattr(self, "splitter"):
                settings.setValue("splitter_sizes", self.splitter.sizes())

            print("✅ 应用程序状态保存完成")
        except Exception as e:
            print(f"❌ 保存应用程序状态失败: {e}")

    def save_window_geometry(self):
        """保存窗口几何信息（兼容性方法）"""
        try:
            settings = QSettings("SmartVault", "MainWindow")
            settings.setValue("geometry", self.saveGeometry())
            if hasattr(self, "splitter"):
                settings.setValue("splitter_sizes", self.splitter.sizes())
        except Exception as e:
            print(f"保存窗口几何信息失败: {e}")

    def _quick_cleanup(self):
        """快速清理资源"""
        try:
            # 1. 快速停止备份服务（不等待线程）
            if hasattr(self, 'backup_manager') and self.backup_manager:
                self.backup_manager.cleanup()
                print("备份管理器已清理")
            elif hasattr(self, 'backup_service') and self.backup_service:
                self.backup_service.is_running = False
                print("备份服务已标记停止")

            # 2. 快速清理监控服务（不更新数据库）
            if hasattr(self, 'monitor_service') and self.monitor_service:
                self.monitor_service.watchers.clear()
                print("监控服务已清理")

            # 3. 快速清理数据库连接
            self._cleanup_database()

            # 4. 不等待线程，让daemon线程自然结束

        except Exception as e:
            print(f"快速清理失败: {e}")



    def _cleanup_database(self):
        """清理数据库连接"""
        try:
            # 清理文件服务的数据库连接
            if hasattr(self, 'file_service') and self.file_service:
                if hasattr(self.file_service, '_db') and self.file_service._db:
                    print("正在关闭文件服务数据库连接...")
                    self.file_service._db.close()
                    self.file_service._db = None

            # 清理监控服务的数据库连接
            if hasattr(self, 'monitor_service') and self.monitor_service:
                if hasattr(self.monitor_service, '_db') and self.monitor_service._db:
                    print("正在关闭监控服务数据库连接...")
                    self.monitor_service._db.close()
                    self.monitor_service._db = None

            # 清理剪贴板服务的数据库连接
            if hasattr(self, 'clipboard_service') and self.clipboard_service:
                if hasattr(self.clipboard_service, 'db') and self.clipboard_service.db:
                    print("正在关闭剪贴板服务数据库连接...")
                    self.clipboard_service.db.close()
                    self.clipboard_service.db = None

        except Exception as e:
            print(f"清理数据库连接时出错: {e}")

    # ========== 委托方法 - 为了保持向后兼容性 ==========

    def toggle_clipboard_monitor(self):
        """委托给剪贴板处理器的切换监控方法"""
        if hasattr(self, 'clipboard_handler'):
            self.clipboard_handler.toggle_clipboard_monitor()
        else:
            print("剪贴板处理器未初始化")

    def show_clipboard_demo(self):
        """委托给剪贴板处理器的演示方法"""
        if hasattr(self, 'clipboard_handler'):
            self.clipboard_handler.show_clipboard_demo()
        else:
            print("剪贴板处理器未初始化")























    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):
        """拖拽放下事件"""
        try:
            urls = event.mimeData().urls()
            if not urls:
                return

            # 收集所有文件路径
            file_paths = []
            for url in urls:
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    file_paths.append(file_path)

            if not file_paths:
                return

            # 使用统一的文件添加处理逻辑
            self._process_dropped_files(file_paths)
            event.acceptProposedAction()

        except Exception as e:
            print(f"处理拖拽文件失败: {e}")
            self.show_status_message(f"拖拽文件失败: {e}", False)

    def _process_dropped_files(self, file_paths):
        """处理拖拽的文件（使用统一的文件添加逻辑）"""
        try:
            from smartvault.ui.dialogs.add_file_dialog import AddFileDialog

            # 创建添加文件对话框
            dialog = AddFileDialog(self)

            # 添加拖拽的文件到对话框
            for file_path in file_paths:
                dialog.file_list.append(file_path)
                if os.path.isdir(file_path):
                    dialog.file_list_widget.addItem(f"文件夹: {file_path}")
                else:
                    dialog.file_list_widget.addItem(file_path)

            # 显示对话框让用户选择入库方式
            result = dialog.exec()

            if result == AddFileDialog.Accepted and dialog.file_list:
                # 获取入库方式
                entry_type = dialog.get_entry_type()

                # 获取入库方式显示文本
                entry_type_text = {
                    "link": "链接",
                    "copy": "复制",
                    "move": "移动"
                }.get(entry_type, entry_type)

                # 批量处理文件
                added_files = []
                failed_files = []
                total_files = len(dialog.file_list)

                # 启动批量操作上下文
                self.file_service.start_batch_operation("拖拽添加文件")

                # 显示进度条
                if hasattr(self, 'compact_progress_bar'):
                    self.compact_progress_bar.start_progress(f"拖拽添加文件", total_files, True)

                # 显示进度信息
                self.show_status_message(f"正在添加 {total_files} 个拖拽文件...", True)

                for i, file_path in enumerate(dialog.file_list):
                    try:
                        # 更新进度条
                        if hasattr(self, 'compact_progress_bar'):
                            self.compact_progress_bar.set_progress(i, f"添加文件 {i+1}/{total_files}")

                        # 如果是文件夹，则提取实际路径
                        if file_path.startswith("文件夹: "):
                            file_path = file_path[5:].strip()  # 移除"文件夹: "前缀

                        # 使用统一的文件添加入口
                        file_id = self.file_service.add_file(file_path, entry_type)
                        added_files.append(file_path)
                    except Exception as e:
                        failed_files.append((file_path, str(e)))

                # 结束批量操作（会自动处理收集到的重复文件建议）
                self.file_service.end_batch_operation()

                # 完成进度条
                if hasattr(self, 'compact_progress_bar'):
                    self.compact_progress_bar.finish_progress(f"完成添加 {len(added_files)} 个文件", True, 2000)

                # 显示结果统计信息
                if added_files:
                    # 更新状态栏，使用高亮显示
                    self.show_status_message(f"成功{entry_type_text} {len(added_files)} 个拖拽文件到智能文件库", True)

                    # 刷新文件列表，但不显示加载消息
                    self._load_files_silently()

                if failed_files:
                    # 显示失败消息，使用高亮显示
                    self.show_status_message(f"{len(failed_files)} 个拖拽文件{entry_type_text}失败", False)

        except Exception as e:
            print(f"处理拖拽文件失败: {e}")
            self.show_status_message(f"处理拖拽文件失败: {e}", False)

    def on_files_dropped_to_folder(self, file_ids, tag_id):
        """处理文件拖拽到文件夹事件

        Args:
            file_ids: 文件ID列表
            tag_id: 目标文件夹标签ID
        """
        try:
            print(f"🎯 主窗口：处理拖拽 {len(file_ids)} 个文件到文件夹 {tag_id}")

            # 获取标签信息
            tag = self.tag_service.get_tag_by_id(tag_id)
            if not tag:
                self.show_status_message("目标文件夹不存在", False)
                return

            folder_name = tag['name']

            # 批量处理文件夹标签（移除旧的，添加新的）
            success_count = 0
            failed_count = 0

            for file_id in file_ids:
                try:
                    # 获取文件的现有标签
                    existing_tags = self.tag_service.get_file_tags(file_id)

                    # 检查文件是否已经在目标文件夹中
                    if any(t['id'] == tag_id for t in existing_tags):
                        print(f"文件 {file_id} 已在文件夹 {folder_name} 中，跳过")
                        success_count += 1  # 算作成功，因为文件已经在目标位置
                        continue

                    # 移除所有现有的文件夹标签（以📁开头的标签）
                    removed_folders = []
                    for tag in existing_tags:
                        tag_name = tag.get('name', '')
                        if tag_name.startswith('📁'):
                            if self.tag_service.remove_tag_from_file(file_id, tag['id']):
                                folder_name_only = tag_name[1:]  # 去掉📁前缀
                                removed_folders.append(folder_name_only)
                                print(f"🗑️ 已移除文件 {file_id} 的文件夹标签: {folder_name_only}")

                    # 添加新的文件夹标签
                    if self.tag_service.add_tag_to_file(file_id, tag_id):
                        success_count += 1
                        if removed_folders:
                            print(f"📁 文件 {file_id} 从 {', '.join(removed_folders)} 移动到 {folder_name}")
                        else:
                            print(f"✅ 文件 {file_id} 已添加到文件夹 {folder_name}")
                    else:
                        failed_count += 1
                        print(f"❌ 文件 {file_id} 添加到文件夹 {folder_name} 失败")

                except Exception as e:
                    failed_count += 1
                    print(f"❌ 处理文件 {file_id} 失败: {e}")

            # 显示结果
            if success_count > 0:
                self.show_status_message(f"成功将 {success_count} 个文件移动到文件夹 '{folder_name}'", True)

                # 刷新导航面板（如果有文件计数显示）
                if hasattr(self.navigation_panel, 'refresh_tags'):
                    self.navigation_panel.refresh_tags()

                # 刷新当前视图以反映文件移动
                self._refresh_current_view_after_move()

            if failed_count > 0:
                self.show_status_message(f"{failed_count} 个文件移动失败", False)

            print(f"🎉 拖拽操作完成: 成功 {success_count}, 失败 {failed_count}")

        except Exception as e:
            print(f"❌ 处理拖拽到文件夹失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_status_message(f"拖拽操作失败: {e}", False)

    def _refresh_current_view_after_move(self):
        """文件移动后刷新当前视图"""
        try:
            print("🔄 刷新当前视图以反映文件移动")

            # 检查当前的筛选状态
            if hasattr(self, 'current_tag_filter') and self.current_tag_filter:
                # 如果当前正在显示某个标签筛选，重新加载该标签的文件
                print(f"🏷️ 重新加载标签筛选: {self.current_tag_filter}")
                self.on_tag_selected(self.current_tag_filter)

            elif hasattr(self, 'current_folder_filter') and self.current_folder_filter:
                # 如果当前正在显示某个文件夹筛选，重新加载该文件夹的文件
                print(f"📁 重新加载文件夹筛选: {self.current_folder_filter}")
                self.on_folder_selected(self.current_folder_filter.get('value', ''))

            else:
                # 如果没有特定筛选，刷新所有文件视图
                print("📋 刷新所有文件视图")
                self._load_files_silently()

        except Exception as e:
            print(f"❌ 刷新当前视图失败: {e}")
            # 如果刷新失败，至少尝试静默加载文件
            try:
                self._load_files_silently()
            except:
                pass








    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_F1:
            # F1打开帮助
            self.on_help()
        elif event.key() == Qt.Key_F5:
            # F5刷新
            self.refresh_file_list()
        elif event.key() == Qt.Key_Delete:
            # Delete键删除选中文件
            self.on_delete_files()
        else:
            super().keyPressEvent(event)




