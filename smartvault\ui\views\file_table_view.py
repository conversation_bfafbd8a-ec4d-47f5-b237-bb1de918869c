"""
文件表格视图
"""

from PySide6.QtWidgets import (
    QTableView, QMenu, QHeaderView, QAbstractItemView, QApplication,
    QWidget, QHBoxLayout, QVBoxLayout, QPushButton, QLabel, QLineEdit,
    QComboBox, QToolButton, QStackedWidget, QTabWidget
)
from PySide6.QtCore import Qt, Signal, QEvent
from PySide6.QtGui import QFontMetrics
from smartvault.ui.models.file_table_model import FileTableModel
from smartvault.ui.views.file_grid_view import FileGridView
from smartvault.ui.views.file_details_view import FileDetailsView
from smartvault.ui.resources import get_icon
from enum import Enum


class ViewMode(Enum):
    """视图模式枚举"""
    TABLE = "table"
    GRID = "grid"
    DETAILS = "details"


class FileTableViewContainer(QWidget):
    """文件表格视图容器，包含文件表格视图和分页控件"""

    # 自定义信号
    file_double_clicked = Signal(str)  # 文件双击信号
    file_clicked = Signal(str)  # 文件点击信号
    context_menu_requested = Signal(QMenu, str)  # 右键菜单请求信号
    page_changed = Signal(int, int)  # 页面变化信号 (page, page_size)
    page_size_changed = Signal(int)  # 页面大小变化信号

    def __init__(self, parent=None):
        """初始化

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 当前视图模式
        self.current_view_mode = ViewMode.TABLE

        # 创建布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)

        # 创建视图切换选项卡
        self.view_tabs = QTabWidget(self)
        self.view_tabs.setTabPosition(QTabWidget.North)
        self.view_tabs.setMaximumHeight(35)  # 限制选项卡高度
        self.view_tabs.currentChanged.connect(self.on_tab_changed)
        self.layout.addWidget(self.view_tabs)

        # 创建顶部工具栏（包含分页和搜索控件）
        self.top_toolbar = QWidget(self)
        self.top_toolbar.setMinimumHeight(40)  # 减少高度给选项卡让出空间
        self.top_toolbar.setObjectName("fileViewTopToolbar")  # 设置对象名用于主题样式
        self.top_toolbar_layout = QHBoxLayout(self.top_toolbar)
        self.top_toolbar_layout.setContentsMargins(10, 5, 10, 5)
        self.top_toolbar_layout.setSpacing(10)
        self.layout.addWidget(self.top_toolbar)

        # 创建视图切换器（堆叠窗口）
        self.view_stack = QStackedWidget()

        # 创建表格视图
        self.table_view = FileTableView(self)
        self.view_stack.addWidget(self.table_view)

        # 创建网格视图
        self.grid_view = FileGridView(self)
        self.view_stack.addWidget(self.grid_view)

        # 创建详情视图
        self.details_view = FileDetailsView(self)
        self.view_stack.addWidget(self.details_view)

        # 设置默认视图
        self.view_stack.setCurrentWidget(self.table_view)

        self.layout.addWidget(self.view_stack)

        # 初始化选项卡
        self.init_view_tabs()

        # 创建分页控件
        self.create_pagination_controls()

        # 创建搜索控件
        self.create_search_controls()

        # 连接信号
        self.table_view.file_activated.connect(self.file_double_clicked)
        self.table_view.file_selected.connect(self.file_clicked)
        self.table_view.customContextMenuRequested.connect(self.on_context_menu_requested)

        # 连接选择变化信号
        self.table_view.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.grid_view.selectionModel().selectionChanged.connect(self.on_selection_changed)

        # 设置详情视图更新回调
        self.table_view.model.set_details_view_update_callback(self.update_details_view)

    def init_view_tabs(self):
        """初始化视图切换选项卡"""
        # 添加选项卡
        self.view_tabs.addTab(QWidget(), "表格视图")
        self.view_tabs.addTab(QWidget(), "网格视图")
        self.view_tabs.addTab(QWidget(), "详情视图")

        # 设置默认选中表格视图
        self.view_tabs.setCurrentIndex(0)

    def on_tab_changed(self, index):
        """选项卡切换事件处理

        Args:
            index: 选项卡索引
        """
        if index == 0:
            self.switch_view_mode(ViewMode.TABLE)
        elif index == 1:
            self.switch_view_mode(ViewMode.GRID)
        elif index == 2:
            self.switch_view_mode(ViewMode.DETAILS)

        # 连接网格视图信号
        self.grid_view.file_activated.connect(self.file_double_clicked)
        self.grid_view.file_selected.connect(self.file_clicked)
        self.grid_view.customContextMenuRequested.connect(self.on_context_menu_requested)

        # 连接详情视图信号
        self.details_view.file_activated.connect(self.file_double_clicked)
        self.details_view.file_selected.connect(self.file_clicked)

    def create_pagination_controls(self):
        """创建分页控件"""
        # 创建分页控件容器
        pagination_widget = QWidget(self)
        pagination_layout = QHBoxLayout(pagination_widget)
        pagination_layout.setContentsMargins(0, 0, 0, 0)
        pagination_layout.setSpacing(5)

        # 页码信息标签
        self.page_info_label = QLabel("第 1 页 / 共 1 页")
        self.page_info_label.setMinimumWidth(100)

        # 分页按钮（移除硬编码样式，使用主题样式）
        self.first_page_button = QToolButton(self)
        self.first_page_button.setIcon(get_icon("first_page"))
        self.first_page_button.setToolTip("第一页")
        self.first_page_button.clicked.connect(self.go_to_first_page)
        self.first_page_button.setObjectName("paginationButton")

        self.prev_page_button = QToolButton(self)
        self.prev_page_button.setIcon(get_icon("prev_page"))
        self.prev_page_button.setToolTip("上一页")
        self.prev_page_button.clicked.connect(self.go_to_prev_page)
        self.prev_page_button.setObjectName("paginationButton")

        self.next_page_button = QToolButton(self)
        self.next_page_button.setIcon(get_icon("next_page"))
        self.next_page_button.setToolTip("下一页")
        self.next_page_button.clicked.connect(self.go_to_next_page)
        self.next_page_button.setObjectName("paginationButton")

        self.last_page_button = QToolButton(self)
        self.last_page_button.setIcon(get_icon("last_page"))
        self.last_page_button.setToolTip("最后一页")
        self.last_page_button.clicked.connect(self.go_to_last_page)
        self.last_page_button.setObjectName("paginationButton")

        # 每页显示数量
        self.page_size_label = QLabel("每页显示:")
        self.page_size_combo = QComboBox(self)
        self.page_size_combo.addItems(["50", "100", "200", "500", "全部"])
        self.page_size_combo.setCurrentText("100")  # 默认100条，保证性能
        self.page_size_combo.currentTextChanged.connect(self.on_page_size_changed)
        self.page_size_combo.setMinimumWidth(80)

        # 添加到分页布局
        pagination_layout.addWidget(self.first_page_button)
        pagination_layout.addWidget(self.prev_page_button)
        pagination_layout.addWidget(self.page_info_label)
        pagination_layout.addWidget(self.next_page_button)
        pagination_layout.addWidget(self.last_page_button)
        pagination_layout.addSpacing(20)
        pagination_layout.addWidget(self.page_size_label)
        pagination_layout.addWidget(self.page_size_combo)
        pagination_layout.addStretch()

        # 添加分页控件到顶部工具栏
        self.top_toolbar_layout.addWidget(pagination_widget)
        self.top_toolbar_layout.addStretch()

    def create_search_controls(self):
        """创建搜索控件"""
        # 创建搜索控件容器
        search_widget = QWidget(self)
        search_layout = QHBoxLayout(search_widget)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(5)

        # 搜索框（移除硬编码样式，使用主题样式）
        self.search_edit = QLineEdit(self)
        self.search_edit.setPlaceholderText("搜索...")
        self.search_edit.setClearButtonEnabled(True)
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        self.search_edit.setMinimumWidth(200)
        self.search_edit.setObjectName("searchEdit")

        # 搜索列选择（移除硬编码样式，使用主题样式）
        self.search_column_combo = QComboBox(self)
        self.search_column_combo.addItems(["全部", "名称", "类型", "位置"])
        self.search_column_combo.currentIndexChanged.connect(self.on_search_column_changed)
        self.search_column_combo.setObjectName("searchCombo")

        # 高级搜索按钮（移除硬编码样式，使用主题样式）
        self.advanced_search_button = QPushButton("高级搜索", self)
        self.advanced_search_button.setMaximumWidth(80)
        self.advanced_search_button.setObjectName("advancedSearchButton")
        self.advanced_search_button.clicked.connect(self.on_advanced_search_clicked)

        # 添加到搜索布局
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(self.search_column_combo)
        search_layout.addWidget(self.advanced_search_button)
        search_layout.addStretch()

        # 添加搜索控件到顶部工具栏
        self.top_toolbar_layout.addWidget(search_widget)

    def set_model(self, model):
        """设置模型

        Args:
            model: 数据模型
        """
        self.table_view.setModel(model)
        
        # 重新恢复列宽设置，因为setModel可能会重置表头
        self.table_view.restore_column_widths()

        # 同时为网格视图模型设置数据加载回调
        if hasattr(model, 'data_loader_callback') and model.data_loader_callback:
            self.grid_view.model.set_data_loader_callback(model.data_loader_callback)
        if hasattr(model, 'search_total_count_callback') and model.search_total_count_callback:
            self.grid_view.model.set_search_total_count_callback(model.search_total_count_callback)

        self.update_pagination_info()

    def update_pagination_info(self):
        """更新分页信息"""
        # 获取当前活动视图的模型
        current_view = self.get_current_view()
        model = None

        if self.current_view_mode == ViewMode.TABLE:
            model = self.table_view.model
        elif self.current_view_mode == ViewMode.GRID:
            model = self.grid_view.model
        elif self.current_view_mode == ViewMode.DETAILS:
            # 详情视图使用表格视图的模型进行分页
            model = self.table_view.model

        if model:
            current_page = model.getCurrentPage() + 1  # 转为1-based

            # 🔧 修复：正确获取当前筛选状态下的文件总数
            # 检查是否在标签筛选模式
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'current_tag_filter'):
                main_window = main_window.parent()

            # 🔧 关键修复：更准确地检测标签筛选模式
            is_tag_filter_mode = False
            if (main_window and hasattr(main_window, 'current_tag_filter') and
                main_window.current_tag_filter):
                is_tag_filter_mode = True
                print(f"🔧 检测到标签筛选模式，当前标签ID: {main_window.current_tag_filter}")

            if is_tag_filter_mode:
                # 🔧 关键修复：在标签筛选模式下，直接使用模型中已设置的总文件数
                # 因为在on_tag_selected中已经正确设置了total_files
                if hasattr(model, 'total_files') and model.total_files > 0:
                    total_files = model.total_files
                    print(f"🔧 标签筛选模式：使用模型中预设的总文件数: {total_files}")
                else:
                    # 备用方案：从标签服务查询
                    try:
                        if hasattr(main_window, '_tag_service') and main_window._tag_service:
                            actual_total = main_window._tag_service.get_files_count_by_tag_hierarchy(main_window.current_tag_filter)
                            total_files = actual_total
                            print(f"🔧 标签筛选模式：从标签服务获取实际总数: {total_files}")
                        else:
                            total_files = len(model.filtered_files)
                            print(f"🔧 标签筛选模式：使用过滤文件数: {total_files}")
                    except Exception as e:
                        total_files = len(model.filtered_files)
                        print(f"🔧 标签筛选模式：查询失败，使用过滤文件数: {total_files}, 错误: {e}")
            elif (main_window and hasattr(main_window, 'current_folder_filter') and
                  main_window.current_folder_filter.get('type') == 'staging'):
                # 中转文件夹模式：使用实际的文件数量
                total_files = len(model.filtered_files)
                print(f"🔧 中转文件夹模式检测到，使用实际文件数: {total_files}")
            else:
                # 普通模式：使用数据库中的总文件数
                if hasattr(model, 'total_files') and model.total_files > 0:
                    total_files = model.total_files
                else:
                    total_files = len(model.filtered_files)
                print(f"🔧 普通模式检测到，使用数据库总文件数: {total_files}")

            # 🔧 修复：根据实际文件总数重新计算总页数
            if model.page_size >= 999999:  # "全部"选项
                total_pages = 1
            else:
                total_pages = max(1, (total_files + model.page_size - 1) // model.page_size)

            # 更新页码信息
            self.page_info_label.setText(f"第 {current_page} 页 / 共 {total_pages} 页 (共 {total_files} 个文件)")

            # 更新按钮状态
            self.first_page_button.setEnabled(current_page > 1)
            self.prev_page_button.setEnabled(current_page > 1)
            self.next_page_button.setEnabled(current_page < total_pages)
            self.last_page_button.setEnabled(current_page < total_pages)

            # 更新页面大小下拉框
            current_page_size = str(model.page_size)
            if model.page_size >= 999999:
                current_page_size = "全部"

            # 阻断信号以避免触发事件
            self.page_size_combo.blockSignals(True)
            if current_page_size in ["50", "100", "200", "500", "全部"]:
                self.page_size_combo.setCurrentText(current_page_size)
            self.page_size_combo.blockSignals(False)

            # 打印调试信息
            print(f"🔄 分页信息已更新: 第 {current_page} 页 / 共 {total_pages} 页, 每页 {model.page_size} 条, 共 {total_files} 条记录")

    def go_to_first_page(self):
        """转到第一页"""
        model = self.get_current_model()
        if model:
            model.goToPage(0)
            self.update_pagination_info()

    def go_to_prev_page(self):
        """转到上一页"""
        model = self.get_current_model()
        if model:
            model.previousPage()
            self.update_pagination_info()

    def go_to_next_page(self):
        """转到下一页"""
        model = self.get_current_model()
        if model:
            model.nextPage()
            self.update_pagination_info()

    def go_to_last_page(self):
        """转到最后一页"""
        model = self.get_current_model()
        if model:
            model.goToPage(model.getTotalPages() - 1)
            self.update_pagination_info()

    def get_current_model(self):
        """获取当前活动视图的模型"""
        if self.current_view_mode == ViewMode.TABLE:
            return self.table_view.model
        elif self.current_view_mode == ViewMode.GRID:
            return self.grid_view.model
        elif self.current_view_mode == ViewMode.DETAILS:
            # 详情视图使用表格视图的模型进行分页
            return self.table_view.model
        else:
            return self.table_view.model

    def on_page_size_changed(self, text):
        """页面大小改变事件

        Args:
            text: 页面大小文本
        """
        model = self.get_current_model()
        if model:
            if text == "全部":
                model.setPageSize(999999)  # 一个足够大的数字
            else:
                try:
                    page_size = int(text)
                    model.setPageSize(page_size)
                except ValueError:
                    pass
            self.update_pagination_info()

    def on_search_text_changed(self, text):
        """搜索文本改变事件

        Args:
            text: 搜索文本
        """
        model = self.get_current_model()
        if model:
            column_index = self.get_search_column_index()
            model.applyFilter(text, column_index)
            model.updateVisibleFiles()
            self.update_pagination_info()

    def on_search_column_changed(self, index):
        """搜索列改变事件

        Args:
            index: 列索引
        """
        model = self.table_view.model
        if model:
            column_index = self.get_search_column_index()
            model.applyFilter(self.search_edit.text(), column_index)
            model.updateVisibleFiles()
            self.update_pagination_info()

    def get_search_column_index(self):
        """获取搜索列索引

        Returns:
            int: 列索引
        """
        column_text = self.search_column_combo.currentText()
        if column_text == "名称":
            return FileTableModel.NAME_COLUMN
        elif column_text == "类型":
            return FileTableModel.TYPE_COLUMN
        elif column_text == "位置":
            return FileTableModel.LOCATION_COLUMN
        else:
            return -1  # 全部

    def on_advanced_search_clicked(self):
        """高级搜索按钮点击事件"""
        # 查找主窗口对象并调用高级搜索方法
        main_window = self
        while main_window and not hasattr(main_window, 'on_advanced_search'):
            main_window = main_window.parent()

        if main_window and hasattr(main_window, 'on_advanced_search'):
            main_window.on_advanced_search()
        else:
            print("未找到主窗口的高级搜索方法")

    def on_context_menu_requested(self, position):
        """处理右键菜单请求

        Args:
            position: 菜单位置
        """
        # 获取选中的文件ID
        file_ids = self.get_selected_file_ids()
        if file_ids:
            # 创建菜单并发送信号
            menu = QMenu(self)
            self.context_menu_requested.emit(menu, file_ids[0])

    def get_selected_file_ids(self):
        """获取选中的文件ID列表

        Returns:
            list: 文件ID列表
        """
        current_view = self.get_current_view()
        return current_view.get_selected_file_ids()

    def on_selection_changed(self, selected, deselected):
        """处理选择变化事件

        Args:
            selected: 新选中的项目
            deselected: 取消选中的项目
        """
        # 获取选中的文件数量
        selected_count = len(self.get_selected_file_ids())

        # 发送选择变化信号给主窗口
        main_window = self
        while main_window and not hasattr(main_window, 'on_file_selection_changed'):
            main_window = main_window.parent()

        if main_window and hasattr(main_window, 'on_file_selection_changed'):
            main_window.on_file_selection_changed(selected_count)

    def select_all_files(self):
        """全选文件"""
        current_view = self.get_current_view()
        if hasattr(current_view, 'selectAll'):
            current_view.selectAll()

    def clear_selection(self):
        """清除选择"""
        current_view = self.get_current_view()
        if hasattr(current_view, 'clearSelection'):
            current_view.clearSelection()

    def invert_selection(self):
        """反选"""
        current_view = self.get_current_view()
        if hasattr(current_view, 'invert_selection'):
            current_view.invert_selection()
        else:
            # 如果视图没有反选方法，我们手动实现
            self._manual_invert_selection(current_view)

    def _manual_invert_selection(self, view):
        """手动实现反选功能

        Args:
            view: 当前视图
        """
        if not hasattr(view, 'model') or not view.model:
            return

        # 获取当前选中的索引
        selected_indexes = set(view.selectedIndexes())

        # 清除当前选择
        view.clearSelection()

        # 选择所有未选中的项目
        model = view.model
        total_rows = model.rowCount()

        for row in range(total_rows):
            if self.current_view_mode == ViewMode.TABLE:
                # 表格视图：检查第一列的索引
                index = model.index(row, 0)
                if index not in selected_indexes:
                    view.selectionModel().select(index, view.selectionModel().Select | view.selectionModel().Rows)
            else:
                # 网格视图：直接检查行索引
                index = model.index(row, 0)
                if index not in selected_indexes:
                    view.selectionModel().select(index, view.selectionModel().Select)

    def remove_file(self, file_id):
        """从视图中移除文件

        Args:
            file_id: 文件ID
        """
        # 从所有视图中都移除
        self.table_view.remove_file(file_id)
        self.grid_view.remove_file(file_id)
        self.details_view.remove_file(file_id)

    def set_files(self, files):
        """设置文件列表

        Args:
            files: 文件列表
        """
        # 在设置文件之前，先根据当前页面大小设置调整模型的页面大小
        model = self.table_view.model
        if model:
            current_text = self.page_size_combo.currentText()
            if current_text == "全部":
                model.setPageSize(999999)  # 一个足够大的数字
            else:
                try:
                    page_size = int(current_text)
                    model.setPageSize(page_size)
                except ValueError:
                    pass

        # 设置到当前活动的视图
        current_view = self.get_current_view()
        current_view.set_files(files)

        # 同步到其他视图的模型
        if self.current_view_mode == ViewMode.TABLE:
            # 同步分页状态到网格视图
            self.sync_pagination_state(self.table_view.model, self.grid_view.model)
            self.grid_view.model.setFiles(files)
            self.details_view.set_files(files)
        elif self.current_view_mode == ViewMode.GRID:
            # 同步分页状态到表格视图
            self.sync_pagination_state(self.grid_view.model, self.table_view.model)
            self.table_view.model.setFiles(files)
            self.details_view.set_files(files)
        else:  # DETAILS
            self.table_view.model.setFiles(files)
            self.grid_view.model.setFiles(files)

        self.update_pagination_info()

    def set_files_for_tag_filter(self, files):
        """为标签筛选设置文件列表（不触发数据库重新加载）

        Args:
            files: 文件列表
        """
        # 在设置文件之前，先根据当前页面大小设置调整模型的页面大小
        model = self.table_view.model
        if model:
            current_text = self.page_size_combo.currentText()
            if current_text == "全部":
                model.setPageSize(999999)  # 一个足够大的数字
            else:
                try:
                    page_size = int(current_text)
                    model.setPageSize(page_size)
                except ValueError:
                    pass

        # 设置到当前活动的视图（使用标签筛选专用方法）
        current_view = self.get_current_view()
        if hasattr(current_view, 'set_files_for_tag_filter'):
            current_view.set_files_for_tag_filter(files)
        else:
            # 回退到普通方法（用于网格视图和详情视图）
            current_view.set_files(files)

        # 同步到其他视图的模型（使用标签筛选专用方法）
        if self.current_view_mode == ViewMode.TABLE:
            # 同步分页状态到网格视图
            self.sync_pagination_state(self.table_view.model, self.grid_view.model)
            self.grid_view.model.setFilesForTagFilter(files)
            self.details_view.set_files(files)
        elif self.current_view_mode == ViewMode.GRID:
            # 同步分页状态到表格视图
            self.sync_pagination_state(self.grid_view.model, self.table_view.model)
            self.table_view.model.setFilesForTagFilter(files)
            self.details_view.set_files(files)
        else:  # DETAILS
            self.table_view.model.setFilesForTagFilter(files)
            self.grid_view.model.setFilesForTagFilter(files)

        self.update_pagination_info()

    def append_files(self, files):
        """批量添加文件到现有列表

        Args:
            files: 文件列表
        """
        # 添加到当前活动的视图
        current_view = self.get_current_view()
        current_view.append_files(files)

        # 同步到其他视图
        if self.current_view_mode == ViewMode.TABLE:
            self.grid_view.append_files(files)
            self.details_view.append_files(files)
        elif self.current_view_mode == ViewMode.GRID:
            self.table_view.append_files(files)
            self.details_view.append_files(files)
        else:  # DETAILS
            self.table_view.append_files(files)
            self.grid_view.append_files(files)

        self.update_pagination_info()

    def get_current_page_size(self):
        """获取当前页面大小设置

        Returns:
            int: 当前页面大小
        """
        current_text = self.page_size_combo.currentText()
        if current_text == "全部":
            return 999999  # 一个足够大的数字
        else:
            try:
                return int(current_text)
            except ValueError:
                return 100  # 默认值

    def switch_view_mode(self, view_mode):
        """切换视图模式

        Args:
            view_mode: 视图模式 (ViewMode枚举)
        """
        if view_mode == self.current_view_mode:
            return

        old_view_mode = self.current_view_mode
        self.current_view_mode = view_mode

        # 同步更新选项卡状态（避免递归调用）
        tab_index = 0
        if view_mode == ViewMode.GRID:
            tab_index = 1
        elif view_mode == ViewMode.DETAILS:
            tab_index = 2

        if self.view_tabs.currentIndex() != tab_index:
            self.view_tabs.blockSignals(True)
            self.view_tabs.setCurrentIndex(tab_index)
            self.view_tabs.blockSignals(False)

        # 获取当前视图的文件数据和分页状态
        current_files = []
        source_model = None

        if old_view_mode == ViewMode.TABLE:
            source_model = self.table_view.model
            current_files = source_model.files if source_model else []
        elif old_view_mode == ViewMode.GRID:
            source_model = self.grid_view.model
            current_files = source_model.files if source_model else []
        elif old_view_mode == ViewMode.DETAILS:
            source_model = self.table_view.model  # 详情视图使用表格视图的模型
            current_files = source_model.files if source_model else []

        # 切换视图并同步数据
        if view_mode == ViewMode.TABLE:
            self.view_stack.setCurrentWidget(self.table_view)
            if source_model and current_files:
                self.sync_pagination_state(source_model, self.table_view.model)
                self.table_view.set_files(current_files)
        elif view_mode == ViewMode.GRID:
            self.view_stack.setCurrentWidget(self.grid_view)
            if source_model and current_files:
                self.sync_pagination_state(source_model, self.grid_view.model)
                self.grid_view.set_files(current_files)
        elif view_mode == ViewMode.DETAILS:
            self.view_stack.setCurrentWidget(self.details_view)
            if source_model and current_files:
                # 详情视图使用表格视图的模型，所以同步到表格视图
                if view_mode != ViewMode.TABLE:
                    self.sync_pagination_state(source_model, self.table_view.model)
                self.details_view.set_files(current_files)

        # 更新分页信息
        self.update_pagination_info()

        print(f"视图模式已切换到: {view_mode.value}")

    def update_details_view(self, files):
        """更新详情视图的文件列表

        Args:
            files: 文件列表
        """
        if self.current_view_mode == ViewMode.DETAILS:
            # 只有在详情视图模式下才更新详情视图
            self.details_view.set_files(files)
            print(f"详情视图文件列表已更新，显示 {len(files)} 个文件")

    def get_current_view_mode(self):
        """获取当前视图模式

        Returns:
            ViewMode: 当前视图模式
        """
        return self.current_view_mode

    def set_page_size(self, page_size):
        """设置页面大小

        Args:
            page_size: 页面大小
        """
        try:
            # 更新页面大小下拉框
            if hasattr(self, 'page_size_combo'):
                if page_size >= 999999:
                    self.page_size_combo.setCurrentText("全部")
                else:
                    self.page_size_combo.setCurrentText(str(page_size))

            # 应用页面大小到当前模型
            current_model = self.get_current_model()
            if current_model:
                current_model.setPageSize(page_size)
                current_model._reload_current_page_data()

            print(f"页面大小已设置为: {page_size}")
        except Exception as e:
            print(f"设置页面大小失败: {e}")

    def get_current_view(self):
        """获取当前活动的视图组件

        Returns:
            QWidget: 当前视图组件
        """
        if self.current_view_mode == ViewMode.TABLE:
            return self.table_view
        elif self.current_view_mode == ViewMode.GRID:
            return self.grid_view
        elif self.current_view_mode == ViewMode.DETAILS:
            return self.details_view
        else:
            return self.table_view  # 默认返回表格视图

    def sync_pagination_state(self, source_model, target_model):
        """同步分页状态

        Args:
            source_model: 源模型
            target_model: 目标模型
        """
        if hasattr(source_model, 'total_files'):
            target_model.total_files = source_model.total_files
        if hasattr(source_model, 'current_page'):
            target_model.current_page = source_model.current_page
        if hasattr(source_model, 'page_size'):
            target_model.page_size = source_model.page_size
        if hasattr(source_model, 'total_pages'):
            target_model.total_pages = source_model.total_pages

        # 同步回调函数
        if hasattr(source_model, 'data_loader_callback') and source_model.data_loader_callback:
            target_model.set_data_loader_callback(source_model.data_loader_callback)
        if hasattr(source_model, 'search_total_count_callback') and source_model.search_total_count_callback:
            target_model.set_search_total_count_callback(source_model.search_total_count_callback)


class FileTableView(QTableView):
    """文件表格视图"""

    # 自定义信号
    file_activated = Signal(str)  # 文件被激活（双击）
    file_selected = Signal(str)   # 文件被选中

    def __init__(self, parent=None):
        """初始化视图

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 设置视图属性
        self.setSelectionBehavior(QTableView.SelectRows)
        self.setSelectionMode(QTableView.ExtendedSelection)
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        self.setAlternatingRowColors(True)
        self.setShowGrid(False)
        self.setSortingEnabled(True)
        self.verticalHeader().setVisible(False)

        # 设置表头属性
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setStretchLastSection(False)  # 禁用最后一列自动拉伸，保持用户设置的列宽
        header.setMinimumSectionSize(50)  # 设置最小列宽为50像素

        # 禁止编辑
        self.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # 为表头安装事件过滤器，处理双击事件
        self.horizontalHeader().installEventFilter(self)

        # 创建数据模型
        self.model = FileTableModel()
        self.setModel(self.model)

        # 设置默认列宽
        self.set_default_column_widths()
        
        # 恢复保存的列宽设置
        self.restore_column_widths()
        
        # 连接列宽变化信号到保存方法
        header.sectionResized.connect(self.save_column_widths)
        
        # 恢复保存的列宽（会自动设置_is_first_use标志）
        self.restore_column_widths()

        # 连接信号
        self.doubleClicked.connect(self.on_double_clicked)
        self.clicked.connect(self.on_clicked)

        # 拖拽相关属性
        self.drag_start_position = None
        
        # 性能优化：预创建右键菜单组件，避免每次创建导致的卡顿
        # 传入主窗口的服务实例，避免重复创建服务
        from smartvault.ui.components.note_menu import NoteMenu
        from smartvault.ui.components.quick_tag_menu import QuickTagMenu
        
        # 通过parent()方法向上查找主窗口
        main_window = self
        while main_window and not hasattr(main_window, 'file_service'):
            main_window = main_window.parent()
            
        if main_window and hasattr(main_window, 'file_service') and hasattr(main_window, 'tag_service'):
            self._note_menu = NoteMenu(self, main_window.file_service)
            self._quick_tag_menu = QuickTagMenu(self, main_window.tag_service)
        else:
            self._note_menu = NoteMenu(self)
            self._quick_tag_menu = QuickTagMenu(self)
        
        # 连接信号
        self._note_menu.notes_changed.connect(self._on_tags_changed)
        self._quick_tag_menu.tags_changed.connect(self._on_tags_changed)

    def on_double_clicked(self, index):
        """处理双击事件

        Args:
            index: 项目索引
        """
        # 获取文件ID
        file_id = self.model.data(index, FileTableModel.FileIdRole)
        if file_id:
            # 发送信号
            self.file_activated.emit(file_id)

            # 查找主窗口对象并调用打开文件方法
            main_window = self
            while main_window and not hasattr(main_window, 'on_open_file'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'on_open_file'):
                main_window.on_open_file(file_id)

    def on_clicked(self, index):
        """处理单击事件

        Args:
            index: 项目索引
        """
        # 获取文件ID
        file_id = self.model.data(index, FileTableModel.FileIdRole)
        if file_id:
            self.file_selected.emit(file_id)

    def show_context_menu(self, position):
        """显示上下文菜单

        Args:
            position: 菜单位置
        """
        # 获取选中的项目
        indexes = self.selectedIndexes()
        if not indexes:
            return

        # 获取唯一的行索引
        rows = set(index.row() for index in indexes)
        if not rows:
            return

        # 创建上下文菜单
        menu = QMenu(self)

        # 添加菜单项
        open_action = menu.addAction("打开")
        show_in_explorer_action = menu.addAction("在系统资源管理器中显示")
        menu.addSeparator()

        # 编辑备注菜单项（置顶，使用频率最高）
        # 性能优化：使用预创建的组件，避免重复创建导致卡顿
        file_ids = [self.model.data(self.model.index(row, 0), self.model.FileIdRole) for row in rows]
        note_action = self._note_menu.create_action(menu, file_ids)
        menu.addAction(note_action)

        # 快速标签菜单
        # 性能优化：使用预创建的组件，避免重复创建导致卡顿
        tag_submenu = self._quick_tag_menu.create_menu(menu, file_ids)
        menu.addMenu(tag_submenu)

        menu.addSeparator()

        # 标签管理
        manage_tags_action = menu.addAction("添加标签")
        clear_tags_action = menu.addAction("清除标签")
        menu.addSeparator()

        # 中转文件夹操作
        staging_action = None
        if len(rows) == 1:
            # 单个文件时，检查当前状态并显示相应操作
            first_row = next(iter(rows))
            file_id = self.model.data(self.model.index(first_row, 0), FileTableModel.FileIdRole)
            if file_id:
                # 获取文件信息以检查中转状态
                main_window = self
                while main_window and not hasattr(main_window, 'file_service'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'file_service'):
                    try:
                        file_info = main_window.file_service.get_file_by_id(file_id)
                        if file_info:
                            staging_status = file_info.get("staging_status", "normal")
                            if staging_status == "staging":
                                staging_action = menu.addAction("📤 移至智能文件库")
                            else:
                                staging_action = menu.addAction("📥 移入中转文件夹")
                    except Exception as e:
                        print(f"检查文件中转状态失败: {e}")
        else:
            # 多个文件时，显示批量操作
            staging_to_action = menu.addAction("📥 批量移入中转文件夹")
            staging_from_action = menu.addAction("📤 批量移至智能文件库")

        menu.addSeparator()

        # 添加"移动到文件夹"菜单
        move_to_folder_menu = self._create_move_to_folder_menu(menu, rows)
        if move_to_folder_menu:
            menu.addMenu(move_to_folder_menu)

        # 添加"添加到文件夹"菜单
        add_to_folder_menu = self._create_add_to_folder_menu(menu, rows)
        if add_to_folder_menu:
            menu.addMenu(add_to_folder_menu)

        rename_action = menu.addAction("重命名")
        move_action = menu.addAction("移动到...")
        export_action = menu.addAction("📤 导出到...")
        menu.addSeparator()
        delete_action = menu.addAction("删除文件")

        # 显示菜单并获取选择的操作
        action = menu.exec(self.mapToGlobal(position))

        # 处理选择的操作
        if action == open_action:
            # 只打开第一个选中的文件
            first_row = next(iter(rows))
            file_id = self.model.data(self.model.index(first_row, 0), FileTableModel.FileIdRole)
            if file_id:
                self.file_activated.emit(file_id)

                # 查找主窗口对象并调用打开文件方法
                main_window = self
                while main_window and not hasattr(main_window, 'on_open_file'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'on_open_file'):
                    main_window.on_open_file(file_id)

        elif action == show_in_explorer_action:
            # 在系统资源管理器中显示文件
            first_row = next(iter(rows))
            file_id = self.model.data(self.model.index(first_row, 0), FileTableModel.FileIdRole)
            if file_id:
                self._show_file_in_explorer(file_id)

        elif action == manage_tags_action:
            # 管理标签
            file_ids = []
            for row in rows:
                file_id = self.model.data(self.model.index(row, 0), FileTableModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if file_ids:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'on_manage_file_tags'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'on_manage_file_tags'):
                    main_window.on_manage_file_tags(file_ids)

        elif action == clear_tags_action:
            # 清除标签
            file_ids = []
            for row in rows:
                file_id = self.model.data(self.model.index(row, 0), FileTableModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if file_ids:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'on_clear_file_tags'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'on_clear_file_tags'):
                    main_window.on_clear_file_tags(file_ids)

        # 处理中转文件夹操作
        elif 'staging_action' in locals() and action == staging_action:
            # 单个文件的中转状态切换
            first_row = next(iter(rows))
            file_id = self.model.data(self.model.index(first_row, 0), FileTableModel.FileIdRole)
            if file_id:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'file_service'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'file_service'):
                    try:
                        success = main_window.file_service.toggle_staging_status(file_id)
                        if success:
                            # 刷新视图
                            main_window._load_files_silently()
                            # 获取文件信息以显示状态
                            file_info = main_window.file_service.get_file_by_id(file_id)
                            if file_info:
                                staging_status = file_info.get("staging_status", "normal")
                                if staging_status == "staging":
                                    main_window.show_status_message("文件已移入中转文件夹", True)
                                else:
                                    main_window.show_status_message("文件已移至智能文件库，当前显示：全部文件", True)
                        else:
                            main_window.show_status_message("操作失败", False)
                    except Exception as e:
                        print(f"切换文件中转状态失败: {e}")
                        main_window.show_status_message(f"操作失败: {e}", False)

        elif 'staging_to_action' in locals() and action == staging_to_action:
            # 批量移入中转文件夹
            file_ids = []
            for row in rows:
                file_id = self.model.data(self.model.index(row, 0), FileTableModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if file_ids:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'file_service'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'file_service'):
                    try:
                        result = main_window.file_service.batch_move_to_staging(file_ids)
                        success_count = len(result['success'])
                        failed_count = len(result['failed'])

                        if success_count > 0:
                            main_window._load_files_silently()
                            main_window.show_status_message(f"成功移入 {success_count} 个文件到中转文件夹", True)

                        if failed_count > 0:
                            main_window.show_status_message(f"{failed_count} 个文件移入失败", False)
                    except Exception as e:
                        print(f"批量移入中转文件夹失败: {e}")
                        main_window.show_status_message(f"批量操作失败: {e}", False)

        elif 'staging_from_action' in locals() and action == staging_from_action:
            # 批量移出中转文件夹
            file_ids = []
            for row in rows:
                file_id = self.model.data(self.model.index(row, 0), FileTableModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if file_ids:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'file_service'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'file_service'):
                    try:
                        result = main_window.file_service.batch_move_from_staging(file_ids)
                        success_count = len(result['success'])
                        failed_count = len(result['failed'])

                        if success_count > 0:
                            main_window._load_files_silently()
                            main_window.show_status_message(f"成功移至智能文件库 {success_count} 个文件，当前显示：全部文件", True)

                        if failed_count > 0:
                            main_window.show_status_message(f"{failed_count} 个文件移出失败", False)
                    except Exception as e:
                        print(f"批量移出中转文件夹失败: {e}")
                        main_window.show_status_message(f"批量操作失败: {e}", False)

        elif action == rename_action:
            # 重命名文件
            # 只重命名第一个选中的文件
            first_row = next(iter(rows))
            file_id = self.model.data(self.model.index(first_row, 0), FileTableModel.FileIdRole)
            if file_id:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'on_rename_file'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'on_rename_file'):
                    main_window.on_rename_file(file_id)

        elif action == move_action:
            # 移动文件
            # 只移动第一个选中的文件
            first_row = next(iter(rows))
            file_id = self.model.data(self.model.index(first_row, 0), FileTableModel.FileIdRole)
            if file_id:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'on_move_file'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'on_move_file'):
                    main_window.on_move_file(file_id)

        elif action == export_action:
            # 导出文件
            file_ids = []
            for row in rows:
                file_id = self.model.data(self.model.index(row, 0), FileTableModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            # 查找主窗口对象
            main_window = self
            while main_window and not hasattr(main_window, 'on_export_files'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'on_export_files'):
                main_window.on_export_files(file_ids)

        elif action == delete_action:
            # 删除文件
            file_ids = []
            for row in rows:
                file_id = self.model.data(self.model.index(row, 0), FileTableModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            # 查找主窗口对象
            main_window = self
            while main_window and not hasattr(main_window, 'on_delete_file'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'on_delete_file'):
                main_window.on_delete_file(file_ids)

    def set_files(self, files):
        """设置文件列表

        Args:
            files: 文件信息字典列表
        """
        self.model.setFiles(files)

        # 只在首次使用时自动调整列宽
        if hasattr(self, '_is_first_use') and self._is_first_use:
            print("首次使用，自动调整列宽")
            self.resizeColumnsToContents()
            
            # 确保名称列有足够的宽度
            if self.columnWidth(FileTableModel.NAME_COLUMN) < 250:
                self.setColumnWidth(FileTableModel.NAME_COLUMN, 250)
                
            # 保存调整后的列宽
            self.save_column_widths()
            
            # 标记为已完成首次调整
            self._is_first_use = False
            print("首次列宽调整完成，后续将保持用户设置")

    def set_files_for_tag_filter(self, files):
        """为标签筛选设置文件列表（不触发数据库重新加载）

        Args:
            files: 文件信息字典列表
        """
        self.model.setFilesForTagFilter(files)

        # 只在首次使用时自动调整列宽
        if hasattr(self, '_is_first_use') and self._is_first_use:
            print("首次使用（标签筛选），自动调整列宽")
            self.resizeColumnsToContents()
            
            # 确保名称列有足够的宽度
            if self.columnWidth(FileTableModel.NAME_COLUMN) < 250:
                self.setColumnWidth(FileTableModel.NAME_COLUMN, 250)
                
            # 保存调整后的列宽
            self.save_column_widths()
            
            # 标记为已完成首次调整
            self._is_first_use = False
            print("首次列宽调整完成（标签筛选），后续将保持用户设置")

    def add_file(self, file):
        """添加文件

        Args:
            file: 文件信息字典
        """
        self.model.addFile(file)

    def append_files(self, files):
        """批量添加文件

        Args:
            files: 文件信息字典列表
        """
        self.model.appendFiles(files)

    def remove_file(self, file_id):
        """移除文件

        Args:
            file_id: 文件ID

        Returns:
            bool: 是否成功移除
        """
        return self.model.removeFile(file_id)

    def get_file(self, index):
        """获取文件信息

        Args:
            index: 索引

        Returns:
            dict: 文件信息字典，如果索引无效则返回None
        """
        return self.model.getFile(index)

    def get_selected_file_ids(self):
        """获取选中的文件ID列表

        Returns:
            list: 文件ID列表
        """
        file_ids = []
        indexes = self.selectedIndexes()
        rows = set(index.row() for index in indexes)

        for row in rows:
            file_id = self.model.data(self.model.index(row, 0), FileTableModel.FileIdRole)
            if file_id:
                file_ids.append(file_id)

        return file_ids

    def eventFilter(self, obj, event):
        """事件过滤器，处理表头的双击事件

        Args:
            obj: 事件源对象
            event: 事件对象

        Returns:
            bool: 是否已处理事件
        """
        # 检查是否是表头的双击事件
        if obj == self.horizontalHeader() and event.type() == QEvent.MouseButtonDblClick:
            # 获取双击的列索引
            index = self.horizontalHeader().logicalIndexAt(event.pos())
            if index >= 0:
                # 自动调整列宽
                self.resizeColumnToContents(index)
                return True

        # 其他事件交给基类处理
        return super().eventFilter(obj, event)

    def mousePressEvent(self, event):
        """鼠标按下事件 - 支持拖拽和多选"""
        if event.button() == Qt.LeftButton:
            index = self.indexAt(event.pos())
            if index.isValid():
                print(f"🖱️ 表格鼠标按下: 行{index.row()}, 列{index.column()}")

                # 检查当前文件是否已经被选中
                current_selected = self.get_selected_file_ids()
                current_file_id = self.model.data(index, FileTableModel.FileIdRole)
                is_already_selected = current_file_id in current_selected

                print(f"📋 当前选中文件数: {len(current_selected)}, 点击文件已选中: {is_already_selected}")

                # 检查是否真正点击在文件名文字上
                is_on_filename_text = self._is_click_on_filename_text(index, event.pos())

                # 拖拽逻辑：
                # 1. 点击在文件名文字上：允许文件拖拽
                # 2. 点击在非文件名文字区域但已选中文件：允许文件拖拽
                # 3. 点击在非文件名文字区域且未选中：允许框选
                if is_on_filename_text:
                    # 点击在文件名文字上，允许文件拖拽
                    self.drag_start_position = event.pos()
                    print(f"📁 文件名文字点击，允许文件拖拽")
                elif is_already_selected:
                    # 非文件名文字区域但是已选中文件，允许文件拖拽
                    self.drag_start_position = event.pos()
                    print(f"🎯 已选中文件的非文字区域，允许文件拖拽 ({len(current_selected)}个文件)")
                else:
                    # 非文件名文字区域且未选中，允许框选
                    self.drag_start_position = None
                    print(f"🔄 非文件名文字区域未选中文件，允许框选")
            else:
                # 空白区域，允许框选
                self.drag_start_position = None
                print(f"🔄 空白区域点击，允许框选")

        # 先调用父类方法处理选择逻辑
        super().mousePressEvent(event)

        # 输出当前选中的项目数量
        selected_count = len(self.get_selected_file_ids())
        print(f"📊 当前选中文件数量: {selected_count}")

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 触发拖拽或框选"""
        if not (event.buttons() & Qt.LeftButton):
            return

        # 如果没有设置拖拽起始位置，让父类处理（框选）
        if not self.drag_start_position:
            super().mouseMoveEvent(event)
            return

        # 检查是否移动了足够的距离来开始拖拽
        from PySide6.QtWidgets import QApplication
        if ((event.pos() - self.drag_start_position).manhattanLength() <
            QApplication.startDragDistance()):
            return

        # 文件拖拽模式
        index = self.indexAt(self.drag_start_position)
        if not index.isValid():
            # 如果起始位置无效，让父类处理
            super().mouseMoveEvent(event)
            return

        print(f"🎯 表格触发文件拖拽: 行{index.row()}, 列{index.column()}")
        self.startDrag(index)

    def _is_click_on_filename_text(self, index, click_pos):
        """检查点击位置是否在文件名文字上

        Args:
            index: 表格项索引
            click_pos: 点击位置

        Returns:
            bool: 是否点击在文件名文字上
        """
        try:
            # 只检查文件名列
            if index.column() != FileTableModel.NAME_COLUMN:
                return False

            # 获取单元格矩形区域
            cell_rect = self.visualRect(index)

            # 获取文件名文本
            filename = self.model.data(index, Qt.DisplayRole)
            if not filename:
                return False

            # 计算文本宽度（包括图标）
            font_metrics = QFontMetrics(self.font())
            text_width = font_metrics.horizontalAdvance(filename)

            # 图标宽度（大约16像素）+ 间距（4像素）
            icon_width = 20

            # 文本区域的实际宽度
            text_area_width = icon_width + text_width + 10  # 额外10像素边距

            # 计算相对于单元格的点击位置
            relative_x = click_pos.x() - cell_rect.left()

            # 检查是否在文本区域内
            is_on_text = 0 <= relative_x <= text_area_width

            print(f"🔍 文件名文字检测: 文件名='{filename}', 文本宽度={text_width}, 点击位置={relative_x}, 在文字上={is_on_text}")

            return is_on_text

        except Exception as e:
            print(f"❌ 文件名文字检测失败: {e}")
            # 出错时默认返回True，保持原有行为
            return True

    def startDrag(self, index):
        """开始拖拽操作"""
        try:
            # 获取选中的文件ID
            file_ids = self.get_selected_file_ids()
            if not file_ids:
                return

            print(f"📁 表格拖拽有效项目: 行{index.row()}, 列{index.column()}")

            # 创建拖拽数据
            from PySide6.QtCore import QMimeData
            from PySide6.QtGui import QDrag

            mime_data = QMimeData()

            # 设置文件ID数据（用于内部拖拽）
            mime_data.setText(",".join(file_ids))

            # 创建拖拽对象
            drag = QDrag(self)
            drag.setMimeData(mime_data)

            # 执行拖拽
            from PySide6.QtCore import Qt
            drop_action = drag.exec(Qt.MoveAction | Qt.CopyAction)

            print(f"🎯 表格拖拽完成: {drop_action}")

        except Exception as e:
            print(f"❌ 表格拖拽失败: {e}")
            import traceback
            traceback.print_exc()

    def set_default_column_widths(self):
        """设置默认列宽"""
        # 默认列宽
        default_widths = {
            FileTableModel.NAME_COLUMN: 300,
            FileTableModel.LOCATION_COLUMN: 100,
            FileTableModel.DATE_COLUMN: 150,
            FileTableModel.SIZE_COLUMN: 100,
            FileTableModel.TYPE_COLUMN: 80,
            FileTableModel.TAGS_COLUMN: 120
        }

        # 设置每列的默认宽度
        for column, width in default_widths.items():
            self.setColumnWidth(column, width)
            
    def save_column_widths(self):
        """保存列宽设置"""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings()
            settings.beginGroup("FileTableView")
            
            # 保存每列的宽度
            for column in range(self.model.columnCount()):
                width = self.columnWidth(column)
                settings.setValue(f"column_{column}_width", width)
                
            settings.endGroup()
        except Exception as e:
            print(f"保存列宽失败: {e}")
            
    def restore_column_widths(self):
        """恢复列宽设置"""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings()
            settings.beginGroup("FileTableView")
            
            # 检查是否是首次使用（没有保存的列宽设置）
            has_saved_widths = False
            for column in range(self.model.columnCount()):
                if settings.contains(f"column_{column}_width"):
                    has_saved_widths = True
                    break
                    
            # 如果有保存的列宽，则恢复；否则标记为首次使用
            if has_saved_widths:
                for column in range(self.model.columnCount()):
                    if settings.contains(f"column_{column}_width"):
                        width = settings.value(f"column_{column}_width", type=int)
                        if width > 0:  # 确保宽度有效
                            self.setColumnWidth(column, width)
                print("已恢复保存的列宽设置")
            else:
                # 首次使用，设置标志允许自动调整
                self._is_first_use = True
                print("首次使用，将在加载数据时自动调整列宽")
                
            settings.endGroup()
        except Exception as e:
            print(f"恢复列宽失败: {e}")
            # 如果恢复失败，也标记为首次使用
            self._is_first_use = True

    def _on_tags_changed(self):
        """处理标签变化事件"""
        # 刷新文件表格中的标签和备注显示
        if hasattr(self.model, 'refresh_file_data'):
            self.model.refresh_file_data()
        elif hasattr(self.model, 'file_tags_cache'):
            self.model.file_tags_cache.clear()
            # 刷新表格视图
            self.model.layoutChanged.emit()

        # 查找主窗口并通知标签变化
        main_window = self
        while main_window and not hasattr(main_window, 'on_tags_changed'):
            main_window = main_window.parent()

        if main_window and hasattr(main_window, 'on_tags_changed'):
            main_window.on_tags_changed()

    def _show_file_in_explorer(self, file_id):
        """在系统资源管理器中显示文件

        Args:
            file_id: 文件ID
        """
        try:
            # 获取文件服务
            main_window = self
            while main_window and not hasattr(main_window, 'file_service'):
                main_window = main_window.parent()

            if not main_window or not hasattr(main_window, 'file_service'):
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "无法获取文件服务")
                return

            # 获取文件信息
            file_info = main_window.file_service.get_file_by_id(file_id)
            if not file_info:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "文件信息不存在")
                return

            # 确定文件路径
            if file_info["entry_type"] == "link":
                file_path = file_info["original_path"]
            else:
                file_path = file_info["library_path"]

            # 检查文件是否存在
            import os
            if not os.path.exists(file_path):
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "文件不存在", f"文件物理路径不存在：\n{file_path}")
                return

            # 在系统资源管理器中显示文件
            import subprocess
            import platform

            system = platform.system()
            if system == "Windows":
                # Windows系统使用explorer命令，使用原始字符串避免转义问题
                normalized_path = os.path.normpath(file_path)
                # 使用列表形式传递参数，避免shell解析问题
                subprocess.run(['explorer', '/select,', normalized_path], check=False)
            elif system == "Darwin":  # macOS
                # macOS系统使用open命令
                subprocess.run(['open', '-R', file_path], check=True)
            elif system == "Linux":
                # Linux系统使用xdg-open命令
                subprocess.run(['xdg-open', os.path.dirname(file_path)], check=True)
            else:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "不支持的系统", f"当前系统 {system} 不支持此功能")

        except subprocess.CalledProcessError as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"打开资源管理器失败：{e}")
        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"显示文件失败：{e}")

    def _create_move_to_folder_menu(self, parent_menu, rows):
        """创建"移动到文件夹"子菜单

        Args:
            parent_menu: 父菜单
            rows: 选中的行号集合

        Returns:
            QMenu: 子菜单对象，如果没有可用文件夹则返回None
        """
        try:
            # 获取主窗口和标签服务
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window or not hasattr(main_window, 'tag_service'):
                return None

            # 获取自定义文件夹列表
            folder_tags = main_window.tag_service.get_folder_tags()

            if not folder_tags:
                return None

            # 创建子菜单
            from PySide6.QtWidgets import QMenu
            from PySide6.QtGui import QAction
            move_to_folder_menu = QMenu("移动到文件夹", parent_menu)

            # 获取选中的文件ID
            file_ids = []
            for row in rows:
                file_id = self.model.data(self.model.index(row, 0), self.model.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if not file_ids:
                return None

            # 为每个文件夹创建菜单项
            for folder in folder_tags:
                action = QAction(folder['name'], move_to_folder_menu)
                # 使用functools.partial避免lambda闭包问题
                from functools import partial
                action.triggered.connect(
                    partial(self._move_files_to_folder, file_ids, folder['id'])
                )
                move_to_folder_menu.addAction(action)

            # 添加分隔符和"新建文件夹"选项
            move_to_folder_menu.addSeparator()
            new_folder_action = QAction("+ 新建文件夹", move_to_folder_menu)
            new_folder_action.triggered.connect(
                partial(self._create_folder_and_move_files, file_ids)
            )
            move_to_folder_menu.addAction(new_folder_action)

            return move_to_folder_menu

        except Exception as e:
            print(f"创建移动到文件夹菜单失败: {e}")
            return None

    def _move_files_to_folder(self, file_ids, folder_tag_id):
        """将文件移动到指定文件夹

        Args:
            file_ids: 文件ID列表
            folder_tag_id: 文件夹标签ID
        """
        try:
            # 获取主窗口和服务
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window:
                return

            # 批量添加标签关联
            success_count = 0
            for file_id in file_ids:
                if main_window.tag_service.add_tag_to_file(file_id, folder_tag_id):
                    success_count += 1

                    # 如果文件在中转文件夹，移出中转状态
                    if hasattr(main_window, 'file_service'):
                        file_info = main_window.file_service.get_file_by_id(file_id)
                        if file_info and file_info.get("staging_status") == "staging":
                            main_window.file_service.move_from_staging(file_id)

            # 刷新视图
            if hasattr(main_window, '_load_files_silently'):
                main_window._load_files_silently()

            # 显示结果
            if success_count > 0:
                folder_tag = main_window.tag_service.get_tag_by_id(folder_tag_id)
                folder_name = folder_tag['name'] if folder_tag else '文件夹'
                main_window.show_status_message(
                    f"成功将 {success_count}/{len(file_ids)} 个文件移动到 {folder_name}",
                    True
                )
            else:
                main_window.show_status_message("移动文件失败", False)

        except Exception as e:
            print(f"移动文件到文件夹失败: {e}")
            # 获取主窗口显示错误
            main_window = self
            while main_window and not hasattr(main_window, 'show_status_message'):
                main_window = main_window.parent()
            if main_window:
                main_window.show_status_message(f"移动文件失败: {e}", False)

    def _create_folder_and_move_files(self, file_ids):
        """创建新文件夹并移动文件

        Args:
            file_ids: 文件ID列表
        """
        try:
            from PySide6.QtWidgets import QInputDialog, QMessageBox

            # 获取主窗口
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window:
                return

            # 输入文件夹名称
            folder_name, ok = QInputDialog.getText(
                self, "新建文件夹", "请输入文件夹名称:"
            )

            if not ok or not folder_name.strip():
                return

            folder_name = folder_name.strip()

            # 创建文件夹标签
            folder_tag_id = main_window.tag_service.create_folder_tag(folder_name)

            # 移动文件到新文件夹
            self._move_files_to_folder(file_ids, folder_tag_id)

            # 刷新导航面板
            if hasattr(main_window, 'navigation_panel'):
                main_window.navigation_panel.refresh_folder_tree()

        except Exception as e:
            print(f"创建文件夹并移动文件失败: {e}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"创建文件夹失败: {e}")

    def _create_add_to_folder_menu(self, parent_menu, rows):
        """创建"添加到文件夹"子菜单（不移出中转状态）

        Args:
            parent_menu: 父菜单
            rows: 选中的行号集合

        Returns:
            QMenu: 子菜单对象，如果没有可用文件夹则返回None
        """
        try:
            # 获取主窗口和标签服务
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window or not hasattr(main_window, 'tag_service'):
                return None

            # 获取自定义文件夹列表
            folder_tags = main_window.tag_service.get_folder_tags()

            if not folder_tags:
                return None

            # 创建子菜单
            from PySide6.QtWidgets import QMenu
            from PySide6.QtGui import QAction
            add_to_folder_menu = QMenu("添加到文件夹", parent_menu)

            # 获取选中的文件ID
            file_ids = []
            for row in rows:
                file_id = self.model.data(self.model.index(row, 0), self.model.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if not file_ids:
                return None

            # 为每个文件夹创建菜单项
            for folder in folder_tags:
                action = QAction(folder['name'], add_to_folder_menu)
                # 使用functools.partial避免lambda闭包问题
                from functools import partial
                action.triggered.connect(
                    partial(self._add_files_to_folder, file_ids, folder['id'])
                )
                add_to_folder_menu.addAction(action)

            # 添加分隔符和"新建文件夹"选项
            add_to_folder_menu.addSeparator()
            new_folder_action = QAction("+ 新建文件夹", add_to_folder_menu)
            new_folder_action.triggered.connect(
                partial(self._create_folder_and_add_files, file_ids)
            )
            add_to_folder_menu.addAction(new_folder_action)

            return add_to_folder_menu

        except Exception as e:
            print(f"创建添加到文件夹菜单失败: {e}")
            return None

    def _add_files_to_folder(self, file_ids, folder_tag_id):
        """将文件添加到指定文件夹（不移出中转状态）

        Args:
            file_ids: 文件ID列表
            folder_tag_id: 文件夹标签ID
        """
        try:
            # 获取主窗口和服务
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window:
                return

            # 批量添加标签关联（不移出中转状态）
            success_count = 0
            for file_id in file_ids:
                if main_window.tag_service.add_tag_to_file(file_id, folder_tag_id):
                    success_count += 1

            # 刷新视图
            if hasattr(main_window, '_load_files_silently'):
                main_window._load_files_silently()

            # 显示结果
            if success_count > 0:
                folder_tag = main_window.tag_service.get_tag_by_id(folder_tag_id)
                folder_name = folder_tag['name'] if folder_tag else '文件夹'
                main_window.show_status_message(
                    f"成功将 {success_count}/{len(file_ids)} 个文件添加到 {folder_name}",
                    True
                )
            else:
                main_window.show_status_message("添加文件失败", False)

        except Exception as e:
            print(f"添加文件到文件夹失败: {e}")
            # 获取主窗口显示错误
            main_window = self
            while main_window and not hasattr(main_window, 'show_status_message'):
                main_window = main_window.parent()
            if main_window:
                main_window.show_status_message(f"添加文件失败: {e}", False)

    def _create_folder_and_add_files(self, file_ids):
        """创建新文件夹并添加文件（不移出中转状态）

        Args:
            file_ids: 文件ID列表
        """
        try:
            from PySide6.QtWidgets import QInputDialog, QMessageBox

            # 获取主窗口
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window:
                return

            # 输入文件夹名称
            folder_name, ok = QInputDialog.getText(
                self, "新建文件夹", "请输入文件夹名称:"
            )

            if not ok or not folder_name.strip():
                return

            folder_name = folder_name.strip()

            # 创建文件夹标签
            folder_tag_id = main_window.tag_service.create_folder_tag(folder_name)

            # 添加文件到新文件夹
            self._add_files_to_folder(file_ids, folder_tag_id)

            # 刷新导航面板
            if hasattr(main_window, 'navigation_panel'):
                main_window.navigation_panel.refresh_folder_tree()

        except Exception as e:
            print(f"创建文件夹并添加文件失败: {e}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"创建文件夹失败: {e}")
