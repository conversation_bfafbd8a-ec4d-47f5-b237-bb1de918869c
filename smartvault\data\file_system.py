"""
文件系统操作模块
"""

import os
import shutil
from datetime import datetime
from smartvault.utils.config import load_config


class FileSystem:
    """文件系统操作类"""

    def __init__(self, library_path=None):
        """初始化文件系统

        Args:
            library_path: 智能文件库路径，如果为None则使用默认路径
        """
        if library_path is None:
            config = load_config()
            library_path = config["library_path"]

        self.library_path = library_path

        # 确保文件库目录存在
        os.makedirs(library_path, exist_ok=True)

        # 创建警告文件
        self._create_warning_file()

    def _create_warning_file(self):
        """创建警告文件，提醒用户不要删除文件库"""
        warning_file_path = os.path.join(self.library_path, "警告此文件夹不可删除.txt")

        # 如果文件已存在，则不重新创建
        if os.path.exists(warning_file_path):
            return

        warning_content = """
SmartVault 智能文件库 - 重要说明

本文件夹是 SmartVault 智能文件管理系统的文件库，包含以下重要内容：

1. data 目录：存储所有文件索引和元数据信息的数据库文件
2. 文件存储区：存储通过"复制到智能文件库"或"移动到智能文件库"方式添加的文件

警告：请勿直接删除此文件夹！这将导致：
- 所有文件索引和标签信息丢失
- 所有存储在文件库中的文件丢失
- 应用程序无法正常工作

如需移动文件库位置：
请使用 SmartVault 应用程序中的"工具 > 设置 > 智能文件库设置"中的"移动智能文件库"功能。
此功能会安全地将所有数据迁移到新位置，并更新系统配置。

如需删除文件库：
请使用 SmartVault 应用程序中的"工具 > 设置 > 智能文件库设置"中的"删除智能文件库"功能。
此功能会安全地清理数据，并提供数据备份选项。

技术支持：
如有任何问题，请联系技术支持。

SmartVault 团队
"""

        try:
            with open(warning_file_path, "w", encoding="utf-8") as f:
                f.write(warning_content)
        except Exception as e:
            print(f"创建警告文件失败: {e}")

    def file_exists(self, path):
        """检查文件是否存在

        Args:
            path: 文件路径

        Returns:
            bool: 文件是否存在
        """
        return os.path.isfile(path)

    def get_file_info(self, path):
        """获取文件信息

        Args:
            path: 文件路径

        Returns:
            dict: 文件信息字典

        Raises:
            FileNotFoundError: 文件不存在
        """
        if not self.file_exists(path):
            raise FileNotFoundError(f"文件不存在: {path}")

        stat = os.stat(path)

        return {
            "size": stat.st_size,
            "created_at": datetime.fromtimestamp(stat.st_ctime),
            "modified_at": datetime.fromtimestamp(stat.st_mtime),
            "is_file": True
        }

    def copy_file(self, source, target, overwrite=False):
        """复制文件

        Args:
            source: 源文件路径
            target: 目标文件路径
            overwrite: 是否覆盖已存在的文件

        Returns:
            str: 目标文件路径

        Raises:
            FileNotFoundError: 源文件不存在
            FileExistsError: 目标文件已存在且不允许覆盖
        """
        if not self.file_exists(source):
            raise FileNotFoundError(f"源文件不存在: {source}")

        if self.file_exists(target) and not overwrite:
            raise FileExistsError(f"目标文件已存在: {target}")

        # 确保目标目录存在
        os.makedirs(os.path.dirname(target), exist_ok=True)

        # 复制文件
        shutil.copy2(source, target)

        return target

    def move_file(self, source, target, overwrite=False):
        """移动文件

        Args:
            source: 源文件路径
            target: 目标文件路径
            overwrite: 是否覆盖已存在的文件

        Returns:
            str: 目标文件路径

        Raises:
            FileNotFoundError: 源文件不存在
            FileExistsError: 目标文件已存在且不允许覆盖
        """
        if not self.file_exists(source):
            raise FileNotFoundError(f"源文件不存在: {source}")

        if self.file_exists(target) and not overwrite:
            raise FileExistsError(f"目标文件已存在: {target}")

        # 确保目标目录存在
        os.makedirs(os.path.dirname(target), exist_ok=True)

        # 移动文件
        shutil.move(source, target)

        return target

    def move_library(self, new_parent_path, progress_callback=None):
        """移动智能文件库到新位置

        Args:
            new_parent_path: 新的父目录路径，将在此目录下创建SmartVault_Lib
            progress_callback: 进度回调函数，接收参数(current, total, status_message)

        Returns:
            bool: 是否成功移动

        Raises:
            ValueError: 新路径无效
            PermissionError: 无权限访问新路径
        """
        # 检查父目录是否有效
        if not os.path.exists(new_parent_path):
            try:
                os.makedirs(new_parent_path, exist_ok=True)
            except Exception as e:
                raise ValueError(f"无法创建父目录: {e}")

        # 检查父目录是否可写
        if not os.access(new_parent_path, os.W_OK):
            raise PermissionError("无权限写入父目录")

        # 在父目录下创建SmartVault_Lib目录
        library_name = "SmartVault_Lib"
        new_path = os.path.join(new_parent_path, library_name)
        
        # 检查目标文件库目录是否已存在
        if os.path.exists(new_path):
            raise ValueError(f"目标位置已存在SmartVault_Lib目录: {new_path}")
        
        # 创建目标文件库目录
        try:
            os.makedirs(new_path)
        except Exception as e:
            raise ValueError(f"无法创建目标文件库目录: {e}")

        # 获取文件库中的所有文件和目录
        items = []
        for root, dirs, files in os.walk(self.library_path):
            for dir_name in dirs:
                rel_path = os.path.relpath(os.path.join(root, dir_name), self.library_path)
                items.append(("dir", rel_path))

            for file_name in files:
                rel_path = os.path.relpath(os.path.join(root, file_name), self.library_path)
                items.append(("file", rel_path))

        total_items = len(items)

        # 首先创建所有目录
        for i, (_, rel_path) in enumerate([item for item in items if item[0] == "dir"]):
            if progress_callback:
                progress_callback(i, total_items, f"创建目录: {rel_path}")

            target_path = os.path.join(new_path, rel_path)
            os.makedirs(target_path, exist_ok=True)

        # 然后复制所有文件
        for i, (_, rel_path) in enumerate([item for item in items if item[0] == "file"]):
            if progress_callback:
                progress_callback(i + len([item for item in items if item[0] == "dir"]),
                                 total_items,
                                 f"复制文件: {rel_path}")

            source_path = os.path.join(self.library_path, rel_path)
            target_path = os.path.join(new_path, rel_path)

            try:
                shutil.copy2(source_path, target_path)
            except Exception as e:
                print(f"复制文件失败: {rel_path} - {e}")

        # 保存旧文件库路径用于清理
        old_library_path = self.library_path
        
        # 更新文件库路径
        self.library_path = new_path

        if progress_callback:
            progress_callback(total_items, total_items, "移动完成，更新配置...")

        # 更新配置
        from smartvault.utils.config import load_config, save_config, normalize_path
        config = load_config()
        config["library_path"] = normalize_path(new_path)
        save_config(config)

        # 删除旧文件库目录
        if progress_callback:
            progress_callback(total_items, total_items, "清理旧文件库...")
        
        try:
            # 确保所有文件句柄都已关闭
            import gc
            gc.collect()
            
            # 删除旧文件库目录
            import time
            time.sleep(0.5)  # 短暂等待确保文件句柄释放
            shutil.rmtree(old_library_path)
            
            if progress_callback:
                progress_callback(total_items, total_items, "移动完成")
        except Exception as e:
            print(f"警告：无法删除旧文件库目录 {old_library_path}: {e}")
            if progress_callback:
                progress_callback(total_items, total_items, f"移动完成，但旧目录清理失败: {e}")

        return True

    def delete_library(self, delete_files=False):
        """删除智能文件库

        Args:
            delete_files: 是否删除文件库中的文件

        Returns:
            bool: 是否成功删除
        """
        if delete_files:
            try:
                # 删除整个文件库目录
                shutil.rmtree(self.library_path)
            except Exception as e:
                print(f"删除文件库失败: {e}")
                return False
        else:
            # 只删除数据库文件，保留用户文件
            data_dir = os.path.join(self.library_path, "data")
            if os.path.exists(data_dir):
                try:
                    shutil.rmtree(data_dir)
                except Exception as e:
                    print(f"删除数据库文件失败: {e}")
                    return False

        return True

    def create_library(self, parent_path):
        """创建新的智能文件库

        Args:
            parent_path: 父目录路径，智能文件库将在此目录下创建

        Returns:
            tuple: (是否成功创建, 文件库路径, 详细信息)

        Raises:
            ValueError: 路径无效
            PermissionError: 无权限访问路径
        """
        # 检查父目录是否有效
        if not os.path.exists(parent_path):
            try:
                os.makedirs(parent_path, exist_ok=True)
            except Exception as e:
                raise ValueError(f"无法创建父目录: {e}")

        # 检查父目录是否可写
        if not os.access(parent_path, os.W_OK):
            raise PermissionError("无权限写入父目录")

        # 在父目录下创建智能文件库目录
        library_name = "SmartVault_Lib"
        library_path = os.path.join(parent_path, library_name)

        # 检查文件库目录是否已存在
        if os.path.exists(library_path):
            # 如果已存在，检查是否为有效的智能文件库
            data_dir = os.path.join(library_path, "data")
            warning_file = os.path.join(library_path, "警告此文件夹不可删除.txt")

            if os.path.exists(data_dir) and os.path.exists(warning_file):
                return (False, library_path, "智能文件库已存在于此位置")
            else:
                # 目录存在但不是有效的智能文件库，尝试创建
                try:
                    # 创建data目录
                    os.makedirs(data_dir, exist_ok=True)
                except Exception as e:
                    return (False, library_path, f"无法创建数据目录: {e}")
        else:
            # 创建新的文件库目录
            try:
                os.makedirs(library_path)
            except Exception as e:
                return (False, library_path, f"无法创建智能文件库目录: {e}")

        # 更新文件库路径
        self.library_path = library_path

        # 创建data目录
        data_dir = os.path.join(library_path, "data")
        os.makedirs(data_dir, exist_ok=True)

        # 创建警告文件
        self._create_warning_file()

        # 更新配置
        from smartvault.utils.config import load_config, save_config, normalize_path
        config = load_config()
        config["library_path"] = normalize_path(library_path)
        save_config(config)

        # 收集详细信息
        details = (
            f"智能文件库已成功创建！\n\n"
            f"位置: {library_path}\n"
            f"父目录: {parent_path}\n"
            f"数据目录: {data_dir}\n\n"
            f"智能文件库已准备就绪，可以开始添加文件。"
        )

        return (True, library_path, details)

    def validate_library(self, library_path):
        """验证是否为有效的SmartVault文件库

        Args:
            library_path: 文件库路径

        Returns:
            bool: 是否为有效文件库
        """
        from smartvault.services.library_config_service import get_library_config_service
        service = get_library_config_service()
        return service.validate_library(library_path)

    def get_library_stats(self):
        """获取文件库统计信息

        Returns:
            dict: 统计信息字典
        """
        # 获取文件库路径
        library_path = self.library_path

        # 获取文件库大小
        total_size = 0
        file_count = 0

        try:
            for root, _, files in os.walk(library_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.isfile(file_path):
                        file_count += 1
                        total_size += os.path.getsize(file_path)
        except Exception as e:
            print(f"获取文件库统计信息失败: {e}")

        return {
            "library_path": library_path,
            "total_size": total_size,
            "total_files": file_count,
            "entry_type_stats": {"link": 0, "copy": 0, "move": 0}
        }