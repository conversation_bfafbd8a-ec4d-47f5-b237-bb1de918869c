#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI设置页面组件功能测试脚本

全面测试AI设置页面各组件的功能和协作
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager
from smartvault.services.ai.ai_manager import AIManager
from smartvault.utils.config import load_config
from smartvault.services.library_config_service import get_library_config_service

def test_ai_config_manager():
    """测试AI配置管理器"""
    print("\n=== 测试AI配置管理器 ===")
    
    try:
        manager = AIConfigManager()
        
        # 测试配置加载
        print("1. 测试配置加载...")
        config = manager.load_ai_config()
        print(f"   ✅ 配置加载成功: enabled={config.get('enabled')}, stage={config.get('stage')}")
        print(f"   功能配置: {list(config.get('features', {}).keys())}")
        
        # 测试配置验证
        print("\n2. 测试配置验证...")
        is_valid, error_msg = manager.validate_ai_config(config)
        if is_valid:
            print("   ✅ 配置验证通过")
        else:
            print(f"   ❌ 配置验证失败: {error_msg}")
        
        # 测试配置保存
        print("\n3. 测试配置保存...")
        success = manager.save_ai_config(config)
        if success:
            print("   ✅ 配置保存成功")
        else:
            print("   ❌ 配置保存失败")
        
        return True
        
    except Exception as e:
        print(f"   ❌ AI配置管理器测试失败: {e}")
        return False

def test_ai_manager_integration():
    """测试AI管理器集成"""
    print("\n=== 测试AI管理器集成 ===")
    
    try:
        # 初始化AI管理器
        print("1. 初始化AI管理器...")
        ai_manager = AIManager()
        
        # 测试状态获取
        print("\n2. 测试状态获取...")
        status = ai_manager.get_status()
        print(f"   AI状态: enabled={status.get('enabled')}, stage={status.get('stage')}")
        print(f"   初始化状态: {status.get('status')}")
        print(f"   功能状态: {status.get('features', {})}")
        
        if status.get('last_error'):
            print(f"   ⚠️ 最后错误: {status.get('last_error')}")
        
        # 测试可用性检查
        print("\n3. 测试可用性检查...")
        is_available = ai_manager.is_available()
        print(f"   AI可用性: {'✅ 可用' if is_available else '❌ 不可用'}")
        
        # 测试标签建议功能
        print("\n4. 测试标签建议功能...")
        test_file = {
            'name': 'test.py',
            'extension': '.py',
            'path': '/test/test.py',
            'size': 1024
        }
        
        suggestions = ai_manager.suggest_tags(test_file)
        if suggestions:
            print(f"   ✅ 标签建议成功: {suggestions[:5]}")
        else:
            print("   ⚠️ 未生成标签建议")
        
        # 测试学习统计
        print("\n5. 测试学习统计...")
        learning_stats = ai_manager.get_learning_statistics()
        if learning_stats:
            print(f"   ✅ 学习统计获取成功: {list(learning_stats.keys())}")
            if 'total_feedback' in learning_stats:
                print(f"   总反馈数: {learning_stats['total_feedback']}")
        else:
            print("   ⚠️ 学习统计为空")
        
        return True
        
    except Exception as e:
        print(f"   ❌ AI管理器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_manager_with_ai_manager():
    """测试配置管理器与AI管理器的协作"""
    print("\n=== 测试配置管理器与AI管理器协作 ===")
    
    try:
        # 创建配置管理器和AI管理器
        config_manager = AIConfigManager()
        ai_manager = AIManager()
        
        # 设置AI管理器引用
        print("1. 设置AI管理器引用...")
        config_manager.set_ai_manager(ai_manager)
        print("   ✅ AI管理器引用设置成功")
        
        # 测试状态获取
        print("\n2. 通过配置管理器获取AI状态...")
        status = config_manager.get_ai_status()
        print(f"   AI状态: {status.get('enabled')}, 阶段: {status.get('stage')}")
        
        # 测试学习统计
        print("\n3. 通过配置管理器获取学习统计...")
        learning_stats = config_manager.get_learning_statistics()
        if learning_stats:
            print(f"   ✅ 学习统计: {list(learning_stats.keys())}")
        else:
            print("   ⚠️ 学习统计为空")
        
        # 测试反馈统计
        print("\n4. 测试反馈统计...")
        feedback_stats = config_manager.get_feedback_statistics()
        print(f"   反馈统计: {feedback_stats}")
        
        # 测试AI功能测试
        print("\n5. 测试AI功能测试...")
        success, message = config_manager.test_ai_function()
        if success:
            print(f"   ✅ AI功能测试成功: {message}")
        else:
            print(f"   ❌ AI功能测试失败: {message}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置管理器与AI管理器协作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_library_config_integration():
    """测试用户库配置集成"""
    print("\n=== 测试用户库配置集成 ===")
    
    try:
        # 获取当前库路径
        global_config = load_config()
        library_path = global_config.get("library_path", "")
        
        if not library_path:
            print("   ⚠️ 未设置用户库路径，跳过用户库配置测试")
            return True
        
        print(f"1. 当前用户库路径: {library_path}")
        
        # 测试用户库配置服务
        print("\n2. 测试用户库配置服务...")
        library_service = get_library_config_service(library_path)
        library_config = library_service.load_library_config()
        
        ai_config = library_config.get("ai", {})
        if ai_config:
            print(f"   ✅ 用户库AI配置存在: enabled={ai_config.get('enabled')}")
            print(f"   功能配置: {list(ai_config.get('features', {}).keys())}")
        else:
            print("   ⚠️ 用户库AI配置为空")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 用户库配置集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("AI设置页面组件功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_ai_config_manager())
    test_results.append(test_ai_manager_integration())
    test_results.append(test_config_manager_with_ai_manager())
    test_results.append(test_library_config_integration())
    
    # 汇总测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    test_names = [
        "AI配置管理器",
        "AI管理器集成", 
        "配置管理器与AI管理器协作",
        "用户库配置集成"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(test_results)} 项测试通过")
    
    if passed == len(test_results):
        print("\n🎉 所有测试通过！AI设置页面组件功能正常。")
    else:
        print(f"\n⚠️ 有 {len(test_results) - passed} 项测试失败，需要修复。")
    
    return passed == len(test_results)

if __name__ == "__main__":
    main()