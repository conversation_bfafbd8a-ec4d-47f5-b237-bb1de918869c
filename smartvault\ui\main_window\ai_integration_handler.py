# -*- coding: utf-8 -*-
"""
AI功能集成处理器
负责处理AI标签建议、用户反馈等AI相关功能
"""

from PySide6.QtCore import QTimer


class AIIntegrationHandler:
    """AI功能集成处理器"""

    def __init__(self, main_window):
        """初始化AI集成处理器
        
        Args:
            main_window: 主窗口实例
        """
        self.main_window = main_window
        self.ai_manager = None
        
        # 从主窗口获取服务引用
        self.file_service = main_window.file_service
        self.tag_service = main_window.tag_service
        self.auto_tag_service = main_window.auto_tag_service
        
    def initialize_ai_manager(self):
        """初始化AI管理器"""
        try:
            print("开始初始化AI管理器...")

            # 加载配置
            from smartvault.utils.config import load_config
            config = load_config()

            # 初始化AI管理器，传入必要的服务引用
            success = self.main_window.ai_manager.initialize(
                config=config,
                tag_service=self.tag_service,
                auto_tag_service=self.auto_tag_service,
                db=self.file_service.db if hasattr(self.file_service, 'db') else None
            )

            if success:
                print("✅ AI管理器初始化成功")
                status = self.main_window.ai_manager.get_status()
                print(f"AI状态: 启用={status['enabled']}, 阶段={status['stage']}, 状态={status['status']}")
            else:
                print("⚠️ AI管理器初始化失败")

        except Exception as e:
            print(f"AI管理器初始化异常: {e}")
            import traceback
            traceback.print_exc()
            
    def update_ai_tag_suggestions(self, file_id):
        """更新AI标签建议面板

        Args:
            file_id: 文件ID
        """
        try:
            if not file_id or not hasattr(self.main_window, 'ai_tag_panel'):
                return

            # 获取文件信息
            file_info = self.file_service.get_file_by_id(file_id)
            if not file_info:
                return

            file_name = file_info.get('name', '未知文件')

            # 设置文件选择到AI面板（先显示加载状态）
            self.main_window.ai_tag_panel.set_file_selection(file_id, file_name)

            # 检查AI管理器是否可用
            if not self.main_window.ai_manager or not self.main_window.ai_manager.is_available():
                self.main_window.ai_tag_panel.show_no_suggestions()
                return

            # 准备文件信息用于AI分析
            ai_file_info = {
                'name': file_info.get('name', ''),
                'path': file_info.get('original_path', ''),
                'extension': self._get_file_extension(file_info.get('name', '')),
                'full_name': file_info.get('name', ''),
                'folder_path': self._get_folder_path(file_info.get('original_path', ''))
            }

            # 异步获取AI建议（避免阻塞UI）
            QTimer.singleShot(100, lambda: self._fetch_ai_suggestions(ai_file_info))

        except Exception as e:
            print(f"更新AI标签建议失败: {e}")
            if hasattr(self.main_window, 'ai_tag_panel'):
                self.main_window.ai_tag_panel.show_no_suggestions()

    def _fetch_ai_suggestions(self, file_info):
        """获取AI标签建议

        Args:
            file_info: 文件信息字典
        """
        try:
            # 获取AI建议
            suggestions = self.main_window.ai_manager.suggest_tags(file_info)

            if suggestions:
                # 转换为面板需要的格式 [(tag_name, confidence), ...]
                # 目前AI管理器返回的是标签名列表，我们给一个默认可信度
                formatted_suggestions = []
                for tag_name in suggestions:
                    # 根据标签类型设置不同的可信度
                    confidence = self._calculate_tag_confidence(tag_name, file_info)
                    formatted_suggestions.append((tag_name, confidence))

                # 按可信度排序
                formatted_suggestions.sort(key=lambda x: x[1], reverse=True)

                self.main_window.ai_tag_panel.show_suggestions(formatted_suggestions)
            else:
                self.main_window.ai_tag_panel.show_no_suggestions()

        except Exception as e:
            print(f"获取AI标签建议失败: {e}")
            self.main_window.ai_tag_panel.show_no_suggestions()

    def _calculate_tag_confidence(self, tag_name, file_info):
        """计算标签可信度

        Args:
            tag_name: 标签名称
            file_info: 文件信息

        Returns:
            float: 可信度 (0.0-1.0)
        """
        # 基础可信度
        confidence = 0.7

        # 根据文件扩展名提高可信度
        extension = file_info.get('extension', '').lower()
        if extension:
            # 如果标签与文件类型相关，提高可信度
            type_tags = {
                '.py': ['Python', '代码', '脚本'],
                '.js': ['JavaScript', '代码', '脚本'],
                '.pdf': ['PDF', '文档'],
                '.jpg': ['图片', '照片'],
                '.png': ['图片', '照片'],
                '.mp4': ['视频', '媒体'],
                '.mp3': ['音频', '媒体'],
                '.doc': ['文档', 'Word'],
                '.txt': ['文本', '文档']
            }

            if extension in type_tags and tag_name in type_tags[extension]:
                confidence = min(0.95, confidence + 0.2)

        # 根据标签名称特征调整可信度
        if tag_name.startswith('📁'):
            confidence = min(0.9, confidence + 0.1)  # 文件夹标签通常比较准确
        elif len(tag_name) <= 3:
            confidence = max(0.5, confidence - 0.1)  # 短标签可能不够具体

        return confidence

    def _get_file_extension(self, filename):
        """获取文件扩展名"""
        import os
        return os.path.splitext(filename)[1].lower()

    def _get_folder_path(self, file_path):
        """获取文件所在文件夹路径"""
        import os
        if file_path:
            return os.path.dirname(file_path)
        return ""

    def on_ai_tag_accepted(self, file_id, tag_name):
        """处理AI标签接受事件

        Args:
            file_id: 文件ID
            tag_name: 标签名称
        """
        try:
            print(f"🤖 接受AI标签建议: 文件 {file_id} -> 标签 '{tag_name}'")

            # 检查标签是否已存在，如果不存在则创建
            existing_tag = self.tag_service.get_tag_by_name(tag_name)
            if not existing_tag:
                # 创建新标签
                tag_id = self.tag_service.create_tag(tag_name)
                print(f"✅ 创建新标签: {tag_name} (ID: {tag_id})")
            else:
                tag_id = existing_tag['id']
                print(f"✅ 使用现有标签: {tag_name} (ID: {tag_id})")

            # 将标签添加到文件
            success = self.tag_service.add_tag_to_file(file_id, tag_id)

            if success:
                self.main_window.show_status_message(f"已添加标签 '{tag_name}' 到文件", True)

                # 刷新导航面板标签列表
                if hasattr(self.main_window.navigation_panel, 'refresh_tags'):
                    self.main_window.navigation_panel.refresh_tags()

                # 记录用户反馈到AI统计
                if hasattr(self.main_window, 'ai_manager') and self.main_window.ai_manager:
                    from datetime import datetime
                    analysis_id = f"tag_suggestion_{file_id}_{datetime.now().strftime('%Y%m%d')}"
                    self.main_window.ai_manager.record_feedback(analysis_id, "accepted", tag_name)

                print(f"✅ 成功添加AI建议标签: {tag_name}")
            else:
                self.main_window.show_status_message(f"添加标签 '{tag_name}' 失败", False)
                print(f"❌ 添加AI建议标签失败: {tag_name}")

        except Exception as e:
            print(f"处理AI标签接受事件失败: {e}")
            self.main_window.show_status_message(f"添加标签失败: {e}", False)

    def on_ai_tag_rejected(self, file_id, tag_name):
        """处理AI标签拒绝事件

        Args:
            file_id: 文件ID
            tag_name: 标签名称
        """
        try:
            print(f"🤖 拒绝AI标签建议: 文件 {file_id} -> 标签 '{tag_name}'")

            # 可以在这里记录用户的拒绝行为，用于改进AI建议
            # TODO: 实现用户反馈学习机制
            
            # 记录用户反馈到AI统计
            if hasattr(self.main_window, 'ai_manager') and self.main_window.ai_manager:
                from datetime import datetime
                analysis_id = f"tag_suggestion_{file_id}_{datetime.now().strftime('%Y%m%d')}"
                self.main_window.ai_manager.record_feedback(analysis_id, "rejected", tag_name)

            self.main_window.show_status_message(f"已拒绝标签建议 '{tag_name}'", True)

        except Exception as e:
            print(f"处理AI标签拒绝事件失败: {e}")