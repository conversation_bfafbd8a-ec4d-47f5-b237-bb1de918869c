#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动标签功能修复测试脚本
测试编辑规则保存和高级规则添加功能
"""

import sys
import os
import tempfile
import json
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.auto_tag_service import AutoTagService, AutoTagRule, ConditionType, ConditionGroup, Condition, LogicOperator
from smartvault.utils.config import load_config, save_config

def test_auto_tag_service_save():
    """测试自动标签服务的保存功能"""
    print("\n=== 测试自动标签服务保存功能 ===")
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_config_path = f.name
        initial_config = {
            "auto_tags": {
                "enabled": True,
                "enable_ai": False,
                "rules": []
            }
        }
        json.dump(initial_config, f, ensure_ascii=False, indent=4)
    
    try:
        # 备份原配置路径
        from smartvault.utils import config as config_module
        original_get_config_path = config_module.get_config_path
        config_module.get_config_path = lambda: temp_config_path
        
        # 创建服务实例
        service = AutoTagService()
        service.load_rules_from_config(initial_config)
        
        # 测试添加简单规则
        print("1. 测试添加简单规则...")
        simple_rule = AutoTagRule(
            id="test_rule_1",
            name="测试规则1",
            condition_type=ConditionType.FILE_EXTENSION,
            condition_value=".txt",
            tag_names=["文档", "文本"],
            enabled=True,
            priority=1
        )
        service.add_rule(simple_rule)
        
        # 验证配置文件是否更新
        with open(temp_config_path, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        
        assert len(saved_config["auto_tags"]["rules"]) == 1
        assert saved_config["auto_tags"]["rules"][0]["name"] == "测试规则1"
        print("✓ 简单规则添加和保存成功")
        
        # 测试添加高级规则（多条件）
        print("2. 测试添加高级规则...")
        condition1 = Condition(type=ConditionType.FILE_EXTENSION, value=".pdf")
        condition2 = Condition(type=ConditionType.FILE_SIZE, value=">1MB")
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[condition1, condition2]
        )
        
        advanced_rule = AutoTagRule(
            id="test_rule_2",
            name="测试高级规则",
            condition_group=condition_group,
            tag_names=["大文档", "PDF"],
            enabled=True,
            priority=2
        )
        service.add_rule(advanced_rule)
        
        # 验证配置文件是否更新
        with open(temp_config_path, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        
        assert len(saved_config["auto_tags"]["rules"]) == 2
        advanced_rule_config = None
        for rule_config in saved_config["auto_tags"]["rules"]:
            if rule_config["name"] == "测试高级规则":
                advanced_rule_config = rule_config
                break
        
        assert advanced_rule_config is not None
        assert "condition_group" in advanced_rule_config
        assert advanced_rule_config["condition_group"]["operator"] == "and"
        assert len(advanced_rule_config["condition_group"]["conditions"]) == 2
        print("✓ 高级规则添加和保存成功")
        
        # 测试更新规则
        print("3. 测试更新规则...")
        simple_rule.name = "更新后的测试规则1"
        simple_rule.tag_names = ["文档", "文本", "更新"]
        service.update_rule(simple_rule.id, simple_rule)
        
        # 验证配置文件是否更新
        with open(temp_config_path, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        
        updated_rule_config = None
        for rule_config in saved_config["auto_tags"]["rules"]:
            if rule_config["id"] == "test_rule_1":
                updated_rule_config = rule_config
                break
        
        assert updated_rule_config is not None
        assert updated_rule_config["name"] == "更新后的测试规则1"
        assert "更新" in updated_rule_config["tag_names"]
        print("✓ 规则更新和保存成功")
        
        # 测试加载配置
        print("4. 测试加载配置...")
        new_service = AutoTagService()
        new_service.load_rules_from_config(saved_config)
        
        loaded_rules = new_service.get_all_rules()
        assert len(loaded_rules) == 2
        
        # 验证简单规则
        simple_loaded = None
        advanced_loaded = None
        for rule in loaded_rules:
            if rule.id == "test_rule_1":
                simple_loaded = rule
            elif rule.id == "test_rule_2":
                advanced_loaded = rule
        
        assert simple_loaded is not None
        assert simple_loaded.name == "更新后的测试规则1"
        assert simple_loaded.condition_type == ConditionType.FILE_EXTENSION
        assert simple_loaded.condition_value == ".txt"
        
        # 验证高级规则
        assert advanced_loaded is not None
        assert advanced_loaded.name == "测试高级规则"
        assert advanced_loaded.condition_group is not None
        assert advanced_loaded.condition_group.operator == LogicOperator.AND
        assert len(advanced_loaded.condition_group.conditions) == 2
        print("✓ 配置加载成功")
        
        print("\n🎉 所有测试通过！自动标签功能修复成功！")
        
    finally:
        # 恢复原配置路径
        config_module.get_config_path = original_get_config_path
        # 清理临时文件
        try:
            os.unlink(temp_config_path)
        except:
            pass

def test_condition_group_description():
    """测试条件组描述功能"""
    print("\n=== 测试条件组描述功能 ===")
    
    # 创建测试条件组
    condition1 = Condition(type=ConditionType.FILE_EXTENSION, value=".pdf")
    condition2 = Condition(type=ConditionType.FILE_SIZE, value=">1MB")
    condition_group = ConditionGroup(
        operator=LogicOperator.AND,
        conditions=[condition1, condition2]
    )
    
    # 创建规则
    rule = AutoTagRule(
        id="test_desc_rule",
        name="描述测试规则",
        condition_group=condition_group,
        tag_names=["测试"],
        enabled=True,
        priority=1
    )
    
    try:
        # 测试获取描述
        description = rule.get_description()
        print(f"规则描述: {description}")
        assert "文件扩展名" in description
        assert "文件大小" in description
        assert "且" in description  # AND操作符
        print("✓ 条件组描述生成成功")
        
    except Exception as e:
        print(f"❌ 条件组描述测试失败: {e}")
        raise

def main():
    """主测试函数"""
    print("开始自动标签功能修复测试...")
    
    try:
        test_auto_tag_service_save()
        test_condition_group_description()
        print("\n🎉 所有测试完成！修复验证成功！")
        return True
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)