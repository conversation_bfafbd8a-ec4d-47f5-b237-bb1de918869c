# -*- coding: utf-8 -*-
"""
AI智能标签建议面板
"""

import uuid
from datetime import datetime
import logging
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QScrollArea, QFrame, QProgressBar,
                               QGroupBox, QSizePolicy)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QPalette


class TagSuggestionItem(QFrame):
    """单个标签建议项"""
    
    # 信号
    accepted = Signal(str)  # 标签被接受
    rejected = Signal(str)  # 标签被拒绝
    
    def __init__(self, tag_name, confidence=0.8, parent=None):
        """初始化标签建议项
        
        Args:
            tag_name: 标签名称
            confidence: 可信度 (0.0-1.0)
            parent: 父组件
        """
        super().__init__(parent)
        self.tag_name = tag_name
        self.confidence = confidence
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f9f9f9;
                margin: 2px;
                padding: 4px;
            }
            QFrame:hover {
                background-color: #f0f0f0;
                border-color: #bbb;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(4)
        
        # 标签名称
        self.tag_label = QLabel(self.tag_name)
        font = QFont()
        font.setBold(True)
        self.tag_label.setFont(font)
        self.tag_label.setStyleSheet("color: palette(text);")
        layout.addWidget(self.tag_label)
        
        # 可信度显示
        confidence_layout = QHBoxLayout()
        confidence_layout.setContentsMargins(0, 0, 0, 0)
        
        confidence_label = QLabel("可信度:")
        confidence_label.setStyleSheet("color: palette(mid); font-size: 11px;")
        confidence_layout.addWidget(confidence_label)
        
        # 可信度进度条
        self.confidence_bar = QProgressBar()
        self.confidence_bar.setMaximum(100)
        self.confidence_bar.setValue(int(self.confidence * 100))
        self.confidence_bar.setTextVisible(True)
        self.confidence_bar.setFormat(f"{int(self.confidence * 100)}%")
        self.confidence_bar.setMaximumHeight(16)
        
        # 根据可信度设置颜色
        if self.confidence >= 0.8:
            color = "#4CAF50"  # 绿色
        elif self.confidence >= 0.6:
            color = "#FF9800"  # 橙色
        else:
            color = "#F44336"  # 红色
            
        self.confidence_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid #ccc;
                border-radius: 3px;
                text-align: center;
                font-size: 10px;
            }}
            QProgressBar::chunk {{
                background-color: {color};
                border-radius: 2px;
            }}
        """)
        
        confidence_layout.addWidget(self.confidence_bar)
        layout.addLayout(confidence_layout)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(4)
        
        self.accept_btn = QPushButton("✓ 接受")
        self.accept_btn.setStyleSheet("""
            QPushButton {
                background-color: #a5d6a7;
                color: #2e7d32;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #81c784;
            }
            QPushButton:pressed {
                background-color: #66bb6a;
            }
        """)
        self.accept_btn.clicked.connect(lambda: (
            print(f"DEBUG: 用户点击接受标签 {self.tag_name}"),
            self.accepted.emit(self.tag_name)
        ))
        
        self.reject_btn = QPushButton("✗ 拒绝")
        self.reject_btn.clicked.connect(lambda: (
            print(f"DEBUG: 用户点击拒绝标签 {self.tag_name}"),
            self.rejected.emit(self.tag_name)
        ))
        self.reject_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffcdd2;
                color: #c62828;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #ef9a9a;
            }
            QPushButton:pressed {
                background-color: #e57373;
            }
        """)
        self.reject_btn.clicked.connect(lambda: self.rejected.emit(self.tag_name))
        
        button_layout.addWidget(self.accept_btn)
        button_layout.addWidget(self.reject_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)


class AITagSuggestionPanel(QWidget):
    """AI智能标签建议面板"""
    
    # 信号
    tag_accepted = Signal(str, str)  # (file_id, tag_name)
    tag_rejected = Signal(str, str)  # (file_id, tag_name)
    
    def __init__(self, ai_manager=None, db=None, parent=None):
        """初始化面板
        
        Args:
            ai_manager: 可选的AI管理器实例
            db: 可选的数据库连接
            parent: 父组件
        """
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.current_file_id = None
        self.current_file_name = ""
        self.suggestion_items = []
        
        # 初始化AI管理器
        if ai_manager:
            self.ai_manager = ai_manager
            if db and not ai_manager.db:
                self.logger.info("为AI管理器设置数据库连接")
                ai_manager.db = db
        else:
            from smartvault.services.ai.ai_manager import get_ai_manager
            from smartvault.utils.config import load_config
            config = load_config()
            self.ai_manager = get_ai_manager()
            self.ai_manager.initialize(config, db=db)
            
        self.logger.info(f"面板初始化完成 - AI管理器: {self.ai_manager}, 数据库连接: {self.ai_manager.db is not None}")
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setMinimumWidth(200)
        self.setMaximumWidth(300)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("🤖 智能标签建议")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 文件信息显示
        self.file_info_group = QGroupBox("当前文件")
        file_info_layout = QVBoxLayout(self.file_info_group)
        
        self.file_name_label = QLabel("未选择文件")
        self.file_name_label.setWordWrap(True)
        self.file_name_label.setStyleSheet("font-weight: bold; color: #333;")
        file_info_layout.addWidget(self.file_name_label)
        
        layout.addWidget(self.file_info_group)
        
        # 建议标签区域
        self.suggestions_group = QGroupBox("AI建议标签")
        suggestions_layout = QVBoxLayout(self.suggestions_group)
        
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 建议容器
        self.suggestions_container = QWidget()
        self.suggestions_layout = QVBoxLayout(self.suggestions_container)
        self.suggestions_layout.setContentsMargins(0, 0, 0, 0)
        self.suggestions_layout.setSpacing(4)
        
        # 默认提示
        self.no_suggestions_label = QLabel("选择文件以查看AI标签建议")
        self.no_suggestions_label.setAlignment(Qt.AlignCenter)
        self.no_suggestions_label.setStyleSheet("color: #999; font-style: italic; padding: 20px;")
        self.suggestions_layout.addWidget(self.no_suggestions_label)
        
        self.suggestions_layout.addStretch()
        
        self.scroll_area.setWidget(self.suggestions_container)
        suggestions_layout.addWidget(self.scroll_area)
        
        layout.addWidget(self.suggestions_group)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        self.accept_all_btn = QPushButton("✓ 全部接受")
        self.accept_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #a5d6a7;
                color: #2e7d32;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #81c784;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.accept_all_btn.clicked.connect(self.accept_all_suggestions)
        self.accept_all_btn.setEnabled(False)
        
        self.reject_all_btn = QPushButton("✗ 全部拒绝")
        self.reject_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffcdd2;
                color: #c62828;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ef9a9a;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.reject_all_btn.clicked.connect(self.reject_all_suggestions)
        self.reject_all_btn.setEnabled(False)
        
        batch_layout.addWidget(self.accept_all_btn)
        batch_layout.addWidget(self.reject_all_btn)
        
        layout.addLayout(batch_layout)
        
        layout.addStretch()
        
    def set_file_selection(self, file_id, file_name, suggestions=None):
        """设置当前选择的文件并显示建议
        
        Args:
            file_id: 文件ID
            file_name: 文件名
            suggestions: 标签建议列表，格式为 [(tag_name, confidence), ...]
        """
        self.current_file_id = file_id
        self.current_file_name = file_name
        
        # 更新文件信息显示
        self.file_name_label.setText(file_name)
        
        # 清除现有建议
        self.clear_suggestions()
        
        if suggestions:
            self.show_suggestions(suggestions)
        else:
            self.show_loading()
            
    def show_suggestions(self, suggestions):
        """显示标签建议
        
        Args:
            suggestions: 标签建议列表，格式为 [(tag_name, confidence), ...]
        """
        # 隐藏默认提示
        self.no_suggestions_label.hide()
        
        # 创建建议项
        for tag_name, confidence in suggestions:
            item = TagSuggestionItem(tag_name, confidence)
            item.accepted.connect(self.on_tag_accepted)
            item.rejected.connect(self.on_tag_rejected)
            
            self.suggestions_layout.insertWidget(
                self.suggestions_layout.count() - 1, item
            )
            self.suggestion_items.append(item)
            
        # 启用批量操作按钮
        self.accept_all_btn.setEnabled(len(suggestions) > 0)
        self.reject_all_btn.setEnabled(len(suggestions) > 0)
        
    def show_loading(self):
        """显示加载状态"""
        self.no_suggestions_label.setText("🔄 正在获取AI建议...")
        self.no_suggestions_label.show()
        
    def show_no_suggestions(self):
        """显示无建议状态"""
        self.no_suggestions_label.setText("暂无AI标签建议")
        self.no_suggestions_label.show()
        
    def clear_suggestions(self):
        """清除所有建议"""
        # 移除所有建议项
        for item in self.suggestion_items:
            item.setParent(None)
            item.deleteLater()
        self.suggestion_items.clear()
        
        # 显示默认提示
        self.no_suggestions_label.setText("选择文件以查看AI标签建议")
        self.no_suggestions_label.show()
        
        # 禁用批量操作按钮
        self.accept_all_btn.setEnabled(False)
        self.reject_all_btn.setEnabled(False)
        
    def on_tag_accepted(self, tag_name):
        """处理标签接受事件"""
        if self.current_file_id:
            self.tag_accepted.emit(self.current_file_id, tag_name)
            
            # 记录AI反馈到数据库
            try:
                # 优先使用已初始化的ai_manager
                if hasattr(self, 'ai_manager') and self.ai_manager:
                    ai_manager = self.ai_manager
                else:
                    from smartvault.services.ai.ai_manager import get_ai_manager
                    from smartvault.utils.config import load_config
                    config = load_config()
                    ai_manager = get_ai_manager()
                    ai_manager.initialize(config)
                
                self.logger.info(f"使用AI管理器: {ai_manager}, 数据库连接: {ai_manager.db is not None}")
                if ai_manager:
                    analysis_id = f"tag_suggestion_{self.current_file_id}_{datetime.now().strftime('%Y%m%d')}"
                    success = ai_manager.record_feedback(analysis_id, "accepted", tag_name)
                    self.logger.info(f"记录反馈结果: {'成功' if success else '失败'}")
            except Exception as e:
                import traceback
                traceback.print_exc()
            
            self.remove_suggestion_item(tag_name)
            
    def on_tag_rejected(self, tag_name):
        """处理标签拒绝事件"""
        if self.current_file_id:
            self.tag_rejected.emit(self.current_file_id, tag_name)
            
            # 记录AI反馈到数据库
            try:
                # 优先使用已初始化的ai_manager
                if hasattr(self, 'ai_manager') and self.ai_manager:
                    ai_manager = self.ai_manager
                else:
                    from smartvault.services.ai.ai_manager import get_ai_manager
                    from smartvault.utils.config import load_config
                    config = load_config()
                    ai_manager = get_ai_manager()
                    ai_manager.initialize(config)
                
                self.logger.info(f"使用AI管理器: {ai_manager}, 数据库连接: {ai_manager.db is not None}")
                if ai_manager:
                    analysis_id = f"tag_suggestion_{self.current_file_id}_{datetime.now().strftime('%Y%m%d')}"
                    success = ai_manager.record_feedback(analysis_id, "rejected", tag_name)
                    self.logger.info(f"记录反馈结果: {'成功' if success else '失败'}")
            except Exception as e:
                import traceback
                traceback.print_exc()
                
            self.remove_suggestion_item(tag_name)
            
    def remove_suggestion_item(self, tag_name):
        """移除指定的建议项"""
        for item in self.suggestion_items[:]:
            if item.tag_name == tag_name:
                item.setParent(None)
                item.deleteLater()
                self.suggestion_items.remove(item)
                break
                
        # 如果没有建议了，显示提示
        if not self.suggestion_items:
            self.show_no_suggestions()
            self.accept_all_btn.setEnabled(False)
            self.reject_all_btn.setEnabled(False)
            
    def accept_all_suggestions(self):
        """接受所有建议"""
        if self.current_file_id:
            for item in self.suggestion_items[:]:
                self.tag_accepted.emit(self.current_file_id, item.tag_name)
            self.clear_suggestions()
            
    def reject_all_suggestions(self):
        """拒绝所有建议"""
        if self.current_file_id:
            for item in self.suggestion_items[:]:
                self.tag_rejected.emit(self.current_file_id, item.tag_name)
            self.clear_suggestions()
