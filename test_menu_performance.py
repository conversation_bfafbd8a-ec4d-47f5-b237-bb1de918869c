#!/usr/bin/env python3
"""
右键菜单性能测试脚本 - 测试优化效果
"""

import time
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from smartvault.services.tag_service import TagService
from smartvault.ui.components.quick_tag_menu import QuickTagMenu
from smartvault.ui.components.note_menu import NoteMenu
from PySide6.QtWidgets import QApplication, QWidget, QMenu
from PySide6.QtCore import QObject


def test_tag_service_performance():
    """测试标签服务性能"""
    print("🔧 测试标签服务性能...")
    
    tag_service = TagService()
    
    # 测试单个文件标签查询
    start_time = time.time()
    for i in range(10):
        tags = tag_service.get_file_tags("test_file_id")
    single_query_time = time.time() - start_time
    print(f"   单个查询 10 次耗时: {single_query_time:.3f}s")
    
    # 测试批量文件标签查询
    file_ids = [f"test_file_{i}" for i in range(10)]
    start_time = time.time()
    batch_tags = tag_service.get_files_tags_batch(file_ids)
    batch_query_time = time.time() - start_time
    print(f"   批量查询 10 个文件耗时: {batch_query_time:.3f}s")
    
    # 测试标签列表查询
    start_time = time.time()
    for i in range(5):
        tags = tag_service.get_tags(parent_id=None)
    tag_list_time = time.time() - start_time
    print(f"   标签列表查询 5 次耗时: {tag_list_time:.3f}s")
    
    return {
        'single_query': single_query_time,
        'batch_query': batch_query_time,
        'tag_list': tag_list_time
    }


def test_quick_tag_menu_performance():
    """测试快速标签菜单性能"""
    print("🔧 测试快速标签菜单性能...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    parent_widget = QWidget()
    menu = QMenu(parent_widget)
    
    # 模拟文件ID
    file_ids = ["file_1", "file_2", "file_3"]
    
    # 测试菜单创建性能
    start_time = time.time()
    for i in range(5):
        quick_tag_menu = QuickTagMenu(parent_widget)
        tag_submenu = quick_tag_menu.create_menu(menu, file_ids)
    menu_creation_time = time.time() - start_time
    print(f"   菜单创建 5 次耗时: {menu_creation_time:.3f}s")
    
    return {
        'menu_creation': menu_creation_time
    }


def test_note_menu_performance():
    """测试备注菜单性能"""
    print("🔧 测试备注菜单性能...")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    parent_widget = QWidget()
    
    # 测试备注菜单创建性能
    start_time = time.time()
    for i in range(10):
        note_menu = NoteMenu(parent_widget)
        action = note_menu.create_action(None, ["file_1"])
    note_menu_time = time.time() - start_time
    print(f"   备注菜单创建 10 次耗时: {note_menu_time:.3f}s")
    
    return {
        'note_menu_creation': note_menu_time
    }


def main():
    """主测试函数"""
    print("🚀 开始右键菜单性能测试...")
    print("=" * 50)
    
    try:
        # 测试标签服务
        tag_results = test_tag_service_performance()
        print()
        
        # 测试快速标签菜单
        menu_results = test_quick_tag_menu_performance()
        print()
        
        # 测试备注菜单
        note_results = test_note_menu_performance()
        print()
        
        # 汇总结果
        print("📊 性能测试结果汇总:")
        print("=" * 50)
        print(f"标签服务:")
        print(f"  - 单个查询平均: {tag_results['single_query']/10:.4f}s")
        print(f"  - 批量查询: {tag_results['batch_query']:.4f}s")
        print(f"  - 标签列表平均: {tag_results['tag_list']/5:.4f}s")
        print()
        print(f"快速标签菜单:")
        print(f"  - 菜单创建平均: {menu_results['menu_creation']/5:.4f}s")
        print()
        print(f"备注菜单:")
        print(f"  - 菜单创建平均: {note_results['note_menu_creation']/10:.4f}s")
        print()
        
        # 性能评估
        total_menu_time = menu_results['menu_creation']/5 + note_results['note_menu_creation']/10
        if total_menu_time < 0.1:
            print("✅ 性能优秀: 右键菜单响应时间 < 100ms")
        elif total_menu_time < 0.2:
            print("🟡 性能良好: 右键菜单响应时间 < 200ms")
        else:
            print("❌ 性能需要改进: 右键菜单响应时间 > 200ms")
        
        print("=" * 50)
        print("🎯 右键菜单性能测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
