#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查全局模型文件
"""

import sys
import os

# 添加项目路径
sys.path.append('d:/PythonProjects2/SmartVault3')

from smartvault.utils.config import get_app_data_dir

def check_global_model():
    """检查全局模型文件"""
    print("=== 检查全局模型文件 ===")
    
    try:
        app_data_dir = get_app_data_dir()
        print(f"应用数据目录: {app_data_dir}")
        
        global_model_path = os.path.join(app_data_dir, 'smartvault_ml_model.pkl')
        print(f"全局模型文件路径: {global_model_path}")
        print(f"全局模型文件存在: {os.path.exists(global_model_path)}")
        
        if os.path.exists(global_model_path):
            file_size = os.path.getsize(global_model_path)
            print(f"文件大小: {file_size} bytes")
        else:
            print("全局模型文件不存在")
            
            # 检查应用数据目录下的所有文件
            if os.path.exists(app_data_dir):
                print(f"\n应用数据目录内容:")
                for item in os.listdir(app_data_dir):
                    item_path = os.path.join(app_data_dir, item)
                    if os.path.isfile(item_path):
                        print(f"  文件: {item} ({os.path.getsize(item_path)} bytes)")
                    else:
                        print(f"  目录: {item}/")
        
        # 检查旧库中的模型文件
        old_lib_path = 'D:/temp4/SmartVault_Lib'
        old_model_path = os.path.join(old_lib_path, 'ai_models', 'smartvault_ml_model.pkl')
        print(f"\n旧库模型文件路径: {old_model_path}")
        print(f"旧库模型文件存在: {os.path.exists(old_model_path)}")
        
        if os.path.exists(old_model_path):
            file_size = os.path.getsize(old_model_path)
            print(f"旧库模型文件大小: {file_size} bytes")
        
    except Exception as e:
        print(f"检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_global_model()