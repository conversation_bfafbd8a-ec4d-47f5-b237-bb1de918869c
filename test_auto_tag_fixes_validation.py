#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动标签规则编辑保存问题修复验证测试

测试修复的问题：
1. 编辑规则后内容保存但页面不刷新
2. 添加高级规则时出现'Condition' object has no attribute 'conditions'错误
3. 编辑规则时提示找不到对应规则
"""

import sys
import os
import tempfile
import json
import uuid
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from smartvault.services.auto_tag_service import (
    AutoTagService, AutoTagRule, ConditionType, ConditionGroup, Condition, LogicOperator
)


def test_auto_tag_service_integration():
    """测试自动标签服务集成功能"""
    print("\n=== 测试自动标签服务集成功能 ===")
    sys.stdout.flush()
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_config_path = f.name
        initial_config = {
            "auto_tags": {
                "enabled": True,
                "enable_ai": False,
                "rules": []
            }
        }
        json.dump(initial_config, f, ensure_ascii=False, indent=4)
    
    try:
        # 临时替换配置文件路径
        import smartvault.utils.config as config_module
        original_get_config_path = config_module.get_config_path
        config_module.get_config_path = lambda: temp_config_path
        
        # 创建自动标签服务
        service = AutoTagService()
        
        # 测试1：添加简单规则
        print("\n1. 测试添加简单规则")
        simple_rule = AutoTagRule(
            id=str(uuid.uuid4()),
            name="图片文件",
            tag_names=["图片", "媒体"],
            enabled=True,
            priority=1,
            condition_type=ConditionType.FILE_EXTENSION,
            condition_value="jpg,png,gif"
        )
        
        service.add_rule(simple_rule)
        print(f"添加简单规则成功: {simple_rule.name}")
        
        # 验证规则已保存
        rules = service.get_all_rules()
        assert len(rules) == 1, f"期望1个规则，实际{len(rules)}个"
        assert rules[0].name == "图片文件", f"规则名称不匹配: {rules[0].name}"
        print("简单规则验证通过")
        
        # 测试2：添加复杂规则（多条件）
        print("\n2. 测试添加复杂规则（多条件）")
        
        # 创建条件组
        condition1 = Condition(type=ConditionType.FILE_EXTENSION, value="pdf")
        condition2 = Condition(type=ConditionType.FILE_SIZE, value=">1MB")
        
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[condition1, condition2]
        )
        
        complex_rule = AutoTagRule(
            id=str(uuid.uuid4()),
            name="大型PDF文档",
            tag_names=["文档", "PDF", "大文件"],
            enabled=True,
            priority=2,
            condition_group=condition_group
        )
        
        service.add_rule(complex_rule)
        print(f"添加复杂规则成功: {complex_rule.name}")
        
        # 验证规则已保存
        rules = service.get_all_rules()
        assert len(rules) == 2, f"期望2个规则，实际{len(rules)}个"
        
        # 找到复杂规则
        complex_rule_found = None
        for rule in rules:
            if rule.name == "大型PDF文档":
                complex_rule_found = rule
                break
        
        assert complex_rule_found is not None, "未找到复杂规则"
        assert complex_rule_found.condition_group is not None, "复杂规则的条件组为空"
        assert len(complex_rule_found.condition_group.conditions) == 2, "条件组条件数量不正确"
        print("复杂规则验证通过")
        
        # 测试3：编辑规则
        print("\n3. 测试编辑规则")
        
        # 编辑简单规则
        edited_simple_rule = AutoTagRule(
            id=simple_rule.id,
            name="图片文件（已编辑）",
            tag_names=["图片", "媒体", "视觉"],
            enabled=True,
            priority=1,
            condition_type=ConditionType.FILE_EXTENSION,
            condition_value="jpg,png,gif,bmp"
        )
        
        success = service.update_rule(simple_rule.id, edited_simple_rule)
        assert success, "编辑简单规则失败"
        print("编辑简单规则成功")
        
        # 验证编辑结果
        rules = service.get_all_rules()
        edited_rule_found = None
        for rule in rules:
            if rule.id == simple_rule.id:
                edited_rule_found = rule
                break
        
        assert edited_rule_found is not None, "未找到编辑后的规则"
        assert edited_rule_found.name == "图片文件（已编辑）", f"规则名称未更新: {edited_rule_found.name}"
        assert "视觉" in edited_rule_found.tag_names, "标签未更新"
        assert "bmp" in edited_rule_found.condition_value, "条件值未更新"
        print("编辑规则验证通过")
        
        # 测试4：删除规则
        print("\n4. 测试删除规则")
        
        service.remove_rule(simple_rule.id)
        rules = service.get_all_rules()
        assert len(rules) == 1, f"期望1个规则，实际{len(rules)}个"
        
        # 确认删除的是正确的规则
        remaining_rule = rules[0]
        assert remaining_rule.name == "大型PDF文档", f"剩余规则不正确: {remaining_rule.name}"
        print("删除规则验证通过")
        
        # 测试5：条件对象验证
        print("\n5. 测试条件对象验证")
        
        # 测试单个条件
        single_condition = Condition(type=ConditionType.FILE_NAME_PATTERN, value="test*")
        assert hasattr(single_condition, 'type'), "Condition对象缺少type属性"
        assert hasattr(single_condition, 'value'), "Condition对象缺少value属性"
        assert not hasattr(single_condition, 'conditions'), "Condition对象不应该有conditions属性"
        print("单个条件对象验证通过")
        
        # 测试条件组
        test_condition_group = ConditionGroup(
            operator=LogicOperator.OR,
            conditions=[single_condition]
        )
        assert hasattr(test_condition_group, 'operator'), "ConditionGroup对象缺少operator属性"
        assert hasattr(test_condition_group, 'conditions'), "ConditionGroup对象缺少conditions属性"
        assert len(test_condition_group.conditions) == 1, "条件组条件数量不正确"
        print("条件组对象验证通过")
        
        print("\n=== 所有测试通过！ ===")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复原始配置路径函数
        if 'original_get_config_path' in locals():
            config_module.get_config_path = original_get_config_path
        
        # 清理临时文件
        try:
            os.unlink(temp_config_path)
        except:
            pass
    
    return True


def test_ui_integration_simulation():
    """模拟UI集成测试"""
    print("\n=== 模拟UI集成测试 ===")
    sys.stdout.flush()
    
    try:
        # 模拟设置页面的操作流程
        class MockAutoTagSettingsPage:
            def __init__(self):
                self.auto_tag_service = AutoTagService()
                self.rules_table_data = []
            
            def get_auto_tag_service(self):
                return self.auto_tag_service
            
            def load_auto_tag_rules(self):
                """模拟加载规则到表格"""
                rules = self.auto_tag_service.get_all_rules()
                self.rules_table_data = []
                
                for rule in rules:
                    if hasattr(rule, 'condition_group') and rule.condition_group:
                        # 多条件规则
                        row_data = {
                            'name': rule.name,
                            'type': '多条件',
                            'description': rule.get_description(),
                            'tags': ', '.join(rule.tag_names)
                        }
                    else:
                        # 单条件规则
                        row_data = {
                            'name': rule.name,
                            'type': rule.condition_type.value if rule.condition_type else '',
                            'description': rule.condition_value or '',
                            'tags': ', '.join(rule.tag_names)
                        }
                    self.rules_table_data.append(row_data)
                
                print(f"表格加载了 {len(self.rules_table_data)} 个规则")
            
            def add_simple_rule(self, name, condition_type, condition_value, tags):
                """模拟添加简单规则"""
                rule = AutoTagRule(
                    id=str(uuid.uuid4()),
                    name=name,
                    tag_names=tags,
                    condition_type=condition_type,
                    condition_value=condition_value
                )
                self.auto_tag_service.add_rule(rule)
                self.load_auto_tag_rules()  # 刷新表格
                return rule
            
            def add_complex_rule(self, name, condition_group, tags):
                """模拟添加复杂规则"""
                rule = AutoTagRule(
                    id=str(uuid.uuid4()),
                    name=name,
                    tag_names=tags,
                    condition_group=condition_group
                )
                self.auto_tag_service.add_rule(rule)
                self.load_auto_tag_rules()  # 刷新表格
                return rule
            
            def edit_rule(self, rule_name, new_name, new_tags):
                """模拟编辑规则"""
                rules = self.auto_tag_service.get_all_rules()
                for rule in rules:
                    if rule.name == rule_name:
                        # 创建编辑后的规则
                        edited_rule = AutoTagRule(
                            id=rule.id,
                            name=new_name,
                            tag_names=new_tags,
                            enabled=rule.enabled,
                            priority=rule.priority,
                            condition_group=rule.condition_group,
                            condition_type=rule.condition_type,
                            condition_value=rule.condition_value
                        )
                        self.auto_tag_service.update_rule(rule.id, edited_rule)
                        self.load_auto_tag_rules()  # 刷新表格
                        return True
                return False
        
        # 创建模拟页面
        page = MockAutoTagSettingsPage()
        
        # 测试添加简单规则
        print("\n1. 测试添加简单规则")
        rule1 = page.add_simple_rule(
            "文档文件",
            ConditionType.FILE_EXTENSION,
            "doc,docx,pdf",
            ["文档", "办公"]
        )
        assert len(page.rules_table_data) == 1, "表格应该有1个规则"
        assert page.rules_table_data[0]['name'] == "文档文件", "规则名称不匹配"
        print("添加简单规则测试通过")
        
        # 测试添加复杂规则
        print("\n2. 测试添加复杂规则")
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[
                Condition(type=ConditionType.FILE_EXTENSION, value="mp4,avi"),
                Condition(type=ConditionType.FILE_SIZE, value=">100MB")
            ]
        )
        rule2 = page.add_complex_rule(
            "大型视频文件",
            condition_group,
            ["视频", "媒体", "大文件"]
        )
        assert len(page.rules_table_data) == 2, "表格应该有2个规则"
        
        # 找到复杂规则
        complex_rule_row = None
        for row in page.rules_table_data:
            if row['name'] == "大型视频文件":
                complex_rule_row = row
                break
        
        assert complex_rule_row is not None, "未找到复杂规则"
        assert complex_rule_row['type'] == "多条件", f"复杂规则类型不正确: {complex_rule_row['type']}"
        print("添加复杂规则测试通过")
        
        # 测试编辑规则
        print("\n3. 测试编辑规则")
        success = page.edit_rule("文档文件", "办公文档", ["文档", "办公", "工作"])
        assert success, "编辑规则失败"
        
        # 验证编辑结果
        edited_rule_row = None
        for row in page.rules_table_data:
            if row['name'] == "办公文档":
                edited_rule_row = row
                break
        
        assert edited_rule_row is not None, "未找到编辑后的规则"
        assert "工作" in edited_rule_row['tags'], "标签未更新"
        print("编辑规则测试通过")
        
        print("\n=== UI集成模拟测试通过！ ===")
        
    except Exception as e:
        print(f"UI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    print("开始自动标签修复验证测试...")
    
    success1 = test_auto_tag_service_integration()
    success2 = test_ui_integration_simulation()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！修复验证成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！需要进一步修复！")
        sys.exit(1)