"""
数据库访问模块
"""

import os
import sqlite3
import shutil
from datetime import datetime
from smartvault.utils.config import get_app_data_dir, load_config


class Database:
    """数据库访问类"""

    # 🔧 类级别的单例实例缓存
    _instance_cache = {}

    def __init__(self, db_path=None):
        """初始化数据库

        Args:
            db_path: 数据库文件路径，如果为None则延迟初始化
        """
        self.db_path = db_path
        self.conn = None

        if db_path:
            self._init_db()

    @classmethod
    def create_from_config(cls):
        """从配置文件创建数据库实例（带缓存）"""
        config = load_config()
        library_path = config["library_path"]

        # 确保文件库目录存在
        os.makedirs(library_path, exist_ok=True)

        # 在文件库中创建data目录
        data_dir = os.path.join(library_path, "data")
        os.makedirs(data_dir, exist_ok=True)

        db_path = os.path.join(data_dir, "smartvault.db")

        # 🔧 检查缓存，避免重复创建
        if db_path in cls._instance_cache:
            cached_instance = cls._instance_cache[db_path]
            # 检查连接是否仍然有效
            if cached_instance.conn is not None:
                try:
                    # 简单测试连接
                    cached_instance.conn.execute("SELECT 1")
                    return cached_instance
                except:
                    # 连接无效，从缓存中移除
                    del cls._instance_cache[db_path]

        # 检查旧数据库是否存在，如果存在则迁移
        old_db_path = os.path.join(get_app_data_dir(), "smartvault.db")
        if os.path.exists(old_db_path) and not os.path.exists(db_path):
            try:
                shutil.copy2(old_db_path, db_path)
                print(f"数据库已从 {old_db_path} 迁移到 {db_path}")
                
                # 迁移模型文件
                old_model_path = os.path.join(get_app_data_dir(), "smartvault_ml_model.pkl")
                if os.path.exists(old_model_path):
                    new_model_dir = os.path.join(library_path, "ai_models")
                    os.makedirs(new_model_dir, exist_ok=True)
                    new_model_path = os.path.join(new_model_dir, "smartvault_ml_model.pkl")
                    shutil.copy2(old_model_path, new_model_path)
                    print(f"AI模型文件已从 {old_model_path} 迁移到 {new_model_path}")
                    
                    # 更新AI配置
                    from smartvault.services.library_config_service import get_library_config_service
                    library_service = get_library_config_service(library_path)
                    config = library_service.load_library_config()
                    if "ai" not in config:
                        config["ai"] = {}
                    if "features" not in config["ai"]:
                        config["ai"]["features"] = {}
                    if "ml_basic" not in config["ai"]["features"]:
                        config["ai"]["features"]["ml_basic"] = {}
                    config["ai"]["features"]["ml_basic"]["model_path"] = "smartvault_ml_model.pkl"
                    library_service.save_library_config(config)
                    print("已更新AI配置中的模型路径")
            except Exception as e:
                print(f"数据库迁移失败: {e}")

        # 创建新实例并缓存
        instance = cls(db_path)
        cls._instance_cache[db_path] = instance
        return instance

    def reconnect(self, new_db_path):
        """重新连接到新的数据库

        Args:
            new_db_path: 新的数据库文件路径
        """
        print(f"重新连接数据库: {self.db_path} -> {new_db_path}")

        # 关闭当前连接
        self.close()

        # 设置新路径
        self.db_path = new_db_path

        # 确保数据库目录存在
        os.makedirs(os.path.dirname(new_db_path), exist_ok=True)

        # 初始化新连接
        self._init_db()

        print(f"数据库重新连接成功: {new_db_path}")

    def _init_db(self):
        """初始化数据库连接"""
        try:
            print(f"正在初始化数据库: {self.db_path}")
            
            # 设置连接参数以避免锁定问题
            self.conn = sqlite3.connect(
                self.db_path,
                timeout=30.0,  # 30秒超时
                check_same_thread=False  # 允许多线程访问
            )
            print("数据库连接已建立")
            
            self.conn.row_factory = sqlite3.Row

            # 设置WAL模式以提高并发性能
            self.conn.execute("PRAGMA journal_mode=WAL")
            self.conn.execute("PRAGMA synchronous=NORMAL")
            self.conn.execute("PRAGMA cache_size=10000")
            self.conn.execute("PRAGMA temp_store=MEMORY")
            print("数据库参数已设置")

            # 创建表
            print("开始创建数据库表...")
            self._create_tables()
            print("数据库表创建完成")

            # 验证表是否创建成功
            cursor = self.conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"当前数据库包含的表: {tables}")

        except Exception as e:
            print(f"数据库初始化失败: {e}")
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()
                self.conn = None
            raise

    def _create_tables(self):
        """创建数据库表"""
        cursor = self.conn.cursor()

        try:
            # 创建文件表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS files (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                original_path TEXT NOT NULL,
                library_path TEXT,
                size INTEGER NOT NULL,
                created_at TIMESTAMP NOT NULL,
                modified_at TIMESTAMP NOT NULL,
                added_at TIMESTAMP NOT NULL,
                entry_type TEXT NOT NULL,
                is_available INTEGER DEFAULT 1
            )""")

            # 创建标签表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS tags (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                color TEXT,
                parent_id TEXT,
                weight INTEGER DEFAULT 5,
                created_at TIMESTAMP NOT NULL,
                FOREIGN KEY (parent_id) REFERENCES tags (id)
            )""")

            # 创建标签关联表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS tag_relations (
                id TEXT PRIMARY KEY,
                tag1_id TEXT NOT NULL,
                tag2_id TEXT NOT NULL,
                relation_type TEXT NOT NULL DEFAULT 'related',
                strength REAL DEFAULT 0.5,
                created_at TIMESTAMP NOT NULL,
                FOREIGN KEY (tag1_id) REFERENCES tags (id),
                FOREIGN KEY (tag2_id) REFERENCES tags (id),
                UNIQUE(tag1_id, tag2_id, relation_type)
            )""")

            # 创建文件标签关联表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS file_tags (
                file_id TEXT NOT NULL,
                tag_id TEXT NOT NULL,
                added_at TIMESTAMP NOT NULL,
                note TEXT,
                PRIMARY KEY (file_id, tag_id),
                FOREIGN KEY (file_id) REFERENCES files (id),
                FOREIGN KEY (tag_id) REFERENCES tags (id)
            )""")

            # 创建AI学习模式表(包含所有必要列)
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS ai_learning_patterns (
                id TEXT PRIMARY KEY,
                pattern_type TEXT NOT NULL,
                pattern_data TEXT NOT NULL,
                weight REAL DEFAULT 1.0,
                frequency INTEGER DEFAULT 1,
                success_rate REAL DEFAULT 0.8,
                last_used INTEGER DEFAULT 0,
                created_at TIMESTAMP NOT NULL,
                last_used_at TIMESTAMP
            )""")

            # 创建AI标签规则表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS ai_tag_rules (
                id TEXT PRIMARY KEY,
                rule_name TEXT NOT NULL,
                rule_condition TEXT NOT NULL,
                tag_id TEXT NOT NULL,
                confidence REAL DEFAULT 0.8,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP NOT NULL,
                FOREIGN KEY (tag_id) REFERENCES tags (id)
            )""")

            # 创建AI分析结果表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS ai_analysis (
                id TEXT PRIMARY KEY,
                file_id TEXT NOT NULL,
                analysis_type TEXT NOT NULL,  -- 'tag_suggestion', 'project_detection', 'series_detection'
                result_data TEXT NOT NULL,    -- JSON格式的分析结果
                confidence REAL,              -- 置信度 0.0-1.0
                ai_stage TEXT NOT NULL,       -- 'rule_based', 'ml_basic', 'deep_learning'
                created_at TEXT NOT NULL,
                FOREIGN KEY (file_id) REFERENCES files (id)
            )""")

            # 创建AI用户反馈表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS ai_feedback (
                id TEXT PRIMARY KEY,
                analysis_id TEXT NOT NULL,
                feedback_type TEXT NOT NULL,  -- 'accept', 'reject', 'modify'
                user_action TEXT,             -- 用户的具体操作
                feedback_data TEXT,           -- JSON格式的反馈数据
                created_at TEXT NOT NULL,
                FOREIGN KEY (analysis_id) REFERENCES ai_analysis (id)
            )""")

            # 创建AI配置表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS ai_config (
                id TEXT PRIMARY KEY,
                config_key TEXT UNIQUE NOT NULL,
                config_value TEXT NOT NULL,
                config_type TEXT NOT NULL,    -- 'global', 'library', 'user'
                description TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )""")

            self.conn.commit()

            # 执行数据库迁移
            self._migrate_database()
        except Exception as e:
            self.conn.rollback()
            raise e

    def _migrate_database(self):
        """执行数据库迁移，添加新字段"""
        cursor = self.conn.cursor()

        try:
            # 检查并添加tags表的weight字段
            cursor.execute("PRAGMA table_info(tags)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'weight' not in columns:
                cursor.execute("ALTER TABLE tags ADD COLUMN weight INTEGER DEFAULT 5")
                print("已添加tags表的weight字段")

            # 检查并添加files表的file_hash字段
            cursor.execute("PRAGMA table_info(files)")
            file_columns = [column[1] for column in cursor.fetchall()]

            if 'file_hash' not in file_columns:
                cursor.execute("ALTER TABLE files ADD COLUMN file_hash TEXT")
                print("已添加files表的file_hash字段")

            # 检查并添加files表的staging_status字段
            if 'staging_status' not in file_columns:
                cursor.execute("ALTER TABLE files ADD COLUMN staging_status TEXT DEFAULT 'normal'")
                print("已添加files表的staging_status字段")

            # 检查并添加files表的note字段（文件备注）
            if 'note' not in file_columns:
                cursor.execute("ALTER TABLE files ADD COLUMN note TEXT")
                print("已添加files表的note字段")

            # 检查并添加files表的folder_group_id字段
            if 'folder_group_id' not in file_columns:
                cursor.execute("ALTER TABLE files ADD COLUMN folder_group_id TEXT")
                print("已添加files表的folder_group_id字段")

            # 检查并添加files表的folder_name字段
            if 'folder_name' not in file_columns:
                cursor.execute("ALTER TABLE files ADD COLUMN folder_name TEXT")
                print("已添加files表的folder_name字段")

            # 检查并添加ai_learning_patterns表的缺失字段
            cursor.execute("PRAGMA table_info(ai_learning_patterns)")
            ai_columns = [column[1] for column in cursor.fetchall()]
            if 'frequency' not in ai_columns:
                cursor.execute("ALTER TABLE ai_learning_patterns ADD COLUMN frequency INTEGER DEFAULT 1")
                print("已添加ai_learning_patterns表的frequency字段")
            if 'success_rate' not in ai_columns:
                cursor.execute("ALTER TABLE ai_learning_patterns ADD COLUMN success_rate REAL DEFAULT 0.8")
                print("已添加ai_learning_patterns表的success_rate字段")
            if 'last_used' not in ai_columns:
                cursor.execute("ALTER TABLE ai_learning_patterns ADD COLUMN last_used INTEGER DEFAULT 0")
                print("已添加ai_learning_patterns表的last_used字段")

            # 检查并添加file_tags表的note字段
            cursor.execute("PRAGMA table_info(file_tags)")
            file_tag_columns = [column[1] for column in cursor.fetchall()]
            if 'note' not in file_tag_columns:
                cursor.execute("ALTER TABLE file_tags ADD COLUMN note TEXT")
                print("已添加file_tags表的note字段")

        except Exception as e:
            print(f"数据库迁移失败: {e}")

        # 🔧 创建性能优化索引
        self._create_performance_indexes(cursor)

        self.conn.commit()

    def _create_performance_indexes(self, cursor):
        """创建性能优化索引"""
        try:
            # 🔧 静默创建索引，避免重复日志输出
            # 基础索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_name ON files(name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_added_at ON files(added_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_size ON files(size)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_file_tags_file_id ON file_tags(file_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_file_tags_tag_id ON file_tags(tag_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_tags_parent_id ON tags(parent_id)")

            # 🔧 针对性能问题的专用索引
            # 中转文件夹优化索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_staging_status ON files(staging_status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_staging_folder_group ON files(staging_status, folder_group_id, folder_name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_staging_added_at ON files(staging_status, added_at)")

            # 标签查询优化索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_file_tags_composite ON file_tags(tag_id, file_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_file_tags_reverse ON file_tags(file_id, tag_id)")

            # 复合查询优化索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_entry_type ON files(entry_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_available ON files(is_available)")

        except Exception as e:
            print(f"❌ 创建性能索引失败: {e}")

    def close(self):
        """关闭数据库连接"""
        if self.conn is None:
            return  # 连接已经关闭，直接返回

        try:
            # 确保所有事务都已提交
            self.conn.commit()
            # 关闭连接
            self.conn.close()
            self.conn = None
            print("数据库连接已关闭")
        except Exception as e:
            # 设置连接为None，避免重复关闭
            self.conn = None
            print(f"关闭数据库连接时出错: {e}")

    def __del__(self):
        """析构函数，确保数据库连接被关闭"""
        # 避免在析构函数中使用print，防止重入错误
        if self.conn is not None:
            try:
                self.conn.close()
                self.conn = None
            except Exception:
                # 忽略析构函数中的错误，避免重入问题
                self.conn = None
