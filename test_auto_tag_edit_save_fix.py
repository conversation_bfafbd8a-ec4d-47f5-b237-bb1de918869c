#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动标签规则编辑保存问题修复测试

问题分析：
1. 编辑规则时，auto_tag_service.update_rule()确实会保存到配置文件
2. 但是设置页面的save_settings()方法会从表格重新读取数据并覆盖配置
3. 而load_auto_tag_rules()只是更新表格显示，但表格数据可能不完整（特别是多条件规则）
4. 当用户点击设置对话框的"确定"时，save_settings()会用表格数据覆盖之前保存的编辑结果
"""

import sys
import os
import tempfile
import json
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from smartvault.services.auto_tag_service import AutoTagService, AutoTagRule, ConditionType, ConditionGroup, Condition, LogicOperator


def test_auto_tag_edit_save_issue():
    """测试自动标签编辑保存问题"""
    print("\n=== 测试自动标签编辑保存问题 ===")
    sys.stdout.flush()
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_config_path = f.name
        initial_config = {
            "auto_tags": {
                "enabled": True,
                "enable_ai": False,
                "rules": []
            }
        }
        json.dump(initial_config, f, ensure_ascii=False, indent=4)
    
    try:
        # 备份原配置路径
        from smartvault.utils import config as config_module
        original_get_config_path = config_module.get_config_path
        config_module.get_config_path = lambda: temp_config_path
        
        # 创建自动标签服务
        service = AutoTagService()
        service.load_rules_from_config(initial_config)
        
        # 1. 添加一个多条件规则
        print("\n1. 添加多条件规则...")
        condition1 = Condition(type=ConditionType.FILE_EXTENSION, value="pdf")
        condition2 = Condition(type=ConditionType.FILE_SIZE, value=">1048576")  # 大于1MB
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[condition1, condition2]
        )
        
        original_rule = AutoTagRule(
            id=str(uuid.uuid4()),
            name="大PDF文档",
            tag_names=["文档", "PDF", "大文件"],
            enabled=True,
            priority=10,
            condition_group=condition_group
        )
        
        service.add_rule(original_rule)
        print(f"添加规则: {original_rule.name}")
        sys.stdout.flush()
        
        # 验证规则已保存
        saved_config = json.loads(Path(temp_config_path).read_text(encoding='utf-8'))
        saved_rules = saved_config.get("auto_tags", {}).get("rules", [])
        print(f"配置文件中的规则数量: {len(saved_rules)}")
        assert len(saved_rules) == 1, "规则应该已保存到配置文件"
        
        # 2. 模拟编辑规则
        print("\n2. 编辑规则...")
        edited_rule = AutoTagRule(
            id=original_rule.id,
            name="编辑后的大PDF文档",  # 修改名称
            tag_names=["文档", "PDF", "大文件", "重要"],  # 添加标签
            enabled=True,
            priority=15,  # 修改优先级
            condition_group=condition_group  # 保持条件不变
        )
        
        # 更新规则（这会保存到配置文件）
        success = service.update_rule(original_rule.id, edited_rule)
        print(f"更新规则结果: {success}")
        assert success, "规则更新应该成功"
        
        # 验证规则已更新到配置文件
        updated_config = json.loads(Path(temp_config_path).read_text(encoding='utf-8'))
        updated_rules = updated_config.get("auto_tags", {}).get("rules", [])
        print(f"更新后配置文件中的规则数量: {len(updated_rules)}")
        assert len(updated_rules) == 1, "应该仍然只有一个规则"
        
        updated_rule_data = updated_rules[0]
        print(f"更新后的规则名称: {updated_rule_data.get('name')}")
        print(f"更新后的标签: {updated_rule_data.get('tag_names')}")
        print(f"更新后的优先级: {updated_rule_data.get('priority')}")
        
        assert updated_rule_data.get('name') == "编辑后的大PDF文档", "规则名称应该已更新"
        assert "重要" in updated_rule_data.get('tag_names', []), "新标签应该已添加"
        assert updated_rule_data.get('priority') == 15, "优先级应该已更新"
        
        # 3. 模拟设置页面的save_settings行为（这是问题所在）
        print("\n3. 模拟设置页面保存行为...")
        
        # 模拟从表格读取数据（这里会丢失多条件信息）
        table_rules = []
        for rule in service.get_all_rules():
            # 这模拟了auto_tag_page.py中save_settings方法的逻辑
            # 问题：只保存了基本信息，丢失了condition_group
            table_rule = {
                "name": rule.name,
                "condition_type": rule.condition_type.value if rule.condition_type else "file_extension",
                "condition_value": rule.condition_value or "",
                "tag_names": rule.tag_names
            }
            table_rules.append(table_rule)
        
        # 模拟设置对话框保存配置（覆盖之前的保存）
        problematic_config = updated_config.copy()
        problematic_config["auto_tags"]["rules"] = table_rules
        
        # 保存这个有问题的配置
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            json.dump(problematic_config, f, ensure_ascii=False, indent=4)
        
        print("模拟设置页面保存后的配置:")
        print(json.dumps(table_rules, ensure_ascii=False, indent=2))
        
        # 4. 验证问题：多条件信息丢失
        print("\n4. 验证问题：多条件信息丢失")
        final_config = json.loads(Path(temp_config_path).read_text(encoding='utf-8'))
        final_rules = final_config.get("auto_tags", {}).get("rules", [])
        
        if final_rules:
            final_rule = final_rules[0]
            has_condition_group = "condition_group" in final_rule
            print(f"最终规则是否包含condition_group: {has_condition_group}")
            print(f"最终规则的条件类型: {final_rule.get('condition_type')}")
            print(f"最终规则的条件值: {final_rule.get('condition_value')}")
            
            if not has_condition_group:
                print("[ERROR] 问题确认：多条件信息在设置页面保存时丢失了！")
                sys.stdout.flush()
            else:
                print("[OK] 多条件信息保持完整")
                sys.stdout.flush()
        
        print("\n=== 测试完成 ===")
        return not has_condition_group if final_rules else True
        
    finally:
        # 恢复原配置路径
        config_module.get_config_path = original_get_config_path
        # 清理临时文件
        try:
            os.unlink(temp_config_path)
        except:
            pass


def analyze_auto_tag_page_save_logic():
    """分析自动标签页面保存逻辑的问题"""
    print("\n=== 分析自动标签页面保存逻辑问题 ===")
    
    print("问题分析：")
    print("1. 编辑规则时，MultiConditionAutoTagDialog.accept_rule() 创建完整的 AutoTagRule 对象")
    print("2. auto_tag_service.update_rule() 正确保存到配置文件，包含 condition_group")
    print("3. auto_tag_page.load_auto_tag_rules() 只更新表格显示，但表格只显示基本信息")
    print("4. auto_tag_page.save_settings() 从表格重新构建规则数据，丢失 condition_group")
    print("5. 设置对话框保存时用表格数据覆盖了之前正确保存的完整数据")
    
    print("\n解决方案：")
    print("1. 修改 AutoTagSettingsPage.save_settings() 方法")
    print("2. 不从表格重新构建规则，而是使用 auto_tag_service 中的完整规则数据")
    print("3. 确保设置页面保存时保持规则的完整性")


if __name__ == "__main__":
    # 运行测试
    has_issue = test_auto_tag_edit_save_issue()
    
    # 分析问题
    analyze_auto_tag_page_save_logic()
    
    if has_issue:
        print("\n[NEED_FIX] 需要修复自动标签编辑保存问题")
    else:
        print("\n[OK] 自动标签编辑保存功能正常")