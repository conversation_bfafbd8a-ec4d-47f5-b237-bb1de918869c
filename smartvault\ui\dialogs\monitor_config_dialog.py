"""
监控配置对话框
用于添加和编辑监控文件夹配置
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLabel, QLineEdit, QPushButton, QComboBox, QCheckBox, QTextEdit,
    QDialogButtonBox, QFileDialog, QMessageBox
)
from PySide6.QtCore import Qt
import os
from typing import Dict, List, Optional, Tuple


class MonitorConfigDialog(QDialog):
    """监控配置对话框"""

    def __init__(self, config: Optional[Dict] = None, parent=None):
        """初始化对话框

        Args:
            config: 现有配置（编辑模式），None表示添加模式
            parent: 父窗口
        """
        super().__init__(parent)
        self.config = config
        self.is_edit_mode = config is not None

        self.init_ui()

        if self.is_edit_mode:
            self.load_config()

    def init_ui(self):
        """初始化UI界面"""
        title = "编辑监控配置" if self.is_edit_mode else "添加监控文件夹"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout(self)

        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_layout = QFormLayout(basic_group)

        # 文件夹路径
        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("选择要监控的文件夹路径")
        path_layout.addWidget(self.path_edit)

        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self.on_browse_folder)
        path_layout.addWidget(browse_button)

        basic_layout.addRow("文件夹路径:", path_layout)

        # 入库模式
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["链接", "复制", "移动"])
        self.mode_combo.setCurrentText("链接")
        basic_layout.addRow("入库模式:", self.mode_combo)

        layout.addWidget(basic_group)

        # 文件过滤组
        filter_group = QGroupBox("监控处理以下类型文件")
        filter_layout = QVBoxLayout(filter_group)

        # 常见文件类型复选框
        common_types_layout = QVBoxLayout()
        common_types_layout.addWidget(QLabel("常见文件类型:"))
        
        # 创建复选框网格布局
        checkbox_layout = QHBoxLayout()
        
        # 第一列
        col1_layout = QVBoxLayout()
        self.image_check = QCheckBox("图片 (*.jpg, *.png, *.gif, *.bmp, *.tiff)")
        self.video_check = QCheckBox("视频 (*.mp4, *.avi, *.mkv, *.mov, *.wmv)")
        self.audio_check = QCheckBox("音频 (*.mp3, *.wav, *.flac, *.aac, *.ogg)")
        col1_layout.addWidget(self.image_check)
        col1_layout.addWidget(self.video_check)
        col1_layout.addWidget(self.audio_check)
        
        # 第二列
        col2_layout = QVBoxLayout()
        self.document_check = QCheckBox("文档 (*.pdf, *.doc, *.docx, *.txt, *.rtf)")
        self.spreadsheet_check = QCheckBox("表格 (*.xls, *.xlsx, *.csv)")
        self.archive_check = QCheckBox("压缩包 (*.zip, *.rar, *.7z, *.tar, *.gz)")
        col2_layout.addWidget(self.document_check)
        col2_layout.addWidget(self.spreadsheet_check)
        col2_layout.addWidget(self.archive_check)
        
        checkbox_layout.addLayout(col1_layout)
        checkbox_layout.addLayout(col2_layout)
        common_types_layout.addLayout(checkbox_layout)
        
        filter_layout.addLayout(common_types_layout)
        
        # 自定义文件类型模式
        filter_layout.addWidget(QLabel("自定义文件类型模式 (每行一个，如 *.txt):"))
        self.patterns_edit = QTextEdit()
        self.patterns_edit.setMaximumHeight(80)
        self.patterns_edit.setPlaceholderText("*.txt\n*.pdf\n*.doc\n*.docx")
        filter_layout.addWidget(self.patterns_edit)

        layout.addWidget(filter_group)
        
        # 连接复选框信号
        self.image_check.toggled.connect(self._update_patterns_from_checkboxes)
        self.video_check.toggled.connect(self._update_patterns_from_checkboxes)
        self.audio_check.toggled.connect(self._update_patterns_from_checkboxes)
        self.document_check.toggled.connect(self._update_patterns_from_checkboxes)
        self.spreadsheet_check.toggled.connect(self._update_patterns_from_checkboxes)
        self.archive_check.toggled.connect(self._update_patterns_from_checkboxes)

        # 选项组
        options_group = QGroupBox("监控选项")
        options_layout = QVBoxLayout(options_group)

        self.auto_add_check = QCheckBox("自动添加到文件库")
        self.auto_add_check.setChecked(True)
        self.auto_add_check.setToolTip("检测到新文件时自动添加到文件库")
        options_layout.addWidget(self.auto_add_check)

        self.recursive_check = QCheckBox("递归监控子文件夹")
        self.recursive_check.setChecked(True)
        self.recursive_check.setToolTip("监控指定文件夹及其所有子文件夹")
        options_layout.addWidget(self.recursive_check)

        # 查重选项
        self.auto_dedup_check = QCheckBox("启用自动查重")
        self.auto_dedup_check.setChecked(True)
        self.auto_dedup_check.setEnabled(True)
        self.auto_dedup_check.setToolTip("自动检测重复文件，避免重复添加到文件库")
        options_layout.addWidget(self.auto_dedup_check)

        layout.addWidget(options_group)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        button_box.accepted.connect(self.on_accept)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)

    def on_browse_folder(self):
        """浏览文件夹"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择要监控的文件夹", self.path_edit.text()
        )
        if folder:
            self.path_edit.setText(folder)

    def load_config(self):
        """加载现有配置到UI控件"""
        if not self.config:
            return

        # 文件夹路径
        self.path_edit.setText(self.config.get("folder_path", ""))

        # 入库模式
        entry_mode = self.config.get("entry_mode", "link")
        mode_map = {"link": "链接", "copy": "复制", "move": "移动"}
        mode_text = mode_map.get(entry_mode, "链接")
        self.mode_combo.setCurrentText(mode_text)

        # 文件模式
        patterns = self.config.get("file_patterns", [])
        if patterns:
            # 分析现有模式，设置对应的复选框
            self._set_checkboxes_from_patterns(patterns)
            # 设置自定义模式（排除已知的常见类型）
            custom_patterns = self._get_custom_patterns(patterns)
            if custom_patterns:
                self.patterns_edit.setPlainText("\n".join(custom_patterns))

        # 选项
        self.auto_add_check.setChecked(self.config.get("auto_add", True))
        self.recursive_check.setChecked(self.config.get("recursive", True))
        self.auto_dedup_check.setChecked(self.config.get("auto_dedup", False))

    def get_config(self) -> Dict:
        """获取配置

        Returns:
            dict: 监控配置字典
        """
        # 入库模式映射
        mode_map = {"链接": "link", "复制": "copy", "移动": "move"}
        entry_mode = mode_map.get(self.mode_combo.currentText(), "link")

        # 收集所有文件模式
        file_patterns = []
        
        # 从复选框收集模式
        if self.image_check.isChecked():
            file_patterns.extend(["*.jpg", "*.jpeg", "*.png", "*.gif", "*.bmp", "*.tiff", "*.webp"])
        if self.video_check.isChecked():
            file_patterns.extend(["*.mp4", "*.avi", "*.mkv", "*.mov", "*.wmv", "*.flv", "*.webm"])
        if self.audio_check.isChecked():
            file_patterns.extend(["*.mp3", "*.wav", "*.flac", "*.aac", "*.ogg", "*.wma"])
        if self.document_check.isChecked():
            file_patterns.extend(["*.pdf", "*.doc", "*.docx", "*.txt", "*.rtf", "*.odt"])
        if self.spreadsheet_check.isChecked():
            file_patterns.extend(["*.xls", "*.xlsx", "*.csv", "*.ods"])
        if self.archive_check.isChecked():
            file_patterns.extend(["*.zip", "*.rar", "*.7z", "*.tar", "*.gz", "*.bz2"])
        
        # 从自定义文本框收集模式
        patterns_text = self.patterns_edit.toPlainText().strip()
        if patterns_text:
            custom_patterns = [
                pattern.strip() for pattern in patterns_text.split('\n')
                if pattern.strip()
            ]
            file_patterns.extend(custom_patterns)
        
        # 去重
        file_patterns = list(set(file_patterns))

        return {
            "folder_path": self.path_edit.text().strip(),
            "entry_mode": entry_mode,
            "file_patterns": file_patterns,
            "auto_add": self.auto_add_check.isChecked(),
            "recursive": self.recursive_check.isChecked(),
            "auto_dedup": self.auto_dedup_check.isChecked()
        }

    def validate_config(self) -> Tuple[bool, str]:
        """验证配置

        Returns:
            tuple: (是否有效, 错误信息)
        """
        folder_path = self.path_edit.text().strip()

        # 检查路径是否为空
        if not folder_path:
            return False, "请选择要监控的文件夹路径"

        # 检查路径是否存在
        if not os.path.exists(folder_path):
            return False, f"文件夹不存在: {folder_path}"

        # 检查是否为文件夹
        if not os.path.isdir(folder_path):
            return False, f"路径不是文件夹: {folder_path}"

        # 检查文件模式格式
        patterns_text = self.patterns_edit.toPlainText().strip()
        if patterns_text:
            patterns = [p.strip() for p in patterns_text.split('\n') if p.strip()]
            for pattern in patterns:
                if not pattern:
                    continue
                # 简单验证：应该包含文件扩展名模式
                if not ('*' in pattern or '?' in pattern or pattern.startswith('.')):
                    return False, f"文件模式格式可能不正确: {pattern}\n建议使用如 *.txt 的格式"

        return True, ""

    def on_accept(self):
        """确定按钮点击事件"""
        # 验证配置
        is_valid, error_msg = self.validate_config()
        if not is_valid:
            QMessageBox.warning(self, "配置错误", error_msg)
            return

        self.accept()
    
    def _update_patterns_from_checkboxes(self):
        """当复选框状态改变时更新模式显示（可选功能）"""
        # 这个方法可以用来实时更新预览，目前保持简单
        pass
    
    def _set_checkboxes_from_patterns(self, patterns: List[str]):
        """根据现有模式设置复选框状态"""
        patterns_lower = [p.lower() for p in patterns]
        
        # 图片类型
        image_patterns = ["*.jpg", "*.jpeg", "*.png", "*.gif", "*.bmp", "*.tiff", "*.webp"]
        if any(p.lower() in patterns_lower for p in image_patterns):
            self.image_check.setChecked(True)
        
        # 视频类型
        video_patterns = ["*.mp4", "*.avi", "*.mkv", "*.mov", "*.wmv", "*.flv", "*.webm"]
        if any(p.lower() in patterns_lower for p in video_patterns):
            self.video_check.setChecked(True)
        
        # 音频类型
        audio_patterns = ["*.mp3", "*.wav", "*.flac", "*.aac", "*.ogg", "*.wma"]
        if any(p.lower() in patterns_lower for p in audio_patterns):
            self.audio_check.setChecked(True)
        
        # 文档类型
        document_patterns = ["*.pdf", "*.doc", "*.docx", "*.txt", "*.rtf", "*.odt"]
        if any(p.lower() in patterns_lower for p in document_patterns):
            self.document_check.setChecked(True)
        
        # 表格类型
        spreadsheet_patterns = ["*.xls", "*.xlsx", "*.csv", "*.ods"]
        if any(p.lower() in patterns_lower for p in spreadsheet_patterns):
            self.spreadsheet_check.setChecked(True)
        
        # 压缩包类型
        archive_patterns = ["*.zip", "*.rar", "*.7z", "*.tar", "*.gz", "*.bz2"]
        if any(p.lower() in patterns_lower for p in archive_patterns):
            self.archive_check.setChecked(True)
    
    def _get_custom_patterns(self, patterns: List[str]) -> List[str]:
        """获取不属于常见类型的自定义模式"""
        # 所有已知的常见模式
        known_patterns = {
            "*.jpg", "*.jpeg", "*.png", "*.gif", "*.bmp", "*.tiff", "*.webp",  # 图片
            "*.mp4", "*.avi", "*.mkv", "*.mov", "*.wmv", "*.flv", "*.webm",  # 视频
            "*.mp3", "*.wav", "*.flac", "*.aac", "*.ogg", "*.wma",  # 音频
            "*.pdf", "*.doc", "*.docx", "*.txt", "*.rtf", "*.odt",  # 文档
            "*.xls", "*.xlsx", "*.csv", "*.ods",  # 表格
            "*.zip", "*.rar", "*.7z", "*.tar", "*.gz", "*.bz2"  # 压缩包
        }
        
        # 转换为小写进行比较
        known_patterns_lower = {p.lower() for p in known_patterns}
        
        # 返回不在已知模式中的自定义模式
        custom_patterns = []
        for pattern in patterns:
            if pattern.lower() not in known_patterns_lower:
                custom_patterns.append(pattern)
        
        return custom_patterns


# 为了兼容性，在settings_dialog模块中也导出这个类
__all__ = ['MonitorConfigDialog']
