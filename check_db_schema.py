import sqlite3
from pprint import pprint

def get_db_schema(db_path):
    """获取数据库表结构"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有表名
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    
    schema = {}
    for table in tables:
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table})")
        columns = cursor.fetchall()
        schema[table] = [{"name": col[1], "type": col[2]} for col in columns]
    
    conn.close()
    return schema

if __name__ == "__main__":
    # 使用运行日志中显示的完整路径
    db_path = r"D:/PythonProjects2/SmartVault3/smartvault/SmartVault_Lib\data\smartvault.db"
    print(f"正在检查数据库: {db_path}")
    schema = get_db_schema(db_path)
    print("\n当前数据库表结构：")
    pprint(schema)