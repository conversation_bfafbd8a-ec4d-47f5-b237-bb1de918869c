# -*- coding: utf-8 -*-
"""
文件监控处理器
负责处理文件监控相关的所有功能，包括监控事件、重复文件处理、批量处理等
"""

import os
from datetime import datetime
from PySide6.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit, QGroupBox
from PySide6.QtCore import QObject


class MonitorHandler(QObject):
    """文件监控处理器类"""

    def __init__(self, main_window):
        """初始化监控处理器
        
        Args:
            main_window: 主窗口实例，用于访问服务和UI组件
        """
        super().__init__()
        self.main_window = main_window
        self.monitor_service = main_window.monitor_service
        self.file_service = main_window.file_service

    def start_configured_monitors(self):
        """启动已配置的监控"""
        try:
            # 检查用户设置中的监控状态
            from smartvault.utils.config import get_monitor_status
            monitor_enabled = get_monitor_status()

            print(f"配置文件中监控状态: {monitor_enabled}")

            # 获取所有监控配置
            configs = self.monitor_service.get_all_monitors()

            if not monitor_enabled:
                # 如果配置文件中禁用了监控，确保数据库中所有监控都是非活动状态
                print("监控功能已在设置中禁用，确保所有监控停止")
                for config in configs:
                    if config.get('is_active', False):
                        self.monitor_service.stop_monitoring(config['id'])
                return

            # 如果配置文件中启用了监控，但数据库中没有活动监控，则激活所有监控
            active_configs = [config for config in configs if config.get('is_active', False)]

            if not active_configs and configs:
                print("配置文件启用监控但数据库中无活动监控，同步状态...")
                # 将所有监控设置为活动状态
                for config in configs:
                    monitor_id = config['id']
                    # 更新数据库中的 is_active 状态
                    cursor = self.monitor_service.db.conn.cursor()
                    cursor.execute(
                        "UPDATE monitor_configs SET is_active = 1, updated_at = ? WHERE id = ?",
                        (datetime.now().isoformat(), monitor_id)
                    )
                self.monitor_service.db.conn.commit()
                print(f"已将 {len(configs)} 个监控配置设置为活动状态")

                # 重新获取配置
                active_configs = self.monitor_service.get_all_monitors()

            if not active_configs:
                print("没有需要启动的监控配置")
                return

            print(f"启动 {len(active_configs)} 个监控配置...")

            for config in active_configs:
                monitor_id = config['id']
                success = self.monitor_service.start_monitoring(monitor_id)
                if success:
                    print(f"✅ 监控已启动: {config['folder_path']}")
                else:
                    print(f"❌ 监控启动失败: {config['folder_path']}")

            # 更新工具栏监控状态
            if hasattr(self.main_window, 'toolbar_manager'):
                self.main_window.toolbar_manager.update_monitor_status()

            # 延时检查现有文件
            if active_configs:
                from PySide6.QtCore import QTimer
                QTimer.singleShot(5000, self._check_existing_files_in_monitored_folders)

        except Exception as e:
            print(f"启动监控配置失败: {e}")
            import traceback
            traceback.print_exc()

    def toggle_all_monitors(self):
        """切换所有监控的开关状态"""
        try:
            from smartvault.utils.config import save_monitor_status, get_monitor_status

            # 获取当前监控状态
            stats = self.monitor_service.get_monitor_statistics()
            running_count = stats['running_monitors']

            if running_count > 0:
                # 当前有监控在运行，停止所有监控
                self.monitor_service.stop_all_monitoring()
                # 保存状态为禁用
                save_monitor_status(False)
                self.main_window.show_status_message("已停止所有文件监控", True)
                print("✅ 已停止所有监控")
            else:
                # 当前没有监控在运行，启动所有配置的监控
                configs = self.monitor_service.get_all_monitors()

                if not configs:
                    self.main_window.show_status_message("没有可启动的监控配置", False)
                    return

                # 将所有监控设置为活动状态（同步数据库）
                for config in configs:
                    monitor_id = config['id']
                    # 更新数据库中的 is_active 状态
                    cursor = self.monitor_service.db.conn.cursor()
                    cursor.execute(
                        "UPDATE monitor_configs SET is_active = 1, updated_at = ? WHERE id = ?",
                        (datetime.now().isoformat(), monitor_id)
                    )
                self.monitor_service.db.conn.commit()

                # 启动所有监控
                started_count = 0
                for config in configs:
                    monitor_id = config['id']
                    success = self.monitor_service.start_monitoring(monitor_id)
                    if success:
                        started_count += 1

                # 保存状态为启用
                save_monitor_status(True)
                self.main_window.show_status_message(f"已启动 {started_count} 个文件监控", True)
                print(f"✅ 已启动 {started_count} 个监控")

            # 更新工具栏状态
            if hasattr(self.main_window, 'toolbar_manager'):
                self.main_window.toolbar_manager.update_monitor_status()

        except Exception as e:
            print(f"切换监控状态失败: {e}")
            self.main_window.show_status_message(f"切换监控状态失败: {e}", False)
            import traceback
            traceback.print_exc()

    def on_duplicate_file_suggestion(self, duplicate_info):
        """处理单个重复文件建议事件"""
        try:
            # 单个文件操作，立即显示对话框
            self._show_single_duplicate_suggestion(duplicate_info)

        except Exception as e:
            print(f"处理重复文件建议失败: {e}")

    def _show_single_duplicate_suggestion(self, duplicate_info):
        """显示单个重复文件建议对话框"""
        try:
            # 构建提示消息
            filename = duplicate_info.get('filename', '未知文件')
            new_file = duplicate_info.get('new_file', '')
            existing_file = duplicate_info.get('existing_file', '')
            suggestion = duplicate_info.get('suggestion', '')

            if suggestion == 'consider_delete_duplicate':
                message = f"检测到重复文件：{filename}\n\n"
                message += f"新文件位置：{new_file}\n"
                message += f"已存在位置：{existing_file}\n\n"
                message += "建议：考虑删除重复的文件以节省存储空间。"

                # 显示信息对话框
                msg_box = QMessageBox(self.main_window)
                msg_box.setWindowTitle("重复文件建议")
                msg_box.setText(message)
                msg_box.setIcon(QMessageBox.Information)
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.exec()

                print(f"✅ 已显示重复文件建议对话框：{filename}")

            elif suggestion == 'file_already_exists':
                message = f"文件已存在于库中：{filename}\n\n"
                message += f"文件位置：{existing_file}\n\n"
                message += "该文件已经在智能文件库中，无需重复添加。"

                # 显示信息对话框
                msg_box = QMessageBox(self.main_window)
                msg_box.setWindowTitle("文件已存在")
                msg_box.setText(message)
                msg_box.setIcon(QMessageBox.Information)
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.exec()

                print(f"✅ 已显示文件已存在对话框：{filename}")

        except Exception as e:
            print(f"显示重复文件建议对话框失败: {e}")

    def on_batch_duplicate_processed(self, batch_context):
        """处理批量重复文件建议事件"""
        try:
            operation_name = batch_context.get('name', '批量操作')
            suggestions = batch_context.get('duplicate_suggestions', [])

            if not suggestions:
                return

            print(f"🎯 处理批量重复文件建议: {operation_name}, {len(suggestions)} 个建议")

            # 显示批量重复文件处理对话框
            self._show_batch_duplicate_dialog(operation_name, suggestions)

        except Exception as e:
            print(f"处理批量重复文件建议失败: {e}")

    def _show_batch_duplicate_dialog(self, operation_name, suggestions):
        """显示批量重复文件处理对话框"""
        try:
            # 创建对话框
            dialog = QDialog(self.main_window)
            dialog.setWindowTitle(f"批量重复文件处理 - {operation_name}")
            dialog.setMinimumSize(600, 400)

            layout = QVBoxLayout(dialog)

            # 标题信息
            title_label = QLabel(f"在 {operation_name} 过程中发现 {len(suggestions)} 个重复文件：")
            title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
            layout.addWidget(title_label)

            # 按类型分组显示
            file_exists_count = len([s for s in suggestions if s.get('suggestion') == 'file_already_exists'])
            duplicate_count = len([s for s in suggestions if s.get('suggestion') == 'consider_delete_duplicate'])

            # 文件已存在组
            if file_exists_count > 0:
                exists_group = QGroupBox(f"文件已存在 ({file_exists_count} 个)")
                exists_layout = QVBoxLayout(exists_group)

                exists_text = QTextEdit()
                exists_text.setMaximumHeight(120)
                exists_content = []
                for suggestion in suggestions:
                    if suggestion.get('suggestion') == 'file_already_exists':
                        filename = suggestion.get('filename', '未知文件')
                        exists_content.append(f"• {filename}")

                exists_text.setPlainText('\n'.join(exists_content))
                exists_text.setReadOnly(True)
                exists_layout.addWidget(exists_text)

                layout.addWidget(exists_group)

            # 重复文件建议组
            if duplicate_count > 0:
                duplicate_group = QGroupBox(f"重复文件建议 ({duplicate_count} 个)")
                duplicate_layout = QVBoxLayout(duplicate_group)

                duplicate_text = QTextEdit()
                duplicate_text.setMaximumHeight(120)
                duplicate_content = []
                for suggestion in suggestions:
                    if suggestion.get('suggestion') == 'consider_delete_duplicate':
                        filename = suggestion.get('filename', '未知文件')
                        new_file = suggestion.get('new_file', '')
                        existing_file = suggestion.get('existing_file', '')
                        duplicate_content.append(f"• {filename}")
                        duplicate_content.append(f"  新位置: {new_file}")
                        duplicate_content.append(f"  已存在: {existing_file}")
                        duplicate_content.append("")

                duplicate_text.setPlainText('\n'.join(duplicate_content))
                duplicate_text.setReadOnly(True)
                duplicate_layout.addWidget(duplicate_text)

                layout.addWidget(duplicate_group)

            # 说明文本
            info_label = QLabel("说明：这些文件已被跳过，不会重复添加到文件库中。")
            info_label.setStyleSheet("color: #666; font-size: 12px; margin-top: 10px;")
            layout.addWidget(info_label)

            # 按钮
            button_layout = QHBoxLayout()
            button_layout.addStretch()

            ok_button = QPushButton("确定")
            ok_button.clicked.connect(dialog.accept)
            ok_button.setDefault(True)
            button_layout.addWidget(ok_button)

            layout.addLayout(button_layout)

            # 显示对话框
            dialog.exec()

            print(f"✅ 已显示批量重复文件处理对话框")

        except Exception as e:
            print(f"显示批量重复文件对话框失败: {e}")

    def _check_existing_files_in_monitored_folders(self):
        """检查监控文件夹中的现有文件"""
        try:
            print("🔍 开始检查监控文件夹中的现有文件...")

            configs = self.monitor_service.get_all_monitors()
            active_configs = [config for config in configs if config.get('is_active', False)]

            total_files_found = 0

            for config in active_configs:
                folder_path = config['folder_path']
                if not os.path.exists(folder_path):
                    continue

                print(f"   📁 检查文件夹: {folder_path}")

                # 扫描文件夹中的文件
                files_found = []
                try:
                    for filename in os.listdir(folder_path):
                        file_path = os.path.join(folder_path, filename)

                        # 跳过目录
                        if os.path.isdir(file_path):
                            continue

                        # 检查文件模式匹配
                        patterns = config.get('file_patterns', [])
                        if patterns and not self.monitor_service._matches_patterns(file_path, patterns):
                            continue

                        files_found.append(file_path)

                    if files_found:
                        print(f"   📄 发现 {len(files_found)} 个符合条件的文件")
                        total_files_found += len(files_found)

                        # 批量处理文件
                        self._batch_process_existing_files(files_found, config)
                    else:
                        print(f"   ✅ 文件夹中没有符合条件的文件")

                except Exception as e:
                    print(f"   ❌ 扫描文件夹失败: {e}")

            if total_files_found > 0:
                print(f"🎉 检查完成，共发现 {total_files_found} 个文件")
            else:
                print("✅ 检查完成，没有发现需要处理的文件")

        except Exception as e:
            print(f"检查现有文件失败: {e}")

    def _batch_process_existing_files(self, file_paths, config):
        """批量处理现有文件"""
        try:
            monitor_id = config['id']

            # 显示批量处理进度
            self.main_window.show_status_message(f"正在批量处理 {len(file_paths)} 个文件...", True)

            success_count = 0
            error_count = 0
            errors = []

            for file_path in file_paths:
                try:
                    # 使用监控服务的文件处理逻辑
                    self.monitor_service._auto_add_file_with_feedback(file_path, config, monitor_id)
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    errors.append(f"{os.path.basename(file_path)}: {str(e)}")

            # 显示处理结果
            if error_count > 0:
                self._show_batch_error_dialog(errors, success_count, error_count)
            else:
                self.main_window.show_status_message(f"✅ 成功处理 {success_count} 个文件", True)

            # 刷新文件视图（如果有文件成功添加）
            if success_count > 0:
                self.main_window._load_files_silently()

        except Exception as e:
            print(f"批量处理文件失败: {e}")

    def _show_batch_error_dialog(self, errors, success_count, error_count):
        """显示批量错误处理对话框"""
        try:
            dialog = QDialog(self.main_window)
            dialog.setWindowTitle("批量处理结果")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout(dialog)

            # 结果摘要
            summary_label = QLabel(f"处理完成：成功 {success_count} 个，失败 {error_count} 个")
            summary_label.setStyleSheet("font-weight: bold; color: #d63384; margin: 10px;")
            layout.addWidget(summary_label)

            # 错误详情
            error_label = QLabel("错误详情：")
            layout.addWidget(error_label)

            error_text = QTextEdit()
            error_text.setReadOnly(True)
            error_text.setPlainText("\n".join(errors))
            layout.addWidget(error_text)

            # 按钮
            button_layout = QHBoxLayout()
            ok_button = QPushButton("确定")
            ok_button.clicked.connect(dialog.accept)
            button_layout.addStretch()
            button_layout.addWidget(ok_button)
            layout.addLayout(button_layout)

            dialog.exec()

        except Exception as e:
            print(f"显示批量错误对话框失败: {e}")

    def on_monitor_event(self, event_type, file_path_or_message, monitor_id, extra_data=None):
        """处理监控事件

        Args:
            event_type: 事件类型 (processing/success/error/duplicate/batch_start/batch_complete)
            file_path_or_message: 文件路径或消息内容
            monitor_id: 监控ID
            extra_data: 额外数据（用于批量处理统计等）
        """
        try:
            if event_type == "error":
                # 错误处理：弹出错误对话框
                self._show_monitor_error(file_path_or_message)
                return

            elif event_type == "batch_start":
                # 批量处理开始：显示进度条和状态信息
                print(f"🎯 UI收到batch_start事件")
                self.main_window.show_status_message("正在批量处理监控文件...", True)
                # 启动进度条（不确定进度模式）
                if hasattr(self.main_window, 'compact_progress_bar'):
                    print(f"🎯 启动进度条: compact_progress_bar存在")
                    self.main_window.compact_progress_bar.start_progress("批量处理监控文件", 0, False)
                else:
                    print(f"⚠️ compact_progress_bar不存在")
                print(f"📊 开始批量处理监控文件...")
                return

            elif event_type == "batch_complete":
                # 批量处理完成：显示统计信息并刷新视图
                print(f"🎯 UI收到batch_complete事件")
                if extra_data:
                    total = extra_data.get('total', 0)
                    success = extra_data.get('success', 0)
                    errors = extra_data.get('errors', 0)

                    print(f"🎯 批量处理数据: 总计 {total} 个文件，成功 {success} 个，失败 {errors} 个")

                    # 完成进度条
                    if hasattr(self.main_window, 'compact_progress_bar'):
                        print(f"🎯 完成进度条: compact_progress_bar存在")
                        self.main_window.compact_progress_bar.finish_progress(f"批量处理完成: {success}/{total} 个文件", True, 3000)
                    else:
                        print(f"⚠️ compact_progress_bar不存在")

                    # 显示详细统计信息
                    if errors > 0:
                        status_msg = f"监控批量处理完成: 成功 {success} 个，失败 {errors} 个文件"
                        print(f"🎯 显示状态栏消息(错误): {status_msg}")
                        self.main_window._show_important_status_message(status_msg, False)
                    else:
                        status_msg = f"监控批量处理完成: 成功添加 {success} 个文件"
                        print(f"🎯 显示状态栏消息(成功): {status_msg}")
                        self.main_window._show_important_status_message(status_msg, True)

                    # 刷新文件视图（如果有文件成功添加）
                    if success > 0:
                        print(f"🎯 刷新文件视图")
                        self.main_window._load_files_silently()

                    print(f"📊 批量处理统计: 总计 {total} 个文件，成功 {success} 个，失败 {errors} 个")
                else:
                    # 没有额外数据，使用消息内容
                    print(f"🎯 显示状态栏消息(无额外数据): {file_path_or_message}")
                    self.main_window.show_status_message(file_path_or_message, True)
                    self.main_window._load_files_silently()
                return

            elif event_type == "success":
                # 单个文件成功处理（旧版本兼容）
                self._show_monitor_success(file_path_or_message)
                self.main_window._load_files_silently()
                return

            elif event_type == "processing":
                # 单个文件处理中（旧版本兼容）
                file_path = file_path_or_message
                filename = os.path.basename(file_path)
                self.main_window.show_status_message(f"正在处理: {filename}", True)
                print(f"📁 开始处理: {filename}")
                return

            elif event_type == "duplicate":
                # 重复文件处理：弹出选择对话框
                self._show_duplicate_file_dialog(file_path_or_message, monitor_id)
                return

            # 其他事件类型忽略，避免不必要的UI更新

        except Exception as e:
            print(f"处理监控事件失败: {e}")
            import traceback
            traceback.print_exc()

    def _show_monitor_error(self, error_message):
        """显示监控错误对话框"""
        try:
            QMessageBox.critical(
                self.main_window,
                "文件监控错误",
                f"文件监控处理时发生错误：\n\n{error_message}"
            )
            print(f"❌ 监控错误: {error_message}")
        except Exception as e:
            print(f"显示监控错误对话框失败: {e}")

    def _show_monitor_success(self, success_message):
        """显示监控成功反馈（状态栏高亮）"""
        try:
            # 状态栏高亮显示成功信息
            status_bar = self.main_window.statusBar()
            status_bar.setStyleSheet("background-color: #d4edda; color: #155724; font-weight: bold;")
            status_bar.showMessage(f"✅ {success_message}")

            # 3秒后恢复默认样式并更新统计
            from PySide6.QtCore import QTimer
            def reset_and_update():
                status_bar.setStyleSheet("")
                self._update_monitor_stats()

            QTimer.singleShot(3000, reset_and_update)

            print(f"✅ 监控成功: {success_message}")
        except Exception as e:
            print(f"显示监控成功反馈失败: {e}")

    def _show_duplicate_file_dialog(self, file_info, monitor_id):
        """显示重复文件处理对话框"""
        try:
            message = file_info["message"]

            # 创建自定义消息框
            msg_box = QMessageBox(self.main_window)
            msg_box.setWindowTitle("发现重复文件")
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Question)

            # 添加自定义按钮
            replace_btn = msg_box.addButton("替换", QMessageBox.ActionRole)
            skip_btn = msg_box.addButton("跳过", QMessageBox.RejectRole)
            rename_btn = msg_box.addButton("重命名添加", QMessageBox.AcceptRole)

            # 设置默认按钮
            msg_box.setDefaultButton(skip_btn)

            # 显示对话框
            msg_box.exec()

            # 处理用户选择
            clicked_button = msg_box.clickedButton()

            if clicked_button == replace_btn:
                choice = "replace"
            elif clicked_button == rename_btn:
                choice = "rename"
            else:  # skip_btn 或其他
                choice = "skip"

            # 调用监控服务处理用户选择
            if hasattr(self.main_window, 'monitor_service'):
                self.main_window.monitor_service.handle_duplicate_choice(choice, file_info)

            print(f"用户选择处理重复文件: {choice}")

        except Exception as e:
            print(f"显示重复文件对话框失败: {e}")
            # 出错时默认跳过
            if hasattr(self.main_window, 'monitor_service'):
                self.main_window.monitor_service.handle_duplicate_choice("skip", file_info)

    def _update_monitor_stats(self):
        """更新监控统计信息"""
        try:
            stats = self.monitor_service.get_monitor_statistics()

            # 更新状态栏统计信息
            stats_text = f"监控: {stats['active_monitors']}/{stats['total_monitors']} 活动, 已处理 {stats['total_files_added']} 个文件"

            # 高亮显示统计信息
            status_bar = self.main_window.statusBar()
            status_bar.setStyleSheet("background-color: #e8f5e8; color: #2d5a2d;")
            status_bar.showMessage(stats_text)

            # 3秒后恢复默认样式（使用主线程的QTimer）
            from PySide6.QtCore import QTimer
            def reset_status_style():
                status_bar.setStyleSheet("")
            QTimer.singleShot(3000, reset_status_style)

        except Exception as e:
            print(f"更新监控统计失败: {e}")