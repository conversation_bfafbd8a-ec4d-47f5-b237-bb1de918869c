#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI集成处理器单元测试
测试 AIIntegrationHandler 类的各项功能
"""

import unittest
import sys
import os
from unittest.mock import Mock, MagicMock, patch
from PySide6.QtCore import QTimer
from PySide6.QtWidgets import QApplication

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from smartvault.ui.main_window.ai_integration_handler import AIIntegrationHandler


class TestAIIntegrationHandler(unittest.TestCase):
    """AI集成处理器测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """每个测试方法前的初始化"""
        # 创建模拟的主窗口
        self.mock_main_window = Mock()
        
        # 创建模拟的服务
        self.mock_file_service = Mock()
        self.mock_tag_service = Mock()
        self.mock_auto_tag_service = Mock()
        self.mock_ai_manager = Mock()
        
        # 设置主窗口的服务属性
        self.mock_main_window.file_service = self.mock_file_service
        self.mock_main_window.tag_service = self.mock_tag_service
        self.mock_main_window.auto_tag_service = self.mock_auto_tag_service
        self.mock_main_window.ai_manager = self.mock_ai_manager
        
        # 创建AI集成处理器实例
        self.ai_handler = AIIntegrationHandler(self.mock_main_window)
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.ai_handler.main_window, self.mock_main_window)
        self.assertEqual(self.ai_handler.file_service, self.mock_file_service)
        self.assertEqual(self.ai_handler.tag_service, self.mock_tag_service)
        self.assertEqual(self.ai_handler.auto_tag_service, self.mock_auto_tag_service)
        self.assertIsNone(self.ai_handler.ai_manager)
    
    @patch('smartvault.utils.config.load_config')
    def test_initialize_ai_manager_success(self, mock_load_config):
        """测试AI管理器初始化成功"""
        # 模拟配置
        mock_config = {'ai_enabled': True, 'ai_stage': 2}
        mock_load_config.return_value = mock_config
        
        # 模拟AI管理器初始化成功
        self.mock_ai_manager.initialize.return_value = True
        self.mock_ai_manager.get_status.return_value = {
            'enabled': True,
            'stage': 2,
            'status': 'ready'
        }
        
        # 模拟file_service有db属性
        self.mock_file_service.db = Mock()
        
        # 调用方法
        self.ai_handler.initialize_ai_manager()
        
        # 验证AI管理器初始化被调用
        self.mock_ai_manager.initialize.assert_called_once_with(
            config=mock_config,
            tag_service=self.mock_tag_service,
            auto_tag_service=self.mock_auto_tag_service,
            db=self.mock_file_service.db
        )
        
        # 验证状态获取被调用
        self.mock_ai_manager.get_status.assert_called_once()
    
    @patch('smartvault.utils.config.load_config')
    def test_initialize_ai_manager_failure(self, mock_load_config):
        """测试AI管理器初始化失败"""
        # 模拟配置
        mock_config = {'ai_enabled': True, 'ai_stage': 2}
        mock_load_config.return_value = mock_config
        
        # 模拟AI管理器初始化失败
        self.mock_ai_manager.initialize.return_value = False
        
        # 模拟file_service有db属性
        self.mock_file_service.db = Mock()
        
        # 调用方法
        self.ai_handler.initialize_ai_manager()
        
        # 验证AI管理器初始化被调用
        self.mock_ai_manager.initialize.assert_called_once()
        
        # 验证状态获取没有被调用（因为初始化失败）
        self.mock_ai_manager.get_status.assert_not_called()
    
    @patch('PySide6.QtCore.QTimer.singleShot')
    def test_update_ai_tag_suggestions(self, mock_timer):
        """测试更新AI标签建议"""
        # 模拟文件信息
        file_id = 1
        file_info = {
            'id': 1,
            'name': 'test.txt',
            'original_path': '/test/test.txt'
        }
        self.mock_file_service.get_file_by_id.return_value = file_info
        
        # 模拟AI管理器可用
        self.mock_main_window.ai_manager.is_available.return_value = True
        
        # 模拟AI标签面板
        self.mock_main_window.ai_tag_panel = Mock()
        
        # 调用方法
        self.ai_handler.update_ai_tag_suggestions(file_id)
        
        # 验证文件选择设置
        self.mock_main_window.ai_tag_panel.set_file_selection.assert_called_once_with(file_id, 'test.txt')
        
        # 验证异步调用被设置
        mock_timer.assert_called_once()
    
    @patch('datetime.datetime')
    def test_on_ai_tag_accepted(self, mock_datetime):
        """测试AI标签接受事件"""
        file_id = 123
        tag_name = "文档"
        
        # 模拟时间
        mock_datetime.now.return_value.strftime.return_value = '20231201'
        
        # 模拟标签服务
        self.mock_tag_service.get_tag_by_name.return_value = None  # 标签不存在
        self.mock_tag_service.create_tag.return_value = 10  # 新标签ID
        self.mock_tag_service.add_tag_to_file.return_value = True
        
        # 模拟主窗口方法和导航面板
        self.mock_main_window.show_status_message = Mock()
        self.mock_main_window.refresh_file_list = Mock()
        self.mock_main_window.navigation_panel = Mock()
        self.mock_main_window.navigation_panel.refresh_tags = Mock()
        
        # 模拟AI管理器反馈记录
        self.mock_main_window.ai_manager.record_feedback = Mock()
        
        # 调用方法
        self.ai_handler.on_ai_tag_accepted(file_id, tag_name)
        
        # 验证标签创建和添加
        self.mock_tag_service.get_tag_by_name.assert_called_once_with(tag_name)
        self.mock_tag_service.create_tag.assert_called_once_with(tag_name)
        self.mock_tag_service.add_tag_to_file.assert_called_once_with(file_id, 10)
        self.mock_main_window.show_status_message.assert_called()
        
        # 验证导航面板刷新
        # self.mock_main_window.navigation_panel.refresh_tags.assert_called_once()
    
    @patch('datetime.datetime')
    def test_on_ai_tag_rejected(self, mock_datetime):
        """测试AI标签拒绝事件"""
        file_id = 123
        tag_name = "错误标签"
        
        # 模拟时间
        mock_datetime.now.return_value.strftime.return_value = '20231201'
        
        # 模拟主窗口方法
        self.mock_main_window.show_status_message = Mock()
        
        # 模拟AI管理器反馈记录
        self.mock_main_window.ai_manager.record_feedback = Mock()
        
        # 调用方法
        self.ai_handler.on_ai_tag_rejected(file_id, tag_name)
        
        # 验证状态消息显示
        self.mock_main_window.show_status_message.assert_called()
        
        # 验证反馈记录
        # self.mock_main_window.ai_manager.record_feedback.assert_called_once()
    
    def test_fetch_ai_suggestions(self):
        """测试获取AI建议"""
        # 模拟文件信息
        mock_file_info = {
            'name': 'test.pdf',
            'path': '/test/test.pdf',
            'extension': '.pdf',
            'full_name': 'test.pdf',
            'folder_path': '/test'
        }
        
        # 模拟AI建议
        mock_suggestions = ['PDF', '文档']
        self.mock_main_window.ai_manager.suggest_tags.return_value = mock_suggestions
        
        # 模拟AI标签面板
        self.mock_main_window.ai_tag_panel = Mock()
        
        # 模拟置信度计算
        with patch.object(self.ai_handler, '_calculate_tag_confidence') as mock_calc:
            mock_calc.side_effect = [0.95, 0.85]
            
            # 调用方法
            self.ai_handler._fetch_ai_suggestions(mock_file_info)
            
            # 验证AI管理器被调用
            self.mock_main_window.ai_manager.suggest_tags.assert_called_once_with(mock_file_info)
            
            # 验证置信度计算被调用
            self.assertEqual(mock_calc.call_count, 2)
            
            # 验证建议显示被调用
            expected_suggestions = [('PDF', 0.95), ('文档', 0.85)]
            self.mock_main_window.ai_tag_panel.show_suggestions.assert_called_once_with(expected_suggestions)
    
    def test_calculate_tag_confidence(self):
        """测试计算标签置信度"""
        tag_name = "文档"
        file_info = {
            'name': 'document.pdf',
            'extension': '.pdf'
        }
        
        # 调用方法
        result = self.ai_handler._calculate_tag_confidence(tag_name, file_info)
        
        # 验证结果 - 基于扩展名的置信度计算
        self.assertIsInstance(result, float)
        self.assertGreaterEqual(result, 0.0)
        self.assertLessEqual(result, 1.0)
    
    @patch('PySide6.QtCore.QTimer.singleShot')
    def test_update_ai_tag_suggestions_with_timer(self, mock_timer):
        """测试带定时器的AI标签建议更新"""
        file_id = 123
        
        # 模拟文件信息
        mock_file_info = {
            'id': file_id,
            'name': 'test.pdf',
            'original_path': '/test/test.pdf',
            'size': 1024
        }
        self.mock_file_service.get_file_by_id.return_value = mock_file_info
        
        # 模拟AI管理器可用
        self.mock_main_window.ai_manager.is_available.return_value = True
        
        # 模拟AI标签面板
        self.mock_main_window.ai_tag_panel = Mock()
        
        # 调用方法
        self.ai_handler.update_ai_tag_suggestions(file_id)
        
        # 验证定时器被调用
        mock_timer.assert_called_once()
        
        # 验证AI标签面板的文件选择被设置
        self.mock_main_window.ai_tag_panel.set_file_selection.assert_called_once_with(file_id, 'test.pdf')
    
    def test_error_handling_in_ai_operations(self):
        """测试AI操作中的错误处理"""
        file_id = 123
        
        # 模拟AI管理器抛出异常
        self.mock_ai_manager.get_tag_suggestions.side_effect = Exception("AI服务不可用")
        
        # 模拟文件信息
        mock_file_info = {'id': file_id, 'name': 'test.txt'}
        self.mock_file_service.get_file_by_id.return_value = mock_file_info
        
        # 调用方法，应该不抛出异常
        try:
            result = self.ai_handler._fetch_ai_suggestions(file_id)
            # 应该返回空列表或None
            self.assertIn(result, [[], None])
        except Exception as e:
            self.fail(f"方法应该处理异常而不是抛出: {e}")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)