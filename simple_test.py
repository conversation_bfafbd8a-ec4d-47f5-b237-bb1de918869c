#!/usr/bin/env python3
"""
简单的性能测试
"""

import time
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入性能"""
    print("🔧 测试模块导入...")
    
    start_time = time.time()
    try:
        from smartvault.services.tag_service import TagService
        print(f"   TagService 导入成功: {time.time() - start_time:.3f}s")
    except Exception as e:
        print(f"   TagService 导入失败: {e}")
        return False
    
    start_time = time.time()
    try:
        from smartvault.ui.components.quick_tag_menu import QuickTagMenu
        print(f"   QuickTagMenu 导入成功: {time.time() - start_time:.3f}s")
    except Exception as e:
        print(f"   QuickTagMenu 导入失败: {e}")
        return False
    
    start_time = time.time()
    try:
        from smartvault.ui.components.note_menu import NoteMenu
        print(f"   NoteMenu 导入成功: {time.time() - start_time:.3f}s")
    except Exception as e:
        print(f"   NoteMenu 导入失败: {e}")
        return False
    
    return True

def test_tag_service():
    """测试标签服务基本功能"""
    print("🔧 测试标签服务...")
    
    try:
        from smartvault.services.tag_service import TagService
        
        tag_service = TagService()
        
        # 测试获取标签列表
        start_time = time.time()
        tags = tag_service.get_tags(parent_id=None)
        query_time = time.time() - start_time
        print(f"   获取标签列表: {len(tags)} 个标签, 耗时 {query_time:.3f}s")
        
        # 测试批量方法是否存在
        if hasattr(tag_service, 'get_files_tags_batch'):
            print("   ✅ 批量查询方法已添加")
        else:
            print("   ❌ 批量查询方法缺失")
            
        if hasattr(tag_service, 'clear_files_tags_batch'):
            print("   ✅ 批量清除方法已添加")
        else:
            print("   ❌ 批量清除方法缺失")
        
        return True
        
    except Exception as e:
        print(f"   标签服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始简单性能测试...")
    print("=" * 40)
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败")
        return
    
    print()
    
    # 测试标签服务
    if not test_tag_service():
        print("❌ 标签服务测试失败")
        return
    
    print()
    print("✅ 基本测试通过")
    print("=" * 40)

if __name__ == "__main__":
    main()
