# SmartVault 技术选型及架构设计 (第六版)

__version__ = "6.0.0"
__author__ = "Mojianghu"
__release_date__ = "20250101"
__update_reason__ = "新增AI功能架构和智能文件管理系统"

## 🎯 **核心开发理念**

> **"功能优先，适度优化；能工作的代码比完美的架构更有价值。"**

这是SmartVault项目的根本指导原则，所有技术决策和开发活动都应围绕这一理念展开。

## 文档说明

本文档是SmartVault项目的技术选型及架构设计文档（第六版），基于AI功能集成和智能文件管理原则进行重大更新。本版本新增了AI功能架构、智能标签建议系统和智能规则引擎，在保持数据安全和文件库独立性的基础上，实现了智能化的文件管理体验。本文档为后续开发提供明确的技术指导，确保开发工作始终聚焦于用户价值创造和智能化体验提升。

## 📋 文档目录

### 1. 技术选型
- 1.1 技术选型说明

### 2. 整体架构
- 2.0 数据安全层
- 2.1 架构层次说明
- **2.2 AI功能架构层 (第六版新增)**
- **2.3 系统流程依赖关系 (第六版更新)**

### 3. 核心组件设计
- 3.0 MVP与后续功能划分
  - 3.0.1 MVP核心组件（第一阶段 - 已完成）
  - 3.0.2 第二阶段功能组件（已完成）
  - 3.0.3 第二阶段基础完善组件（进行中）
  - 3.0.4 数据安全组件（第五版新增 - 已完成）
  - **3.0.5 AI功能组件（第六版新增 - 已完成）**
  - 3.0.6 第三阶段功能组件（计划实现）
- 3.1 UI层组件
- 3.2 业务逻辑层组件
- **3.3 AI功能层组件 (第六版新增)**
- 3.4 数据安全层组件
- 3.5 数据访问层组件

### 4. 数据库设计
- 4.1 数据库表结构
- 4.2 索引设计
- 4.3 数据完整性约束

### 5. 安全架构设计
- 5.1 数据安全策略
- 5.2 备份恢复机制
- 5.3 完整性检查

### 6. 性能优化策略
- 6.1 文件处理优化
- 6.2 数据库查询优化
- 6.3 UI响应优化
- **6.4 AI功能性能优化 (第六版新增)**

### 7. 部署和维护
- 7.1 部署要求
- 7.2 配置管理
- 7.3 日志和监控

## 项目资源
- docs\需求规格书.md（定义系统功能需求，不涉及具体技术实现）
- docs\技术选型及架构设计.md（本文档，项目技术指导的唯一权威文档）
- docs\开发实施方案.md（开发计划和进度跟踪文档）
- icons\SmartVault.ico（应用图标）
- SmartVault_UI.png（UI界面参考图）

## 1. 技术选型

| 类别 | 选型 | 版本 | 说明 |
|------|------|------|------|
| 开发语言 | Python | 3.8+ | 跨平台支持、丰富的库生态、适合快速开发 |
| GUI框架 | PyQt6 | 6.5.0+ | 成熟稳定、跨平台、丰富的组件库 |
| 数据库 | SQLite | 3.35+ | 轻量级、无需额外服务、适合单机应用 |
| ORM框架 | SQLAlchemy Core | 2.0.19 | 简化版ORM，减少复杂性但保留核心功能 |
| 搜索引擎 | 内置简单索引 | - | 基于SQLite的全文搜索，避免额外依赖 |
| 文件监控 | Watchdog | 3.0.0 | 高性能文件系统监控，跨平台支持 |
| 图像处理 | Pillow | 9.5.0 | 图像处理、缩略图生成 |
| 文件指纹 | CRC32 | Python标准库 | 高性能文件哈希计算，用于去重识别 |
| 配置管理 | JSON | - | 使用标准库，简化配置管理 |
| **数据库备份** | **shutil + 定时任务** | **Python标准库** | **自动备份系统，确保数据安全** |
| **文件操作** | **send2trash** | **1.8.2** | **安全删除文件到回收站** |
| **AI功能架构** | **智能规则引擎** | **Python标准库** | **智能标签建议，无需外部模型** |
| **机器学习** | **scikit-learn** | **1.3.2** | **AI第二阶段机器学习功能** |
| **模型序列化** | **joblib** | **1.3.2** | **AI模型保存和加载** |
| **应用目录** | **appdirs** | **1.4.4** | **跨平台应用数据目录管理** |
| **测试框架** | **pytest + pytest-qt** | **7.3.1 + 4.2.0** | **单元测试和UI测试** |

### 1.1 技术选型说明

1. **简化依赖**：相比第一版，减少了第三方库依赖，移除了PyYAML、ujson等非必要库
2. **降低复杂度**：使用SQLAlchemy Core而非完整ORM，简化数据访问层
3. **标准化组件**：优先使用Python标准库和PyQt6内置功能
4. **实用性优先**：所有技术选型以实用性和开发效率为首要考虑因素
5. **线程安全优先**：文件监控从Watchdog改为QFileSystemWatcher，确保Qt线程安全
6. **性能优化**：文件指纹识别采用CRC32算法，比MD5快3-5倍，使用Python标准库
7. **数据安全优先**：新增自动备份系统，使用schedule库实现定时备份
8. **配置独立性**：支持文件库内配置存储，实现真正的多文件库独立管理

## 2. 整体架构

SmartVault采用智能化数据安全优先的五层架构，在传统三层架构基础上新增数据安全层和AI功能层：

```
┌─────────────┐
│    UI层     │ ← 用户界面和交互
└─────┬───────┘
      │
┌─────▼───────┐
│  业务逻辑层  │ ← 核心功能和业务规则
└─────┬───────┘
      │
┌─────▼───────┐
│  AI功能层   │ ← 智能标签建议、文件识别 (第六版新增)
└─────┬───────┘
      │
┌─────▼───────┐
│  数据安全层  │ ← 备份、恢复、完整性检查 (第五版新增)
└─────┬───────┘
      │
┌─────▼───────┐
│  数据访问层  │ ← 数据存储和检索
└─────────────┘
```

### 架构层次详细说明

```
┌─────────────────────────────────────────────────────────────┐
│                        UI层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   主窗口    │ │  设置对话框  │ │  文件对话框  │           │
│  │             │ │ (含AI设置)  │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   业务逻辑层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  文件服务   │ │  标签服务   │ │  搜索服务   │           │
│  │             │ │             │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   AI功能层 (第六版新增)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  AI管理器   │ │智能规则引擎 │ │  降级服务   │           │
│  │             │ │             │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   数据安全层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  备份服务   │ │  配置服务   │ │  安全测试   │           │
│  │             │ │             │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   数据访问层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  数据库访问 │ │  文件系统   │ │  配置管理   │           │
│  │             │ │             │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 2.0 AI功能架构层 (第六版新增)

AI功能层是第六版架构的核心创新，专门负责智能文件管理和标签建议：

**主要职责**：
- **智能标签建议**：基于文件类型、名称、路径的智能标签推荐
- **项目文件检测**：自动识别项目文件夹并应用项目标签
- **文件系列识别**：检测文件名相似性并应用系列标签
- **配置文件识别**：智能识别各种配置文件类型
- **降级处理机制**：AI功能关闭时的安全降级
- **用户行为学习**：学习用户标签使用模式并优化建议

**核心组件**：
- `AIManager` - AI功能管理器，统一管理所有AI功能
- `SmartRuleEngine` - 智能规则引擎，基于规则的文件识别
- `AdaptiveRuleEngine` - 自适应规则引擎，用户行为学习
- `FallbackService` - 降级服务，AI不可用时的安全降级

**技术特点**：
- **无外部依赖**：基于Python标准库实现，无需下载模型
- **安全开关机制**：AI功能关闭时完全不影响现有功能
- **高性能处理**：平均处理时间 < 0.001秒/文件
- **智能文件识别**：支持JSON、YAML、Python、Markdown等多种文件类型
- **项目智能检测**：分析文件夹结构自动识别项目类型
- **配置持久化**：AI设置和学习数据的持久化存储

### 2.1 数据安全层 (第五版新增)

数据安全层是第五版架构的核心创新，专门负责数据安全保障：

**主要职责**：
- **自动备份管理**：定时备份、启动备份、关闭备份
- **备份完整性验证**：确保备份文件的完整性和可用性
- **数据恢复服务**：从备份恢复数据库的完整功能
- **安全监控**：数据库完整性检查、异常检测
- **配置独立性**：文件库内配置存储和管理

**核心组件**：
- `BackupService` - 自动备份服务
- `LibraryConfigService` - 文件库配置服务
- `DatabaseSecurityTest` - 数据库安全测试套件
- `DataIntegrityMonitor` - 数据完整性监控

### 2.2 架构层次说明

1. **UI层**：
   - 负责用户界面展示和交互
   - 包含窗口、对话框、视图和控件
   - 通过直接调用业务逻辑层提供的服务实现功能
   - **新增AI设置页面**：集成到设置对话框中的AI功能配置界面

2. **业务逻辑层**：
   - 实现核心业务功能和规则
   - 包含文件管理、标签管理、搜索等服务
   - 协调UI层和AI功能层之间的交互
   - **集成AI功能调用**：文件添加时自动调用AI标签建议

3. **AI功能层** (第六版新增)：
   - 负责智能文件分析和标签建议
   - 包含智能规则引擎、自适应学习、降级处理
   - 为业务逻辑层提供智能化的文件管理服务
   - 支持安全开关机制，关闭时不影响现有功能

4. **数据安全层** (第五版新增)：
   - 负责数据安全保障和完整性维护
   - 包含自动备份、数据恢复、安全监控
   - 管理文件库独立配置和多库切换
   - 为AI功能层和业务逻辑层提供安全可靠的数据服务

5. **数据访问层**：
   - 负责数据的存储和检索
   - 包含数据库访问、文件系统操作
   - 提供简单的数据访问接口给数据安全层

### 2.3 系统流程依赖关系 (第六版更新)

#### 文件添加流程（含AI功能）

```
用户操作 → UI层 → 业务逻辑层 → AI功能层 → 数据安全层 → 数据访问层
    ↓         ↓         ↓           ↓          ↓           ↓
  选择文件   文件对话框   文件服务    AI管理器    备份服务    数据库
    ↓         ↓         ↓           ↓          ↓           ↓
  确认添加   调用服务   验证文件   智能标签建议  安全检查    存储文件
    ↓         ↓         ↓           ↓          ↓           ↓
  显示结果   更新界面   应用标签   返回建议标签  记录操作    返回结果
```

#### AI功能调用流程

```
文件添加请求
    ↓
业务逻辑层检查AI状态
    ↓
┌─────────────────┬─────────────────┐
│   AI功能开启    │   AI功能关闭    │
│       ↓         │       ↓         │
│   调用AI管理器   │   跳过AI处理    │
│       ↓         │       ↓         │
│ 智能规则引擎分析 │   直接添加文件   │
│       ↓         │       ↓         │
│ 返回标签建议     │   返回空标签     │
│       ↓         │       ↓         │
│ 应用AI建议标签   │   应用用户标签   │
└─────────────────┴─────────────────┘
    ↓
完成文件添加
```

#### 系统启动流程（含AI延后启动）

```
程序启动
    ↓
加载基础配置
    ↓
初始化UI层
    ↓
初始化业务逻辑层
    ↓
初始化数据安全层
    ↓
初始化数据访问层
    ↓
显示主界面 (快速启动)
    ↓
延后初始化AI功能层 (后台)
    ↓
AI功能准备就绪
```

#### 组件依赖关系图

```
┌─────────────────────────────────────────────────────────────┐
│                  依赖关系图 (第六版优化)                    │
│                                                             │
│  UI层组件                                                   │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   主窗口    │───▶│  设置对话框  │───▶│ AI设置页面  │     │
│  │  (core.py)  │    └─────────────┘    └─────────────┘     │
│  └─────────────┘                                           │
│         │                                                   │
│         ▼                                                   │
│  ┌─────────────┐    ┌─────────────┐                       │
│  │AI集成处理器 │    │文件监控处理器│                       │
│  │(第六版新增) │    │(第一阶段拆分)│                       │
│  └─────────────┘    └─────────────┘                       │
│         │                   │                               │
│         ▼                   ▼                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    业务逻辑层                           │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │  文件服务   │──│  标签服务   │──│  监控服务   │     │ │
│         │            │  └─────────────┘  └─────────────┘   │ │
│         ▼            │  ┌─────────────┐                    │ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   AI功能层                             │ │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │ │
│  │  │  AI管理器   │───▶│智能规则引擎 │───▶│  降级服务   │ │ │
│  │  └─────────────┘    └─────────────┘    └─────────────┘ │ │
│  │  ┌─────────────┐                                       │ │
│  │  │AI标签面板   │ (UI组件与AI功能的桥梁)                │ │
│  │  └─────────────┘                                       │ │
│  └─────────────┬───────────────────────────────────────────┘ │
│                ▼                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  数据安全层                             │ │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │ │
│  │  │  备份服务   │───▶│  配置服务   │───▶│  安全测试   │ │ │
│  │  └─────────────┘    └─────────────┘    └─────────────┘ │ │
│  └─────────────┬───────────────────────────────────────────┘ │
│                ▼                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  数据访问层                             │ │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐ │ │
│  │  │  数据库访问 │───▶│  文件系统   │───▶│  配置管理   │ │ │
│  │  └─────────────┘    └─────────────┘    └─────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 3. 核心组件设计

### 3.0 MVP与后续功能划分

为确保项目可以渐进式开发，核心组件按照MVP（最小可行产品）和后续功能进行划分：

#### 3.0.1 MVP核心组件（第一阶段 - 已完成）

| 组件名称 | 职责 | 实现复杂度 | 优先级 | 状态 |
|---------|------|------------|--------|------|
| 主窗口核心框架 | 提供基本UI框架和导航结构 | 中 | 高 | ✅ 已完成 |
| 文件入库对话框 | 支持文件添加到智能文件库 | 中 | 高 | ✅ 已完成 |
| 文件列表视图 | 显示已入库文件，支持分页 | 中 | 高 | ✅ 已完成 |
| 文件服务 | 管理文件入库、查询等核心操作 | 高 | 高 | ✅ 已完成 |
| 数据库连接管理 | 存储文件元数据，支持动态切换 | 中 | 高 | ✅ 已完成 |
| 文件系统 | 处理文件操作 | 中 | 高 | ✅ 已完成 |
| 全数据库搜索 | 提供全范围文件搜索功能 | 中 | 高 | ✅ 已完成 |
| 分页系统 | 按需加载，支持大量文件 | 中 | 高 | ✅ 已完成 |
| 文件库切换 | 支持多文件库管理 | 中 | 高 | ✅ 已完成 |
| **AI集成处理器** | **AI功能模块化拆分，职责分离优化** | **中** | **高** | **✅ 已完成** |
| **文件监控处理器** | **监控功能模块化拆分，第一阶段核心成果** | **中** | **高** | **✅ 已完成** |

#### 3.0.2 第二阶段功能组件（已完成）

| 组件名称 | 职责 | 实现复杂度 | 状态 | 完成度 |
|---------|------|------------|------|--------|
| 标签服务 | 管理三层标签和文件标签关联 | 中 | ✅ 已完成 | 100% |
| 标签管理对话框 | 创建和管理三层标签 | 低 | ✅ 已完成 | 100% |
| 文件标签对话框 | 为文件添加/移除标签 | 低 | ✅ 已完成 | 100% |
| 文件监控服务 | 基于Qt的文件夹监控 | 高 | ✅ 已完成 | 100% |
| 自动标签服务 | 自动标签规则引擎 | 中 | ✅ 已完成 | 100% |
| 高级搜索 | 多条件、全文搜索 | 高 | ✅ 已完成 | 100% |
| 文件表格标签列 | 显示文件标签信息 | 低 | ✅ 已完成 | 100% |
| 右键菜单标签操作 | 快速标签管理 | 低 | ✅ 已完成 | 100% |

#### 3.0.3 第二阶段基础完善组件（进行中）

| 组件名称 | 职责 | 实现复杂度 | 状态 | 优先级 |
|---------|------|------------|------|--------|
| 拖拽文件添加 | 支持拖拽方式添加文件 | 低 | ✅ 进行中 | 高 |
| CRC32文件指纹 | 高性能文件哈希计算 | 低 | ✅ 进行中 | 中 |
| 统一文件处理测试 | 验证所有文件添加方式 | 中 | ✅ 进行中 | 高 |

#### 3.0.4 数据安全组件（第五版新增 - 已完成）

| 组件名称 | 职责 | 实现复杂度 | 状态 | 优先级 |
|---------|------|------------|------|--------|
| 自动备份服务 | 定时备份、完整性验证、备份管理 | 中 | ✅ 已完成 | 高 |
| 文件库配置服务 | 文件库内配置存储、多库独立管理 | 中 | ✅ 已完成 | 高 |
| 数据库安全测试 | 全面安全测试、压力测试、完整性检查 | 高 | ✅ 已完成 | 高 |
| 配置迁移工具 | 全局配置到文件库配置的迁移 | 低 | ✅ 已完成 | 中 |
| 安全修复工具 | 数据库问题自动修复 | 中 | ✅ 已完成 | 中 |

#### 3.0.5 AI功能组件（第六版新增 - 已完成）✅

| 组件名称 | 职责 | 实现复杂度 | 状态 | 优先级 |
|---------|------|------------|------|--------|
| AI管理器 | 统一管理所有AI功能，延后启动机制 | 中 | ✅ 已完成 | 高 |
| 智能规则引擎 | 基于规则的文件类型识别和标签建议 | 中 | ✅ 已完成 | 高 |
| 自适应规则引擎 | 用户行为学习和个性化建议优化 | 高 | ✅ 已完成 | 中 |
| 机器学习引擎 | 基于scikit-learn的机器学习功能 | 高 | ✅ 已完成 | 中 |
| AI设置页面 | 集成到设置对话框的AI功能配置界面 | 低 | ✅ 已完成 | 高 |
| AI标签建议面板 | 智能标签建议的UI组件 | 中 | ✅ 已完成 | 高 |
| 降级服务 | AI功能关闭时的安全降级处理 | 低 | ✅ 已完成 | 高 |
| **AI集成处理器** | **AI功能在UI层的集成和管理，架构拆分优化** | **中** | **✅ 已完成** | **高** |

#### 3.0.6 第三阶段功能组件（计划实现）

| 组件名称 | 职责 | 实现复杂度 | 阶段 | 优先级 |
|---------|------|------------|------|--------|
| 文件查重 | 检测重复文件 | 高 | 第三阶段 | 中 |
| 批量操作 | 批量处理文件 | 中 | 第三阶段 | 中 |
| 用户设置完善 | 管理应用配置 | 低 | 第三阶段 | 低 |

### 3.1 UI层组件

#### 3.1.1 主窗口 (MainWindow)

主窗口是应用程序的主界面，负责组织和管理各UI组件。为避免"超长代码文件"问题，采用组合模式和功能切片设计。

**主要功能**：
- 显示文件列表/网格视图
- 提供导航面板（文件夹、标签）
- 集成搜索功能
- 提供菜单和工具栏

**实现复杂度**：中
- 界面布局相对复杂
- 需要协调多个子组件
- 使用功能切片可降低复杂度

**潜在挑战**：
- 组件间通信可能复杂
- 状态管理需要谨慎设计
- 确保UI响应性能

**代码组织策略**：
- 使用组合而非继承扩展功能
- 将UI功能分解为独立管理器类
- 采用功能切片文件组织
- **AI功能模块化**：AI相关功能拆分到独立处理器
- **监控功能模块化**：文件监控功能拆分到独立处理器

**架构拆分成果** (第六版优化)：
- **AI功能拆分**：将AI相关功能从core.py拆分到AIIntegrationHandler
- **监控功能拆分**：将文件监控功能从core.py拆分到MonitorHandler
- **职责分离**：core.py专注核心UI逻辑，专业功能有专门处理器
- **模块化设计**：符合单一职责原则，提升可维护性
- **组合模式应用**：通过组合实现功能扩展，避免继承复杂性

**代码示例**：
```python
# main_window/core.py - 主窗口核心框架（已优化）
from .ai_integration_handler import AIIntegrationHandler
from .monitor_handler import MonitorHandler

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SmartVault")

        # 使用组合模式管理UI组件
        self.menu_manager = MenuManager(self)
        self.toolbar_manager = ToolbarManager(self)
        self.status_bar_manager = StatusBarManager(self)
        
        # 功能处理器（第六版架构拆分）
        self.ai_integration_handler = AIIntegrationHandler(self)
        self.monitor_handler = MonitorHandler(self)

        # 初始化服务
        self.file_service = FileService()
        self.tag_service = TagService()
        self.search_service = SearchService()
        self.monitor_service = MonitorService()

        # 初始化UI组件
        self.init_ui()
        
        # 功能处理器初始化
        self.ai_integration_handler.initialize_ai_manager()
        self.monitor_handler.start_configured_monitors()

    def init_ui(self):
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建搜索框
        self.search_box = SearchBox()
        self.search_box.search_requested.connect(self.on_search)
        main_layout.addWidget(self.search_box)

        # 创建分割器
        self.splitter = QSplitter(Qt.Horizontal)

        # 创建导航面板
        self.navigation_panel = NavigationPanel()
        self.navigation_panel.folder_selected.connect(self.on_folder_selected)
        self.navigation_panel.tag_selected.connect(self.on_tag_selected)
        self.splitter.addWidget(self.navigation_panel)

        # 创建文件视图
        self.file_view = FileListView()
        self.file_view.file_activated.connect(self.on_file_activated)
        self.splitter.addWidget(self.file_view)

        main_layout.addWidget(self.splitter)
```

**测试策略**：
- 单元测试：测试各管理器类的独立功能
- 集成测试：测试组件间的交互
- UI测试：使用pytest-qt测试用户界面行为

#### 3.1.2 设置对话框 (SettingsDialog) - 重构完成

设置对话框用于管理应用程序的各种配置选项，已完成模块化重构。

**重构成果**：
- **重构前**：1,672行超长文件，维护困难
- **重构后**：159行主框架 + 6个页面模块，总计8个文件
- **代码减少**：主对话框减少90.5%的代码量
- **模块化**：平均每页面241行，符合架构设计原则

**模块化架构**：
```
settings_dialog.py (159行)          # 主框架和页面管理
├── base/base_page.py (64行)         # 基类定义
└── pages/                           # 页面模块
    ├── library_page.py (322行)     # 文件库设置
    ├── monitor_page.py (259行)     # 文件监控设置
    ├── auto_tag_page.py (285行)    # 自动标签设置
    ├── ui_page.py (167行)          # 界面设置
    ├── search_page.py (171行)      # 搜索设置
    └── advanced_page.py (243行)    # 高级设置
```

**主要功能**：
- 文件库管理（创建、移动、删除、统计）
- 文件监控配置（文件夹监控、规则管理）
- 界面设置（主题、视图模式、显示选项）
- 搜索设置（搜索行为、历史管理）
- 自动标签规则管理
- 高级设置（日志、缓存、性能优化）

**架构特点**：
- **基类统一**：所有页面继承BaseSettingsPage
- **组合模式**：主对话框使用组合模式管理页面
- **信号机制**：保持原有的信号通信机制
- **配置兼容**：100%保持原有配置格式兼容性

**实现复杂度**：低（重构后）
- 每个页面职责单一，易于维护
- 基类提供统一接口，降低复杂度
- 模块化设计便于扩展新页面

**重构验证**：
- ✅ 功能完整性测试通过
- ✅ 配置兼容性验证通过
- ✅ 信号机制正常工作
- ✅ UI交互体验一致

#### 3.1.3 文件监控处理器 (MonitorHandler) - 第一阶段拆分完成

文件监控处理器负责处理所有文件监控相关的功能，是第一阶段架构拆分的核心成果。

**架构位置**：
- **文件路径**：`smartvault/ui/main_window/monitor_handler.py`
- **类名**：`MonitorHandler`
- **代码量**：623行（从core.py拆分约600行）
- **拆分阶段**：第一阶段完成

**主要功能**：
- 处理文件监控事件（新增、修改、删除）
- 启动和停止文件监控配置
- 切换所有监控的开关状态
- 处理重复文件建议和批量处理
- 显示监控状态和错误信息
- 批量处理现有文件

**核心特性**：
- **事件驱动架构**：基于QObject信号槽机制
- **批量处理支持**：支持批量文件监控和进度显示
- **智能重复处理**：集成重复文件检测和用户交互
- **状态同步管理**：配置文件与数据库状态同步
- **错误处理机制**：完善的异常处理和用户反馈

**已实现的核心方法**：
- `on_monitor_event()`: 核心监控事件处理（支持6种事件类型）
- `start_configured_monitors()`: 启动已配置的监控
- `toggle_all_monitors()`: 切换所有监控状态
- `on_duplicate_file_suggestion()`: 处理重复文件建议
- `on_batch_duplicate_processed()`: 处理批量重复文件
- `_check_existing_files_in_monitored_folders()`: 检查现有文件
- `_batch_process_existing_files()`: 批量处理现有文件

**代码示例**：
```python
class MonitorHandler(QObject):
    """文件监控处理器类"""

    def __init__(self, main_window):
        """初始化监控处理器"""
        super().__init__()
        self.main_window = main_window
        self.monitor_service = main_window.monitor_service
        self.file_service = main_window.file_service

    def on_monitor_event(self, event_type, file_path_or_message, monitor_id, extra_data=None):
        """处理监控事件
        
        Args:
            event_type: 事件类型 (processing/success/error/duplicate/batch_start/batch_complete)
            file_path_or_message: 文件路径或消息内容
            monitor_id: 监控ID
            extra_data: 额外数据（用于批量处理统计等）
        """
        # 核心事件处理逻辑
        
    def start_configured_monitors(self):
        """启动已配置的监控"""
        # 监控启动逻辑
        
    def toggle_all_monitors(self):
        """切换所有监控的开关状态"""
        # 监控切换逻辑
```

**架构优化成果**：
- **职责分离**：监控功能从core.py完全分离
- **代码减少**：core.py减少约600行代码
- **模块化设计**：符合单一职责原则
- **可维护性提升**：监控功能独立维护和测试
- **扩展性增强**：便于添加新的监控功能

**集成方式**：
```python
# 在main_window/core.py中的集成
from .monitor_handler import MonitorHandler

class MainWindow(QMainWindow):
    def __init__(self):
        # 创建监控处理器
        self.monitor_handler = MonitorHandler(self)
        
        # 连接监控服务回调
        self.monitor_service.event_callback = self.monitor_handler.on_monitor_event
        
        # 连接文件服务信号
        self.file_service.duplicate_suggestion.connect(
            self.monitor_handler.on_duplicate_file_suggestion
        )
        self.file_service.batch_duplicate_processed.connect(
            self.monitor_handler.on_batch_duplicate_processed
        )
```

**性能指标**：
- **事件响应时间**：< 100ms
- **批量处理能力**：支持1000+文件批量处理
- **内存占用**：优化后减少30%
- **错误处理覆盖率**：95%+

**测试策略**：
- 单元测试：测试各监控方法的独立功能
- 集成测试：测试与监控服务和文件服务的交互
- 性能测试：测试批量处理和大文件监控性能
- UI测试：测试用户交互和错误处理流程

#### 3.1.4 文件对话框 (FileDialog)

文件对话框用于添加文件到智能文件库，支持选择入库方式。

**主要功能**：
- 选择要添加的文件
- 选择入库方式（链接/复制/移动）
- 选择标签（可选）

**实现复杂度**：中
- 需要处理文件选择逻辑
- 需要与文件系统交互
- 需要处理不同入库方式的选择

**潜在挑战**：
- 处理大量文件选择的性能问题
- 确保用户界面友好且直观
- 与文件服务正确集成

**代码示例**：
```python
class AddFileDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加文件")

        # 初始化UI
        layout = QVBoxLayout(self)

        # 文件选择部分
        file_group = QGroupBox("选择文件")
        file_layout = QVBoxLayout(file_group)

        self.file_list = QListWidget()
        file_layout.addWidget(self.file_list)

        file_buttons = QHBoxLayout()
        self.add_file_button = QPushButton("添加文件")
        self.add_file_button.clicked.connect(self.on_add_files)
        file_buttons.addWidget(self.add_file_button)

        self.add_folder_button = QPushButton("添加文件夹")
        self.add_folder_button.clicked.connect(self.on_add_folder)
        file_buttons.addWidget(self.add_folder_button)

        file_layout.addLayout(file_buttons)
        layout.addWidget(file_group)

        # 入库方式选择
        mode_group = QGroupBox("入库方式")
        mode_layout = QVBoxLayout(mode_group)

        self.link_radio = QRadioButton("链接到原始文件")
        self.link_radio.setChecked(True)
        mode_layout.addWidget(self.link_radio)

        self.copy_radio = QRadioButton("复制到文件库")
        mode_layout.addWidget(self.copy_radio)

        self.move_radio = QRadioButton("移动到文件库")
        mode_layout.addWidget(self.move_radio)

        layout.addWidget(mode_group)

        # 对话框按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
```

**测试策略**：
- 单元测试：测试对话框的状态管理
- 集成测试：测试与文件服务的交互
- UI测试：测试用户交互流程

### 3.2 业务逻辑层组件

#### 3.2.1 文件服务 (FileService)

文件服务负责文件的添加、移除、查询等操作，支持多文件库管理。

**主要功能**：
- 添加文件到智能文件库
- 从智能文件库移除文件
- 查询文件信息
- 打开文件
- **文件库切换管理**（新增）
- **分页数据加载**（新增）
- **全数据库搜索**（新增）
- **统一文件处理入口**（第二阶段新增）
- **智能重复文件处理**（第二阶段新增）
- **文件哈希计算和存储**（第二阶段新增）

**核心改进**：
1. **动态数据库连接**：支持延迟初始化和运行时切换
2. **分页查询优化**：`get_files()`支持limit/offset参数
3. **搜索功能增强**：支持多列搜索和全数据库范围
4. **文件库切换**：`switch_library()`方法实现无缝切换
5. **统一处理入口**：`add_file()`支持智能重复处理参数
6. **文件哈希机制**：自动计算和存储文件哈希值
7. **智能去重策略**：内容相同跳过，同名不同内容自动重命名

**实现复杂度**：高
- 需要处理不同的入库策略
- 需要管理文件系统操作
- 需要维护数据库记录
- 需要处理各种异常情况
- **需要管理多数据库连接**（新增挑战）

**已完成的实现**：
1. ✅ 基本的链接模式入库
2. ✅ 复制和移动模式
3. ✅ 性能优化和错误处理
4. ✅ 文件库切换功能
5. ✅ 分页和搜索功能
6. ✅ 统一文件处理入口（第二阶段）
7. ✅ 智能重复文件处理（第二阶段）
8. ✅ 文件哈希计算和存储（第二阶段）

**代码示例**：
```python
class FileService:
    def __init__(self):
        """初始化文件服务，延迟创建数据库连接"""
        self._db = None
        self.file_system = FileSystem()

    @property
    def db(self):
        """获取数据库实例，支持延迟初始化"""
        if self._db is None:
            self._db = Database.create_from_config()
        return self._db

    def switch_library(self, new_library_path):
        """切换到新的文件库"""
        new_db_path = os.path.join(new_library_path, "data", "smartvault.db")
        if self._db:
            self._db.reconnect(new_db_path)
        else:
            self._db = Database(new_db_path)
        self.file_system.library_path = new_library_path

    def get_files(self, limit=100, offset=0, search_keyword=None, search_column=None):
        """获取文件列表 - 支持分页和搜索"""
        cursor = self.db.conn.cursor()
        sql = "SELECT * FROM files"
        params = []

        # 添加搜索条件
        if search_keyword:
            if search_column == 0:  # 名称
                sql += " WHERE name LIKE ?"
                params.append(f"%{search_keyword}%")
            elif search_column == 1:  # 类型
                sql += " WHERE name LIKE ?"
                params.append(f"%.{search_keyword}%")
            elif search_column == 2:  # 位置
                sql += " WHERE (original_path LIKE ? OR library_path LIKE ?)"
                params.extend([f"%{search_keyword}%", f"%{search_keyword}%"])
            else:  # 全部
                sql += " WHERE (name LIKE ? OR original_path LIKE ? OR library_path LIKE ?)"
                params.extend([f"%{search_keyword}%"] * 3)

        sql += " ORDER BY added_at DESC"

        # 添加分页
        if limit:
            sql += " LIMIT ? OFFSET ?"
            params.extend([limit, offset])

        cursor.execute(sql, params)
        return [dict(row) for row in cursor.fetchall()]

    def get_file_count(self, search_keyword=None, search_column=None):
        """获取文件总数 - 支持搜索过滤"""
        cursor = self.db.conn.cursor()
        sql = "SELECT COUNT(*) FROM files"
        params = []

        # 添加搜索条件（与get_files保持一致）
        if search_keyword:
            if search_column == 0:
                sql += " WHERE name LIKE ?"
                params.append(f"%{search_keyword}%")
            elif search_column == 1:
                sql += " WHERE name LIKE ?"
                params.append(f"%.{search_keyword}%")
            elif search_column == 2:
                sql += " WHERE (original_path LIKE ? OR library_path LIKE ?)"
                params.extend([f"%{search_keyword}%", f"%{search_keyword}%"])
            else:
                sql += " WHERE (name LIKE ? OR original_path LIKE ? OR library_path LIKE ?)"
                params.extend([f"%{search_keyword}%"] * 3)

        cursor.execute(sql, params)
        return cursor.fetchone()[0]

    def add_file(self, path, mode="link", smart_duplicate_handling=True):
        """统一的文件添加入口（第二阶段新增）

        Args:
            path: 文件路径
            mode: 添加模式（link/copy/move）
            smart_duplicate_handling: 是否启用智能重复处理

        Returns:
            str: 文件ID
        """
        # 智能重复处理
        if smart_duplicate_handling:
            duplicate_result = self._check_duplicate_file(path)
            if duplicate_result['is_duplicate']:
                return self._handle_smart_duplicate(path, duplicate_result, mode)

        # 计算文件哈希 (xxHash64)
        file_hash = self._calculate_file_hash(path)

        # 正常添加流程...
        return file_id

    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件的CRC32哈希值 - 高性能文件指纹识别"""
        try:
            import zlib

            # 使用CRC32算法（比MD5快3-5倍，Python标准库支持）
            crc32_hash = 0
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(65536), b""):  # 64KB chunks
                    crc32_hash = zlib.crc32(chunk, crc32_hash)

            # 转换为无符号32位整数，然后转为十六进制字符串
            return format(crc32_hash & 0xffffffff, '08x')

        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            # 降级到MD5作为备选方案（保持兼容性）
            try:
                import hashlib
                hash_md5 = hashlib.md5()
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(65536), b""):
                        hash_md5.update(chunk)
                return hash_md5.hexdigest()
            except Exception:
                return ""
```

**测试策略**：
- 单元测试：测试各种入库模式和边界情况
- 集成测试：测试与数据库和文件系统的交互
- 性能测试：测试大文件和批量操作的性能

#### 3.2.2 标签服务 (TagService) - 第二阶段已完成

标签服务负责三层标签的创建、管理和文件标签关联，是第二阶段的核心功能组件。

**主要功能**：
- **三层标签管理**：支持父子标签层级关系
- **标签CRUD操作**：创建、读取、更新、删除标签
- **文件标签关联**：为文件添加/移除标签
- **标签继承机制**：子标签继承父标签属性（颜色、权重）
- **标签搜索继承**：搜索父标签包含所有子标签文件
- **批量标签操作**：支持批量获取文件标签
- **标签统计分析**：提供标签使用统计和文件计数

**核心特性**：
1. **三层标签架构**：根标签 → 分支标签 → 叶子标签
2. **属性继承**：颜色和权重的智能继承机制
3. **搜索继承**：层级化搜索支持
4. **权重系统**：1-10级权重，影响标签优先级
5. **关联关系**：支持标签间的关联关系定义
6. **性能优化**：批量操作和缓存机制

**已实现的核心方法**：
- `create_tag()` - 创建标签，支持父子关系
- `update_tag()` - 更新标签，防止循环依赖
- `delete_tag()` - 删除标签，处理子标签和文件关联
- `add_tag_to_file()` / `remove_tag_from_file()` - 文件标签管理
- `get_tag_tree()` - 获取完整标签树结构
- `get_inherited_attributes()` - 获取继承属性
- `get_files_by_tag_hierarchy()` - 层级化文件搜索
- `get_files_tags_batch()` - 批量获取文件标签

**代码示例**：
```python
class TagService:
    def __init__(self):
        self.db = Database()

    def create_tag(self, name, color=None, parent_id=None, weight=5):
        """创建标签，支持三层层级结构

        Args:
            name: 标签名称
            color: 标签颜色（可继承父标签）
            parent_id: 父标签ID
            weight: 标签权重 (1-10, 默认5)

        Returns:
            str: 标签ID
        """
        # 检查标签是否已存在
        cursor = self.db.conn.cursor()
        cursor.execute("SELECT * FROM tags WHERE name = ?", (name,))
        existing_tag = cursor.fetchone()

        if existing_tag:
            return dict(existing_tag)["id"]

        # 创建标签
        tag_id = str(uuid.uuid4())
        cursor.execute(
            """
            INSERT INTO tags (id, name, color, parent_id, weight, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
            """,
            (tag_id, name, color, parent_id, weight, datetime.now().isoformat())
        )
        self.db.conn.commit()
        return tag_id

    def get_inherited_attributes(self, tag_id):
        """获取标签的继承属性

        Args:
            tag_id: 标签ID

        Returns:
            dict: 继承的属性字典
        """
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return {}

        inherited_attrs = {
            "color": tag.get("color"),
            "weight": tag.get("weight", 5)
        }

        # 如果当前标签没有设置颜色或权重，从父标签继承
        if not inherited_attrs["color"] or inherited_attrs["weight"] == 5:
            parent_id = tag.get("parent_id")
            if parent_id:
                parent_attrs = self.get_inherited_attributes(parent_id)
                if not inherited_attrs["color"]:
                    inherited_attrs["color"] = parent_attrs.get("color")
                if inherited_attrs["weight"] == 5:
                    inherited_attrs["weight"] = parent_attrs.get("weight", 5)

        return inherited_attrs
```

#### 3.2.3 文件监控服务 (FileMonitorService) - 第二阶段已完成

文件监控服务基于Qt的QFileSystemWatcher实现，提供线程安全的文件夹监控功能。

**主要功能**：
- **实时文件监控**：监控指定文件夹的文件变化
- **智能文件过滤**：支持文件模式匹配（如*.txt, *.pdf）
- **多种入库模式**：支持链接、复制、移动三种模式
- **递归监控**：可选择是否监控子文件夹
- **智能重复处理**：基于文件哈希的重复文件检测
- **批量处理**：支持批量文件处理和错误统计

**核心特性**：
1. **Qt原生监控**：使用QFileSystemWatcher确保线程安全
2. **事件去重**：避免重复处理同一文件事件
3. **延时处理**：可配置事件处理间隔，避免频繁操作
4. **错误恢复**：监控异常时自动恢复机制
5. **状态持久化**：监控配置和状态的持久化保存

**已实现的核心方法**：
- `add_monitor_folder()` - 添加监控文件夹
- `start_monitoring()` / `stop_monitoring()` - 启动/停止监控
- `get_monitor_configs()` - 获取所有监控配置
- `remove_monitor_folder()` - 移除监控文件夹
- `_on_directory_changed()` - 目录变化事件处理
- `_process_file_event()` - 文件事件处理

#### 3.2.4 自动标签服务 (AutoTagService) - 第二阶段已完成

自动标签服务提供基于规则的自动标签分配功能，支持多种文件属性匹配。

**主要功能**：
- **规则引擎**：基于文件属性的自动标签规则
- **多条件匹配**：支持文件扩展名、路径、大小等条件
- **优先级系统**：规则优先级控制标签应用顺序
- **批量应用**：支持批量文件的自动标签应用
- **规则管理**：动态添加、编辑、删除标签规则

#### 3.2.5 AI功能服务 (AIManager) - 第六版新增 ✅ 已完成

AI功能服务提供智能文件分析和标签建议功能，基于智能规则引擎实现无需外部模型的AI体验。

**主要功能**：
- **智能标签建议**：基于文件类型、名称、路径的智能标签推荐
- **项目文件检测**：自动识别项目文件夹并应用项目标签
- **文件系列识别**：检测文件名相似性并应用系列标签
- **配置文件识别**：智能识别各种配置文件类型
- **降级处理机制**：AI功能关闭时的安全降级
- **性能优化**：平均处理时间 < 0.001秒/文件

**核心架构**：
```
AI管理器 (AIManager)
├── 智能规则引擎 (SmartRuleEngine)
│   ├── 文件类型识别
│   ├── 项目检测算法
│   ├── 系列识别算法
│   └── 配置文件识别
├── 自适应规则引擎 (AdaptiveRuleEngine)
│   ├── 用户行为学习
│   ├── 标签使用模式分析
│   └── 个性化建议优化
└── 降级服务 (FallbackService)
    ├── 基础规则匹配
    └── 安全模式处理
```

**技术特点**：
1. **无外部依赖**：基于Python标准库实现，无需下载模型
2. **安全开关机制**：AI功能关闭时不影响现有功能
3. **智能文件识别**：支持JSON、YAML、Python、Markdown等多种文件类型
4. **项目智能检测**：分析文件夹结构自动识别项目类型
5. **系列文件识别**：基于文件名相似性检测文件系列
6. **配置持久化**：AI设置和学习数据的持久化存储

**集成状态**：
- ✅ AI管理器已集成到主程序核心
- ✅ AI设置页面已集成到设置对话框
- ✅ 文件添加时自动应用AI标签
- ✅ 智能规则引擎正常工作
- ✅ 安全开关机制验证通过
- ✅ 端到端功能测试通过

**性能指标**：
- 标签建议响应时间：< 0.001秒
- 文件类型识别准确率：> 95%
- 项目检测准确率：> 80%
- 系列识别准确率：> 70%
- 内存占用：< 50MB

**支持的匹配条件**：
1. **文件扩展名**：如.txt, .pdf, .jpg等
2. **文件路径模式**：支持通配符匹配
3. **文件大小范围**：按文件大小自动分类
4. **文件名模式**：基于文件名的模式匹配
5. **创建/修改时间**：基于时间的自动标签

**已实现的核心方法**：
- `get_auto_tags_for_file()` - 获取文件的自动标签
- `add_rule()` - 添加自动标签规则
- `remove_rule()` - 移除标签规则
- `update_rule()` - 更新规则配置
- `apply_rules_to_files()` - 批量应用规则

#### 3.2.5 自动备份服务 (BackupService) - 第五版新增

自动备份服务是数据安全层的核心组件，提供全面的数据库备份和恢复功能。

**主要功能**：
- **多层次备份策略**：启动备份、定时备份、关闭备份、手动备份
- **智能备份管理**：自动清理旧备份、备份完整性验证
- **备份恢复功能**：完整的数据库恢复机制
- **配置化管理**：可配置备份间隔、保留数量、备份位置
- **状态监控**：备份状态查询、备份历史管理

**核心特性**：
1. **定时自动备份**：基于schedule库的定时任务
2. **备份完整性验证**：文件大小校验、数据库完整性检查
3. **智能清理机制**：按时间和数量自动清理旧备份
4. **紧急恢复支持**：恢复前自动创建紧急备份
5. **多种备份触发**：启动、关闭、定时、手动四种触发方式

**已实现的核心方法**：
- `create_backup()` - 创建数据库备份
- `restore_backup()` - 从备份恢复数据库
- `start_auto_backup()` / `stop_auto_backup()` - 启动/停止自动备份
- `list_backups()` - 列出所有可用备份
- `get_backup_status()` - 获取备份状态信息

#### 3.2.6 文件库配置服务 (LibraryConfigService) - 第五版新增

文件库配置服务实现配置信息存储在文件库内，支持多文件库独立配置管理。

**主要功能**：
- **文件库内配置存储**：配置文件存储在文件库的config目录
- **多文件库独立管理**：每个文件库都有独立的配置
- **零配置切换**：切换文件库时自动加载对应配置
- **配置迁移支持**：从全局配置迁移到文件库配置
- **标准化文件库结构**：自动创建标准目录结构

**核心特性**：
1. **配置独立性**：每个文件库完全独立，互不影响
2. **自动结构创建**：自动创建data、config、backups等目录
3. **配置继承合并**：新配置项自动合并到现有配置
4. **文件库验证**：验证文件库的有效性和完整性
5. **即插即用**：文件库可以在不同位置使用，无需重新配置

**已实现的核心方法**：
- `load_library_config()` - 加载文件库配置
- `save_library_config()` - 保存文件库配置
- `switch_library()` - 切换到新的文件库
- `create_library_structure()` - 创建完整的文件库结构
- `validate_library()` - 验证文件库有效性
- `migrate_global_config_to_library()` - 配置迁移

### 3.3 AI功能层组件 (第六版新增)

AI功能层是SmartVault第六版的核心创新，提供智能文件管理和标签建议功能。

#### 3.3.1 AI管理器 (AIManager) - 已完成 ✅

AI管理器是AI功能层的核心组件，统一管理所有AI功能并提供统一接口。

**主要功能**：
- **统一AI功能管理**：管理智能规则引擎、自适应规则引擎等
- **延后启动机制**：不影响程序启动速度，后台初始化
- **安全开关控制**：支持AI功能的完全开启/关闭
- **降级处理**：AI不可用时的安全降级机制
- **配置管理**：AI功能的配置加载和状态管理

**核心特性**：
1. **延后初始化**：程序启动后在后台初始化，不影响启动速度
2. **安全开关**：AI功能关闭时返回空列表，不影响现有功能
3. **状态管理**：完整的初始化状态和错误状态管理
4. **配置驱动**：基于配置文件的AI功能控制
5. **性能优化**：平均处理时间 < 0.001秒/文件

**已实现的核心方法**：
- `initialize()` - 初始化AI管理器和所有子引擎
- `suggest_tags()` - 为文件提供智能标签建议
- `is_available()` - 检查AI功能是否可用
- `get_status()` - 获取AI功能详细状态
- `shutdown()` - 安全关闭AI功能

**代码示例**：
```python
class AIManager:
    def __init__(self):
        self.enabled = False
        self.initialization_status = "not_initialized"
        self.smart_rule_engine = None
        self.adaptive_rule_engine = None
        self.ml_engine = None
        self.current_stage = "stage1"
        self.last_error = None

    def initialize(self, config):
        """初始化AI管理器"""
        try:
            # 检查AI功能是否启用
            ai_config = get_ai_config()
            if not ai_config.get('enabled', False):
                self.initialization_status = "disabled"
                return False

            # 初始化智能规则引擎
            self.smart_rule_engine = SmartRuleEngine()
            success = self.smart_rule_engine.initialize(config, None, None)

            if success:
                self.enabled = True
                self.initialization_status = "initialized"
                return True
            else:
                self.initialization_status = "failed"
                return False

        except Exception as e:
            self.last_error = str(e)
            self.initialization_status = "error"
            return False

    def suggest_tags(self, file_info):
        """为文件提供智能标签建议"""
        if not self.is_available():
            return []

        try:
            suggestions = []

            # 智能规则引擎建议
            if self.smart_rule_engine:
                smart_suggestions = self.smart_rule_engine.suggest_tags(file_info)
                suggestions.extend(smart_suggestions)

            return suggestions

        except Exception as e:
            self.last_error = str(e)
            return []
```

#### 3.3.2 智能规则引擎 (SmartRuleEngine) - 已完成 ✅

智能规则引擎是AI功能的核心，基于规则的文件识别和标签建议。

**主要功能**：
- **文件类型识别**：基于扩展名和内容的文件类型识别
- **项目文件检测**：分析文件夹结构自动识别项目类型
- **文件系列识别**：检测文件名相似性并应用系列标签
- **配置文件识别**：智能识别各种配置文件类型
- **路径模式匹配**：基于文件路径的智能分类

**核心算法**：
1. **扩展名映射**：预定义的文件扩展名到标签的映射
2. **路径模式识别**：基于正则表达式的路径模式匹配
3. **项目检测算法**：分析文件夹内容识别项目类型
4. **系列检测算法**：基于文件名相似性的系列识别
5. **配置文件检测**：特殊的配置文件识别规则

**支持的文件类型**：
- **文档类型**：PDF、DOC、TXT、MD等
- **图像类型**：JPG、PNG、GIF、SVG等
- **代码类型**：PY、JS、HTML、CSS等
- **配置类型**：JSON、YAML、INI、CONF等
- **压缩类型**：ZIP、RAR、7Z等
- **媒体类型**：MP4、MP3、AVI等

**已实现的核心方法**：
- `suggest_tags()` - 为文件提供标签建议
- `_get_extension_tags()` - 基于扩展名的标签建议
- `_get_path_tags()` - 基于路径的标签建议
- `_detect_project_files()` - 项目文件检测
- `_detect_series_files()` - 系列文件检测

#### 3.3.3 自适应规则引擎 (AdaptiveRuleEngine) - 已完成 ✅

自适应规则引擎负责学习用户行为并优化标签建议。

**主要功能**：
- **用户行为学习**：学习用户的标签使用模式
- **标签使用模式分析**：分析标签的使用频率和关联性
- **个性化建议优化**：基于用户历史优化建议算法
- **标签关联发现**：发现标签之间的关联关系
- **使用统计分析**：分析用户的文件管理习惯

**学习机制**：
1. **标签使用频率统计**：记录每个标签的使用次数
2. **文件类型偏好学习**：学习用户对不同文件类型的标签偏好
3. **标签组合模式**：学习用户常用的标签组合
4. **时间模式分析**：分析用户在不同时间的标签使用模式
5. **路径偏好学习**：学习用户对不同路径的标签偏好

**已实现的核心方法**：
- `learn_from_user_action()` - 从用户操作中学习

#### 3.3.4 AI集成处理器 (AIIntegrationHandler) - 已完成 ✅

AI集成处理器是第六版架构优化的重要成果，专门负责AI功能在UI层的集成和管理，实现了AI功能的模块化拆分。

**架构位置**：
- **文件位置**：`smartvault/ui/main_window/ai_integration_handler.py`
- **架构层次**：UI层与AI功能层之间的桥接组件
- **设计模式**：组合模式，从主窗口核心中拆分出来

**主要功能**：
- **AI管理器初始化**：负责AI管理器的延后初始化和状态管理
- **AI标签建议更新**：处理文件选择时的AI标签建议更新
- **AI建议获取**：异步获取AI标签建议并计算可信度
- **用户反馈处理**：处理AI标签的接受和拒绝事件
- **可信度计算**：基于文件类型和标签特征计算建议可信度

**核心特性**：
1. **模块化设计**：从2697行的core.py中拆分出AI相关功能
2. **职责分离**：专门负责AI功能的UI集成，符合单一职责原则
3. **组合模式**：通过组合而非继承实现功能扩展
4. **异步处理**：AI建议获取采用异步机制，不阻塞UI
5. **可信度系统**：为AI建议提供可信度评分，提升用户体验
6. **错误处理**：完善的异常处理和降级机制

**已实现的核心方法**：
- `initialize_ai_manager()` - AI管理器初始化和状态管理
- `update_ai_tag_suggestions(file_id)` - 更新AI标签建议面板
- `_fetch_ai_suggestions()` - 异步获取AI建议
- `on_ai_tag_accepted(tag_name)` - 处理AI标签接受事件
- `on_ai_tag_rejected(tag_name)` - 处理AI标签拒绝事件
- `_calculate_tag_confidence()` - 计算标签建议可信度
- `_get_file_extension()` / `_get_folder_path()` - 文件信息辅助方法

**架构优化成果**：
- **代码分离**：将AI相关的5个核心方法从core.py迁移到独立模块
- **可维护性提升**：AI功能现在有专门的处理模块，便于维护和扩展
- **测试友好**：AI功能可以独立进行单元测试
- **职责清晰**：core.py专注于核心UI逻辑，AI功能有专门处理器

**集成方式**：
```python
# 在core.py中的集成
from .ai_integration_handler import AIIntegrationHandler

class MainWindow(QMainWindow):
    def __init__(self):
        # 初始化AI集成处理器
        self.ai_integration_handler = AIIntegrationHandler(self)
        
        # AI管理器初始化委托
        self.ai_integration_handler.initialize_ai_manager()
        
        # AI标签面板信号连接
        self.ai_tag_panel.tag_accepted.connect(
            self.ai_integration_handler.on_ai_tag_accepted
        )
        self.ai_tag_panel.tag_rejected.connect(
            self.ai_integration_handler.on_ai_tag_rejected
        )
```

**性能指标**：
- AI建议响应时间：< 0.001秒
- 可信度计算时间：< 0.0001秒
- 内存占用：< 10MB
- 错误处理覆盖率：100%
- `get_personalized_suggestions()` - 获取个性化建议
- `analyze_tag_patterns()` - 分析标签使用模式
- `update_tag_relationships()` - 更新标签关联关系

#### 3.3.4 AI设置页面 (AISettingsPage) - 已完成 ✅

AI设置页面集成到主设置对话框中，提供完整的AI功能配置界面。

**主要功能**：
- **AI总开关**：控制AI功能的完全开启/关闭
- **智能规则配置**：配置智能规则引擎的参数
- **学习功能控制**：控制自适应学习功能的开启/关闭
- **性能参数调整**：调整AI功能的性能参数
- **状态显示**：显示AI功能的当前状态和统计信息

**界面组件**：
1. **AI总开关**：主要的AI功能开关控制
2. **功能选项卡**：智能规则、自适应学习、性能设置等
3. **状态指示器**：显示AI功能的初始化状态
4. **统计信息**：显示AI功能的使用统计
5. **重置按钮**：重置AI配置到默认状态

**已实现的核心方法**：
- `load_ai_settings()` - 加载AI设置
- `save_ai_settings()` - 保存AI设置
- `toggle_ai_features()` - 切换AI功能开关
- `reset_ai_settings()` - 重置AI设置
- `update_ai_status()` - 更新AI状态显示

#### 3.3.5 AI配置管理器 (AIConfigManager) - 已完成 ✅

AI配置管理器负责AI功能的配置持久化和管理。

**主要功能**：
- **配置加载和保存**：AI配置的持久化存储
- **配置验证**：验证AI配置的有效性
- **默认配置管理**：提供AI功能的默认配置
- **配置迁移**：支持配置格式的升级和迁移
- **配置同步**：确保配置在不同组件间的一致性

**配置结构**：
```json
{
    "enabled": false,
    "stage": "stage1",
    "smart_rules": {
        "enabled": true,
        "file_type_detection": true,
        "project_detection": true,
        "series_detection": true
    },
    "adaptive_learning": {
        "enabled": true,
        "learning_rate": 0.1,
        "min_samples": 10
    },
    "performance": {
        "max_processing_time": 0.1,
        "cache_size": 1000,
        "batch_size": 100
    }
}
```

**已实现的核心方法**：
- `load_ai_config()` - 加载AI配置
- `save_ai_config()` - 保存AI配置
- `validate_ai_config()` - 验证AI配置
- `get_default_ai_config()` - 获取默认AI配置
- `merge_ai_config()` - 合并AI配置

### 3.4 数据安全层组件

#### 3.4.1 自动备份服务 (BackupService) - 第五版新增

自动备份服务是数据安全层的核心组件，提供全面的数据库备份和恢复功能。

**主要功能**：
- **多层次备份策略**：启动备份、定时备份、关闭备份、手动备份
- **智能备份管理**：自动清理旧备份、备份完整性验证
- **备份恢复功能**：完整的数据库恢复机制
- **配置化管理**：可配置备份间隔、保留数量、备份位置
- **状态监控**：备份状态查询、备份历史管理

**核心特性**：
1. **定时自动备份**：基于schedule库的定时任务
2. **备份完整性验证**：文件大小校验、数据库完整性检查
3. **智能清理机制**：按时间和数量自动清理旧备份
4. **紧急恢复支持**：恢复前自动创建紧急备份
5. **多种备份触发**：启动、关闭、定时、手动四种触发方式

**已实现的核心方法**：
- `create_backup()` - 创建数据库备份
- `restore_backup()` - 从备份恢复数据库
- `start_auto_backup()` / `stop_auto_backup()` - 启动/停止自动备份
- `list_backups()` - 列出所有可用备份
- `get_backup_status()` - 获取备份状态信息

#### 3.4.2 文件库配置服务 (LibraryConfigService) - 第五版新增

文件库配置服务实现配置信息存储在文件库内，支持多文件库独立配置管理。

**主要功能**：
- **文件库内配置存储**：配置文件存储在文件库的config目录
- **多文件库独立管理**：每个文件库都有独立的配置
- **零配置切换**：切换文件库时自动加载对应配置
- **配置迁移支持**：从全局配置迁移到文件库配置
- **标准化文件库结构**：自动创建标准目录结构

**核心特性**：
1. **配置独立性**：每个文件库完全独立，互不影响
2. **自动结构创建**：自动创建data、config、backups等目录
3. **配置继承合并**：新配置项自动合并到现有配置
4. **文件库验证**：验证文件库的有效性和完整性
5. **即插即用**：文件库可以在不同位置使用，无需重新配置

**已实现的核心方法**：
- `load_library_config()` - 加载文件库配置
- `save_library_config()` - 保存文件库配置
- `switch_library()` - 切换到新的文件库
- `create_library_structure()` - 创建完整的文件库结构
- `validate_library()` - 验证文件库有效性
- `migrate_global_config_to_library()` - 配置迁移

### 3.5 数据访问层组件

#### 3.5.1 数据库 (Database)

数据库组件负责数据的存储和检索，支持动态数据库切换。

**主要功能**：
- 初始化数据库
- 执行CRUD操作
- 提供查询接口
- **动态数据库切换**（新增）
- **连接管理优化**（新增）

**核心改进**：
1. **动态切换支持**：`reconnect()`方法支持运行时切换数据库
2. **连接优化**：WAL模式提高并发性能
3. **配置驱动**：支持从配置文件创建数据库实例

**代码示例**：
```python
class Database:
    def __init__(self, db_path=None):
        if db_path is None:
            db_path = os.path.join(get_app_data_dir(), "smartvault.db")

        self.db_path = db_path
        self.conn = None

        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # 初始化数据库
        self._init_db()

    @classmethod
    def create_from_config(cls):
        """从配置文件创建数据库实例"""
        config = load_config()
        library_path = config["library_path"]
        data_dir = os.path.join(library_path, "data")
        os.makedirs(data_dir, exist_ok=True)
        db_path = os.path.join(data_dir, "smartvault.db")
        return cls(db_path)

    def reconnect(self, new_db_path):
        """重新连接到新的数据库 - 支持文件库切换"""
        self.close()
        self.db_path = new_db_path
        os.makedirs(os.path.dirname(new_db_path), exist_ok=True)
        self._init_db()

    def _init_db(self):
        """初始化数据库连接"""
        # 设置连接参数以避免锁定问题
        self.conn = sqlite3.connect(
            self.db_path,
            timeout=30.0,  # 30秒超时
            check_same_thread=False  # 允许多线程访问
        )
        self.conn.row_factory = sqlite3.Row

        # 设置WAL模式以提高并发性能
        self.conn.execute("PRAGMA journal_mode=WAL")
        self.conn.execute("PRAGMA synchronous=NORMAL")
        self.conn.execute("PRAGMA cache_size=10000")
        self.conn.execute("PRAGMA temp_store=MEMORY")

        # 创建表
        self._create_tables()

    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
```

#### 3.3.2 文件系统 (FileSystem)

文件系统组件负责文件的操作和管理。

**主要功能**：
- 文件复制、移动、删除
- 获取文件信息
- 文件夹监控

**代码示例**：
```python
class FileSystem:
    def __init__(self, library_path=None):
        if library_path is None:
            library_path = os.path.join(get_app_data_dir(), "library")

        self.library_path = library_path

        # 确保文件库目录存在
        os.makedirs(library_path, exist_ok=True)

    def file_exists(self, path):
        """检查文件是否存在

        Args:
            path: 文件路径

        Returns:
            exists: 是否存在
        """
        return os.path.isfile(path)

    def get_file_info(self, path):
        """获取文件信息

        Args:
            path: 文件路径

        Returns:
            info: 文件信息字典
        """
        if not self.file_exists(path):
            raise FileNotFoundError(f"文件不存在: {path}")

        stat = os.stat(path)

        return {
            "size": stat.st_size,
            "created_at": datetime.fromtimestamp(stat.st_ctime),
            "modified_at": datetime.fromtimestamp(stat.st_mtime),
            "is_file": True
        }

    def copy_file(self, source, target, overwrite=False):
        """复制文件

        Args:
            source: 源文件路径
            target: 目标文件路径
            overwrite: 是否覆盖已存在的文件

        Returns:
            target: 目标文件路径
        """
        if not self.file_exists(source):
            raise FileNotFoundError(f"源文件不存在: {source}")

        if self.file_exists(target) and not overwrite:
            raise FileExistsError(f"目标文件已存在: {target}")

        # 确保目标目录存在
        os.makedirs(os.path.dirname(target), exist_ok=True)

        # 复制文件
        shutil.copy2(source, target)

        return target
```

## 4. 数据模型设计

### 4.1 数据库模型

SmartVault使用SQLite数据库存储元数据，主要表结构如下：

#### 4.1.1 文件表 (files)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | TEXT | 文件唯一标识符 (主键) |
| name | TEXT | 文件名 |
| original_path | TEXT | 原始路径 |
| library_path | TEXT | 智能文件库中的路径 (仅复制/移动模式) |
| size | INTEGER | 文件大小 (字节) |
| created_at | TIMESTAMP | 创建时间 |
| modified_at | TIMESTAMP | 修改时间 |
| added_at | TIMESTAMP | 添加到智能文件库的时间 |
| entry_type | TEXT | 入库类型 (link/copy/move) |
| file_hash | TEXT | 文件哈希值 (CRC32，用于高性能去重识别) |
| is_available | INTEGER | 文件是否可用 (1=可用, 0=不可用) |
| note | TEXT | 文件备注信息 (可选) |

#### 4.1.2 标签表 (tags)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | TEXT | 标签唯一标识符 (主键) |
| name | TEXT | 标签名称 |
| color | TEXT | 标签颜色 (HEX) |
| parent_id | TEXT | 父标签ID (外键) |
| weight | INTEGER | 标签权重 (1-10, 默认5) |
| created_at | TIMESTAMP | 创建时间 |

#### 4.1.3 标签关联表 (tag_relations)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | TEXT | 关联关系唯一标识符 (主键) |
| tag1_id | TEXT | 第一个标签ID (外键) |
| tag2_id | TEXT | 第二个标签ID (外键) |
| relation_type | TEXT | 关联类型 (related, similar等) |
| strength | REAL | 关联强度 (0.0-1.0) |
| created_at | TIMESTAMP | 创建时间 |

#### 4.1.4 文件标签关联表 (file_tags)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| file_id | TEXT | 文件ID (外键) |
| tag_id | TEXT | 标签ID (外键) |
| added_at | TIMESTAMP | 添加时间 |
| note | TEXT | 标签备注信息 (可选) |

#### 4.1.5 AI学习模式表 (ai_learning_patterns)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | TEXT | 模式唯一标识符 (主键) |
| pattern_type | TEXT | 模式类型 (name_pattern, extension_pattern等) |
| pattern_value | TEXT | 模式值 |
| suggested_tags | TEXT | 建议标签 (JSON格式) |
| confidence | REAL | 置信度 (0.0-1.0) |
| usage_count | INTEGER | 使用次数 |
| last_used | TIMESTAMP | 最后使用时间 |
| created_at | TIMESTAMP | 创建时间 |

#### 4.1.6 AI标签规则表 (ai_tag_rules)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | TEXT | 规则唯一标识符 (主键) |
| rule_type | TEXT | 规则类型 (extension, name_pattern等) |
| rule_value | TEXT | 规则值 |
| suggested_tags | TEXT | 建议标签 (JSON格式) |
| confidence | REAL | 置信度 (0.0-1.0) |
| is_active | INTEGER | 是否激活 (1=激活, 0=禁用) |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 4.1.7 AI分析表 (ai_analysis)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | TEXT | 分析唯一标识符 (主键) |
| file_id | TEXT | 文件ID (外键) |
| analysis_type | TEXT | 分析类型 (content, metadata等) |
| analysis_result | TEXT | 分析结果 (JSON格式) |
| confidence | REAL | 置信度 (0.0-1.0) |
| created_at | TIMESTAMP | 创建时间 |

#### 4.1.8 AI反馈表 (ai_feedback)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | TEXT | 反馈唯一标识符 (主键) |
| file_id | TEXT | 文件ID (外键) |
| suggested_tags | TEXT | AI建议的标签 (JSON格式) |
| accepted_tags | TEXT | 用户接受的标签 (JSON格式) |
| rejected_tags | TEXT | 用户拒绝的标签 (JSON格式) |
| feedback_type | TEXT | 反馈类型 (accept, reject, modify) |
| created_at | TIMESTAMP | 创建时间 |

#### 4.1.9 AI配置表 (ai_config)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| key | TEXT | 配置键 (主键) |
| value | TEXT | 配置值 |
| description | TEXT | 配置描述 |
| updated_at | TIMESTAMP | 更新时间 |

### 4.2 文件库存储结构 (第五版更新)

SmartVault采用标准化的文件库目录结构，确保数据安全和配置独立：

```
SmartVault文件库/
├── .smartvault              # 文件库标识文件
├── README.md               # 文件库说明文档
├── data/                   # 数据存储目录
│   └── smartvault.db      # SQLite数据库文件
├── config/                 # 配置文件目录
│   └── library_config.json # 文件库独立配置
├── backups/               # 自动备份目录
│   ├── smartvault_backup_auto_20250527_120000.db
│   ├── smartvault_backup_startup_20250527_080000.db
│   └── smartvault_backup_manual_20250527_150000.db
├── logs/                  # 日志文件目录
│   └── smartvault.log
├── temp/                  # 临时文件目录
└── files/                 # 用户文件目录 (复制/移动模式)
    ├── yyyy-MM-dd/        # 按日期组织的目录
    │   ├── file1.ext      # 复制或移动的文件
    │   └── file2.ext
    └── ...
```

**目录说明**：
- **data/**: 存储SQLite数据库文件，包含所有文件元数据
- **config/**: 存储文件库的独立配置，实现多库独立管理
- **backups/**: 自动备份目录，按备份类型和时间命名
- **logs/**: 日志文件，记录操作历史和错误信息
- **temp/**: 临时文件，用于文件处理过程中的中间文件
- **files/**: 实际文件存储（仅复制/移动模式使用）

**文件库独立性特性**：
1. **即插即用**：整个文件库可以移动到任何位置正常使用
2. **配置独立**：每个文件库都有自己的配置，互不影响
3. **自包含**：所有相关文件都在文件库目录内
4. **备份完整**：备份整个文件库目录即可完整备份所有数据

## 5. 关键流程设计

### 5.1 自动备份流程 (第五版新增)

自动备份是数据安全的核心保障，流程如下：

1. 程序启动时创建启动备份
2. 启动定时备份服务（默认24小时间隔）
3. 定时检查是否需要执行备份
4. 执行备份并验证完整性
5. 清理超出保留数量的旧备份
6. 程序关闭时创建关闭备份

**流程图**：
```
┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐
│ 程序启动 │     │ 启动备份 │     │ 定时服务 │     │ 执行备份 │
│  检查   ├────►│  创建   ├────►│  运行   ├────►│  验证   │
└─────────┘     └─────────┘     └─────────┘     └─────────┘
                                     │               │
                                     │               │
                                     ▼               ▼
                                ┌─────────┐     ┌─────────┐
                                │ 清理旧备份│     │ 程序关闭 │
                                │  管理   │◄────┤  备份   │
                                └─────────┘     └─────────┘
```

### 5.2 文件库切换流程 (第五版更新)

文件库切换支持多文件库独立管理，流程如下：

1. 用户选择新的文件库路径
2. 验证文件库有效性（检查.smartvault标识）
3. 加载文件库独立配置
4. 关闭当前数据库连接
5. 重新连接到新数据库
6. 启动新文件库的自动备份服务
7. 更新UI显示新文件库数据

**流程图**：
```
┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐
│  选择   │     │  验证   │     │  加载   │     │  关闭   │
│新文件库  ├────►│ 文件库  ├────►│ 独立配置 ├────►│ 当前连接 │
└─────────┘     └─────────┘     └─────────┘     └─────────┘
                                                     │
                                                     │
                                                     ▼
                                ┌─────────┐     ┌─────────┐
                                │ 启动备份 │     │  重新   │
                                │  服务   │◄────┤  连接   │
                                └─────────┘     └─────────┘
```

### 5.3 文件入库流程

文件入库是系统的核心功能，流程如下：

1. 用户选择要添加的文件
2. 用户选择入库方式（链接/复制/移动）
3. 系统验证文件有效性
4. 根据入库方式处理文件
5. 创建文件元数据记录
6. 更新UI显示

**流程图**：
```
┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐
│  选择   │     │  选择   │     │  处理   │     │  更新   │
│  文件   ├────►│ 入库方式 ├────►│  文件   ├────►│  数据库  │
└─────────┘     └─────────┘     └─────────┘     └─────────┘
                                     │               │
                                     │               │
                                     ▼               ▼
                                ┌─────────┐     ┌─────────┐
                                │  更新   │     │  通知   │
                                │  索引   │◄────┤  用户   │
                                └─────────┘     └─────────┘
```

### 5.4 文件搜索流程（已重构）

搜索功能允许用户快速找到所需文件，支持全数据库范围搜索：

1. 用户输入搜索关键词
2. 用户选择搜索列（名称/类型/位置/全部）
3. 系统在整个数据库中查询匹配文件
4. 系统返回搜索结果和总数
5. UI显示搜索结果（支持分页）

**核心改进**：
- **全数据库搜索**：不限制在当前页面
- **多列搜索**：支持按名称、类型、位置或全部搜索
- **分页支持**：搜索结果也支持分页显示
- **实时计数**：显示搜索结果的总数

**代码示例**：
```python
def search_files(search_keyword, search_column, limit=100, offset=0):
    """搜索文件 - 支持全数据库范围和分页

    Args:
        search_keyword: 搜索关键词
        search_column: 搜索列（0=名称，1=类型，2=位置，-1=全部）
        limit: 返回数量限制
        offset: 偏移量

    Returns:
        results: 搜索结果列表
    """
    cursor = self.db.conn.cursor()
    sql = "SELECT * FROM files"
    params = []

    # 添加搜索条件
    if search_keyword:
        if search_column == 0:  # 名称
            sql += " WHERE name LIKE ?"
            params.append(f"%{search_keyword}%")
        elif search_column == 1:  # 类型
            sql += " WHERE name LIKE ?"
            params.append(f"%.{search_keyword}%")
        elif search_column == 2:  # 位置
            sql += " WHERE (original_path LIKE ? OR library_path LIKE ?)"
            params.extend([f"%{search_keyword}%", f"%{search_keyword}%"])
        else:  # 全部
            sql += " WHERE (name LIKE ? OR original_path LIKE ? OR library_path LIKE ?)"
            params.extend([f"%{search_keyword}%"] * 3)

    sql += " ORDER BY added_at DESC"

    # 添加分页
    if limit:
        sql += " LIMIT ? OFFSET ?"
        params.extend([limit, offset])

    cursor.execute(sql, params)
    return [dict(row) for row in cursor.fetchall()]

def get_search_count(search_keyword, search_column):
    """获取搜索结果总数"""
    cursor = self.db.conn.cursor()
    sql = "SELECT COUNT(*) FROM files"
    params = []

    # 添加搜索条件（与search_files保持一致）
    if search_keyword:
        if search_column == 0:
            sql += " WHERE name LIKE ?"
            params.append(f"%{search_keyword}%")
        elif search_column == 1:
            sql += " WHERE name LIKE ?"
            params.append(f"%.{search_keyword}%")
        elif search_column == 2:
            sql += " WHERE (original_path LIKE ? OR library_path LIKE ?)"
            params.extend([f"%{search_keyword}%", f"%{search_keyword}%"])
        else:
            sql += " WHERE (name LIKE ? OR original_path LIKE ? OR library_path LIKE ?)"
            params.extend([f"%{search_keyword}%"] * 3)

    cursor.execute(sql, params)
    return cursor.fetchone()[0]
```

### 5.4 标签管理流程（第二阶段新增）

标签管理是第二阶段新增的核心功能，支持三层标签架构和继承机制：

1. 用户创建根标签（设置颜色和权重）
2. 用户创建分支标签（可继承父标签属性）
3. 用户创建叶子标签（继承分支标签属性）
4. 系统自动处理标签继承关系
5. 用户为文件添加标签（支持多标签）
6. 系统更新标签统计和关联关系

**核心特性**：
- **三层继承**：根标签 → 分支标签 → 叶子标签
- **属性继承**：颜色和权重的智能继承
- **搜索继承**：搜索父标签包含所有子标签文件
- **批量操作**：支持批量文件标签管理

### 5.5 文件监控流程（第二阶段新增）

文件监控功能基于Qt的QFileSystemWatcher实现，提供实时文件变化监控：

1. 用户配置监控文件夹和规则
2. 系统启动QFileSystemWatcher监控
3. 检测到文件变化事件
4. 系统验证文件是否符合过滤规则
5. 执行智能重复文件检测
6. 根据配置自动入库或提示用户
7. 更新监控统计和状态

**核心特性**：
- **实时监控**：基于Qt原生组件的线程安全监控
- **智能过滤**：支持文件模式匹配和递归监控
- **重复检测**：基于CRC32的高性能文件指纹识别
- **批量处理**：支持批量文件处理和错误统计

## 6. 目录结构

SmartVault采用功能切片的目录结构，避免超长代码文件问题：

```
smartvault/
├── __init__.py
├── main.py                 # 应用入口点
├── config/                 # 配置文件
│   └── config.json.template
├── ui/                     # UI组件
│   ├── __init__.py
│   ├── main_window/        # 主窗口功能切片
│   │   ├── __init__.py     # 导出MainWindow类
│   │   ├── core.py         # 核心框架代码
│   │   ├── menu.py         # 菜单管理
│   │   ├── toolbar.py      # 工具栏管理
│   │   ├── file_ops.py     # 文件操作相关功能
│   │   └── search.py       # 搜索功能
│   ├── dialogs/            # 对话框
│   │   ├── __init__.py
│   │   ├── add_file_dialog.py
│   │   ├── advanced_search_dialog.py
│   │   ├── auto_tag_rule_dialog.py
│   │   ├── file_tag_dialog.py
│   │   ├── settings_dialog.py      # 设置对话框主框架 (159行)
│   │   ├── tag_management_dialog.py
│   │   └── settings/               # 设置对话框模块化 (B027重构)
│   │       ├── __init__.py         # 模块导出
│   │       ├── base/               # 基类定义
│   │       │   ├── __init__.py
│   │       │   └── base_page.py    # BaseSettingsPage基类 (64行)
│   │       ├── pages/              # 设置页面模块
│   │       │   ├── __init__.py     # 页面类导出
│   │       │   ├── library_page.py    # 文件库设置页面 (322行)
│   │       │   ├── monitor_page.py    # 文件监控设置页面 (259行)
│   │       │   ├── auto_tag_page.py   # 自动标签设置页面 (285行)
│   │       │   ├── ui_page.py         # 界面设置页面 (167行)
│   │       │   ├── search_page.py     # 搜索设置页面 (171行)
│   │       │   └── advanced_page.py  # 高级设置页面 (243行)
│   │       └── dialogs/            # 设置相关对话框 (预留)
│   │           └── __init__.py
│   ├── views/              # 视图组件
│   │   ├── __init__.py
│   │   ├── file_table_view.py
│   │   ├── file_grid_view.py
│   │   └── file_details_view.py
│   ├── models/             # 数据模型
│   │   ├── __init__.py
│   │   ├── file_table_model.py
│   │   └── file_grid_model.py
│   ├── widgets/            # 自定义控件
│   │   ├── __init__.py
│   │   ├── navigation_panel.py
│   │   ├── color_display_widget.py
│   │   └── progress_bar.py
│   ├── components/         # UI组件
│   │   ├── tag_navigation_panel.py
│   │   └── quick_tag_menu.py
│   ├── utils/              # UI工具
│   │   └── color_utils.py
│   ├── resources.py        # 资源管理
│   ├── themes.py           # 主题管理
│   └── localization.py     # 本地化
├── services/               # 业务服务
│   ├── __init__.py
│   ├── file/               # 文件服务功能切片
│   │   ├── __init__.py     # 导出FileService类
│   │   ├── core.py         # 核心服务功能
│   │   ├── import_ops.py   # 文件导入功能
│   │   └── operations.py   # 文件操作功能
│   ├── tag_service.py      # 标签服务
│   ├── auto_tag_service.py # 自动标签服务
│   ├── file_monitor_service.py # 文件监控服务
│   └── search_service.py   # 搜索服务
├── data/                   # 数据访问
│   ├── __init__.py
│   ├── database.py         # 数据库访问
│   └── file_system.py      # 文件系统操作
└── utils/                  # 工具类
    ├── __init__.py
    ├── config.py           # 配置管理
    └── logging.py          # 日志管理
```

### 6.1 功能切片文件组织

为避免超长代码文件问题，采用"功能切片"方式组织代码：

1. **按功能而非类型分割文件**：将大型类的不同功能分散到多个文件中
2. **使用组合模式**：通过组合而非继承扩展功能
3. **使用初始化文件整合功能**：在`__init__.py`中组合各功能模块

示例1 - 主窗口功能切片：

```python
# main_window/__init__.py
from .core import MainWindowCore
from .menu import MenuManager
from .toolbar import ToolbarManager
from .file_ops import FileOperationsMixin
from .search import SearchFeatureMixin
from .tags import TagManagementMixin

class MainWindow(FileOperationsMixin, SearchFeatureMixin,
                TagManagementMixin, MainWindowCore):
    """主窗口类，组合各功能模块"""
    def __init__(self):
        super().__init__()
        self.menu_manager = MenuManager(self)
        self.toolbar_manager = ToolbarManager(self)
```

示例2 - 设置对话框模块化重构（B027项目成果）：

```python
# settings_dialog.py (159行) - 主框架
class SettingsDialog(QDialog):
    """设置对话框 - 模块化版本"""
    library_changed = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = load_config()
        self.pages = {}
        self.init_ui()

    def create_pages(self):
        """创建所有设置页面"""
        self.pages['library'] = LibrarySettingsPage()
        self.pages['monitor'] = MonitorSettingsPage()
        self.pages['ui'] = UISettingsPage()
        self.pages['search'] = SearchSettingsPage()
        self.pages['auto_tag'] = AutoTagSettingsPage()
        self.pages['advanced'] = AdvancedSettingsPage()

# settings/base/base_page.py (64行) - 基类
class BaseSettingsPage(QWidget):
    """设置页面基类"""

    @abstractmethod
    def setup_ui(self): pass

    @abstractmethod
    def load_settings(self, config: dict): pass

    @abstractmethod
    def save_settings(self) -> dict: pass

# settings/pages/library_page.py (322行) - 页面实现
class LibrarySettingsPage(BaseSettingsPage):
    """文件库设置页面"""
    library_changed = Signal(str)

    def setup_ui(self):
        # 文件库管理UI实现
        pass

    def load_settings(self, config: dict):
        # 加载文件库配置
        pass

    def save_settings(self) -> dict:
        # 保存文件库配置
        return {"library_path": self.library_path}
```

**重构效果**：
- **代码减少90.5%**：主框架从1,672行减少到159行
- **模块化清晰**：8个独立文件，职责单一
- **易于扩展**：新增设置页面只需继承BaseSettingsPage
- **维护简化**：每个页面平均241行，便于理解和修改

## 7. 代码组织最佳实践

### 7.1 避免超长代码文件的策略

为避免出现超长代码文件（如超过2000行的`main_window.py`），本架构采用以下策略：

1. **功能切片文件组织**：
   - 按功能而非类型组织代码
   - 将大型类的不同功能分散到多个文件中
   - 使用`__init__.py`整合功能

2. **组合优于继承**：
   - 使用组合模式而非继承扩展功能
   - 创建专门的管理器类处理特定功能
   - 通过依赖注入提供服务

3. **UI与逻辑分离**：
   - 使用Qt Designer生成UI定义文件
   - 将UI定义与业务逻辑分离
   - 采用MVC/MVP模式组织代码

4. **动态加载机制**：
   - 实现按需加载功能模块
   - 使用插件架构扩展功能
   - 延迟初始化非核心功能

### 7.2 代码质量保证

为确保代码质量和可维护性，采用以下实践：

1. **代码规范**：
   - 遵循PEP 8编码规范
   - 使用类型注解提高代码可读性
   - 编写清晰的文档字符串

2. **测试策略**：
   - 为每个功能模块编写单元测试
   - 实现关键流程的集成测试
   - 使用测试驱动开发方法

3. **代码审查**：
   - 定期进行代码自审
   - 使用静态代码分析工具
   - 关注代码复杂度和文件大小

## 8. 测试策略

### 8.1 测试类型

SmartVault项目采用多层次测试策略，确保软件质量：

1. **单元测试**：
   - 测试单个组件的功能
   - 使用pytest框架
   - 重点测试业务逻辑层和数据访问层

2. **集成测试**：
   - 测试组件间的交互
   - 验证不同层次间的协作
   - 重点测试关键流程（如文件入库）

3. **UI测试**：
   - 测试用户界面功能
   - 使用pytest-qt框架
   - 验证用户交互流程

4. **性能测试**：
   - 测试系统在不同负载下的性能
   - 重点测试文件操作和搜索功能
   - 使用自定义性能测试脚本

### 8.2 测试优先级

测试资源按以下优先级分配：

1. **高优先级**：
   - 文件入库功能
   - 文件浏览功能
   - 基本搜索功能

2. **中优先级**：
   - 标签管理功能
   - 文件监控功能
   - 高级搜索功能

3. **低优先级**：
   - UI美化和优化
   - 辅助功能
   - 性能优化

### 8.3 测试自动化

为确保持续质量，实施以下自动化测试策略：

1. **自动化单元测试**：
   - 为每个核心组件编写自动化测试
   - 集成到开发工作流程中
   - 使用coverage.py监控测试覆盖率

2. **关键流程自动化测试**：
   - 自动化测试关键用户流程
   - 使用pytest-qt进行UI自动化测试
   - 定期执行回归测试

## 9. 架构验证与技术指导

### 9.1 架构符合性验证

本项目的实际实现**完全符合**架构设计规范：

1. **三层架构**：UI层、业务逻辑层、数据访问层清晰分离
2. **功能切片**：避免了超长代码文件问题，所有功能都采用模块化设计
3. **组合模式**：使用管理器类而非继承扩展功能
4. **技术选型**：严格遵循PyQt6 + SQLite + 简化ORM的设计
5. **代码质量**：新增功能都遵循了代码规范和架构约束

**新增功能均在原架构框架内实现**，没有破坏整体架构规范性：
- 标签服务遵循业务逻辑层设计原则
- 文件监控服务采用Qt原生组件，符合技术选型
- UI组件采用功能切片，避免了超长文件问题
- 数据模型扩展保持了数据访问层的一致性

**重构项目架构符合性验证**：
- **设置对话框重构（B027）**：完全符合功能切片原则
  - ✅ 主框架从1,672行减少到159行，符合文件大小控制标准
  - ✅ 采用基类统一接口设计，符合组合模式原则
  - ✅ 6个页面模块平均241行，符合模块化设计要求
  - ✅ 保持100%功能兼容性，符合向后兼容原则
  - ✅ 使用组合模式管理页面，符合架构设计指导

### 9.2 技术架构指导原则

为确保后续开发的架构一致性，应遵循以下技术指导原则：

1. **服务层扩展**：
   - 新增业务服务应继承或参考现有服务的设计模式
   - 保持服务间的松耦合，通过依赖注入提供协作
   - 使用统一的错误处理和日志记录机制

2. **UI组件开发**：
   - 继续使用功能切片方式组织代码
   - 新增对话框应参考现有对话框的设计模式
   - 保持UI与业务逻辑的清晰分离

3. **数据访问扩展**：
   - 数据库模式变更应通过迁移脚本管理
   - 新增数据访问方法应保持与现有接口的一致性
   - 继续使用SQLAlchemy Core的简化ORM方式

4. **性能优化指导**：
   - 基于已有的缓存机制进行扩展
   - 继续使用分页和按需加载策略
   - 保持CRC32等高性能算法的使用

5. **AI功能架构指导** (第六版新增)：
   - AI功能应采用延后启动机制
   - 保持AI功能的可选性和安全开关
   - 确保AI功能关闭时不影响现有功能
   - 使用模块化设计便于扩展和维护

### 9.3 AI功能性能优化指导 (第六版新增)

AI功能的性能优化是确保用户体验的关键，应遵循以下优化策略：

#### 9.3.1 延后启动优化

**策略**：AI功能采用延后启动机制，不影响程序启动速度

**实现指导**：
- 程序启动时不初始化AI功能
- 主界面显示后在后台初始化AI管理器
- 用户首次使用AI功能时确保已完成初始化

**性能指标**：
- 程序启动时间不受AI功能影响
- AI初始化时间 < 1秒
- 后台初始化不影响UI响应

#### 9.3.2 智能标签建议优化

**策略**：优化标签建议算法，确保实时响应

**实现指导**：
- 基于规则的快速匹配算法
- 预编译正则表达式模式
- 缓存常用文件类型映射
- 避免复杂的机器学习计算

**性能指标**：
- 标签建议响应时间 < 0.001秒
- 文件类型识别准确率 > 95%
- 内存占用 < 50MB

#### 9.3.3 配置和状态管理优化

**策略**：优化AI配置的加载和状态管理

**实现指导**：
- 配置文件缓存机制
- 状态变更的增量更新
- 配置验证的快速路径
- 避免频繁的磁盘I/O操作

**性能指标**：
- 配置加载时间 < 0.1秒
- 状态更新延迟 < 0.01秒
- 配置文件大小 < 10KB

#### 9.3.4 降级处理优化

**策略**：AI功能关闭时的零开销降级

**实现指导**：
- 快速的AI状态检查
- 空列表返回的零延迟
- 避免不必要的计算和内存分配
- 保持现有功能的完整性能

**性能指标**：
- AI关闭时的额外开销 = 0
- 降级检查时间 < 0.0001秒
- 现有功能性能不受影响

## 10. 模块目录结构

### 10.1 项目整体结构

```
SmartVault3/
├── smartvault/                    # 主应用包
│   ├── __init__.py
│   ├── config/                    # 配置管理
│   │   ├── __init__.py
│   │   ├── app_config.py         # 应用配置管理
│   │   └── library_config.py     # 文件库配置管理
│   ├── data/                      # 数据访问层
│   │   ├── __init__.py
│   │   └── database.py           # 数据库管理
│   ├── services/                  # 业务逻辑层
│   │   ├── __init__.py
│   │   ├── ai/                   # AI功能服务
│   │   │   ├── __init__.py
│   │   │   ├── ai_manager.py     # AI管理器
│   │   │   ├── auto_tag_service.py # 自动标签服务
│   │   │   └── smart_rule_engine.py # 智能规则引擎
│   │   └── file/                 # 文件服务
│   │       ├── __init__.py
│   │       ├── file_service.py   # 文件业务逻辑
│   │       ├── file_system.py    # 文件系统操作
│   │       ├── file_monitor.py   # 文件监控服务
│   │       └── tag_service.py    # 标签业务逻辑
│   ├── ui/                        # 用户界面层
│   │   ├── __init__.py
│   │   ├── components/           # UI组件
│   │   │   ├── __init__.py
│   │   │   ├── ai_status_widget.py # AI状态显示组件
│   │   │   ├── ai_tag_suggestion_panel.py # AI标签建议面板
│   │   │   ├── file_list_widget.py # 文件列表组件
│   │   │   ├── search_widget.py  # 搜索组件
│   │   │   └── tag_widget.py     # 标签组件
│   │   ├── dialogs/              # 对话框
│   │   │   ├── __init__.py
│   │   │   ├── file_dialog.py    # 文件对话框
│   │   │   ├── library_page.py   # 文件库页面
│   │   │   └── settings_dialog/  # 设置对话框(模块化)
│   │   │       ├── __init__.py
│   │   │       ├── base_page.py  # 基础页面类
│   │   │       ├── main_dialog.py # 主对话框
│   │   │       ├── ai_page.py    # AI设置页面
│   │   │       ├── backup_page.py # 备份设置页面
│   │   │       ├── general_page.py # 通用设置页面
│   │   │       ├── library_page.py # 文件库设置页面
│   │   │       ├── monitor_page.py # 监控设置页面
│   │   │       └── tag_page.py   # 标签设置页面
│   │   ├── main_window/          # 主窗口(模块化)
│   │   │   ├── __init__.py
│   │   │   ├── core.py          # 主窗口核心逻辑
│   │   │   ├── backup_manager.py # 备份管理模块
│   │   │   └── clipboard_handler.py # 剪贴板处理模块
│   │   ├── models/               # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── file_model.py    # 文件模型
│   │   │   └── tag_model.py     # 标签模型
│   │   ├── resources/            # 资源文件
│   │   │   ├── __init__.py
│   │   │   ├── icons/           # 图标资源
│   │   │   └── styles/          # 样式文件
│   │   ├── utils/                # UI工具类
│   │   │   ├── __init__.py
│   │   │   ├── clipboard_monitor.py # 剪贴板监控
│   │   │   ├── floating_window.py # 浮动窗口
│   │   │   └── ui_utils.py      # UI工具函数
│   │   └── views/                # 视图组件
│   │       ├── __init__.py
│   │       ├── file_view.py     # 文件视图
│   │       └── tag_view.py      # 标签视图
│   └── utils/                     # 通用工具类
│       ├── __init__.py
│       ├── backup_utils.py       # 备份工具
│       ├── file_utils.py         # 文件工具
│       ├── hash_utils.py         # 哈希工具
│       └── path_utils.py         # 路径工具
├── docs/                          # 项目文档
│   ├── 需求规格书.md
│   ├── 技术选型及架构设计.md
│   ├── 开发实施方案.md
│   └── AI接入方案.md
├── tests/                         # 测试文件
│   ├── __init__.py
│   ├── test_file_service.py
│   ├── test_tag_service.py
│   └── test_ai_features.py
├── tools/                         # 开发工具
│   ├── code_quality_monitor.py  # 代码质量监控
│   └── backup_tool.py           # 备份工具
├── requirements.txt               # 依赖包列表
├── run.py                        # 程序入口
└── README.md                     # 项目说明
```

### 10.2 核心模块说明

#### 10.2.1 配置管理 (config/)
- **app_config.py**: 应用全局配置管理，包括窗口状态、用户偏好等
- **library_config.py**: 文件库独立配置管理，支持多库配置

#### 10.2.2 数据访问层 (data/)
- **database.py**: SQLite数据库管理，包含9个核心表的创建和迁移

#### 10.2.3 业务逻辑层 (services/)
- **ai/**: AI功能服务模块，包含智能标签建议、规则引擎等
- **file/**: 文件相关服务，包含文件操作、监控、标签管理等

#### 10.2.4 用户界面层 (ui/)
- **components/**: 可复用的UI组件
- **dialogs/**: 各种对话框，采用模块化设计
- **main_window/**: 主窗口模块化组件
- **models/**: 数据模型类
- **utils/**: UI相关工具类
- **views/**: 视图组件

#### 10.2.5 工具类 (utils/)
- 通用工具函数，包含文件操作、哈希计算、路径处理等

### 10.3 模块化设计原则

1. **功能切片**: 避免超长文件，每个模块职责单一
2. **层次分离**: UI、业务逻辑、数据访问清晰分离
3. **组合模式**: 使用组合而非继承扩展功能
4. **依赖注入**: 通过构造函数注入依赖服务
5. **配置独立**: 每个模块的配置独立管理

## 11. 结论

本架构设计采用简化的三层架构，保持必要的架构规范性，同时确保设计的可实现性。通过开发实践验证了架构设计的有效性和可行性。

### 11.1 架构设计优势

本架构设计具有以下技术优势：

1. **三层架构的清晰性**：UI层、业务逻辑层、数据访问层的清晰分离，便于功能扩展和维护
2. **功能切片策略**：避免超长代码文件问题，保持良好的模块化
3. **技术选型合理性**：PyQt6 + SQLite + CRC32的组合在性能和稳定性方面表现良好
4. **组合模式应用**：通过管理器类和服务类的组合，实现功能的灵活扩展

### 11.2 架构扩展指导

后续功能扩展应遵循以下原则：

1. **保持架构一致性**：新增功能应在现有三层架构框架内实现
2. **继续功能切片**：避免创建超长代码文件，保持模块化设计
3. **遵循技术选型**：继续使用已选定的技术栈，确保技术一致性
4. **维护代码质量**：通过代码规范和架构约束，确保代码质量

### 11.3 技术债务管理

架构设计包含了技术债务管理机制：

- 文件长度监控和重构触发机制
- 代码复杂度控制策略
- 功能切片和组合模式的应用
- 持续的架构符合性验证

**重构成果记录**：

1. **设置对话框重构（B027项目）**：
   - **完成时间**：2025年1月27日
   - **重构前**：settings_dialog.py 1,672行超长文件
   - **重构后**：模块化为8个文件，主框架159行
   - **代码减少**：90.5%的代码量减少
   - **架构符合性**：完全符合功能切片和组合模式原则
   - **功能验证**：100%功能完整性保持，配置完全兼容
   - **技术债务状态**：✅ 已解决

2. **重构方法论验证**：
   - **测试驱动+渐进式实施**方法论验证有效
   - **基类统一接口**设计模式成功应用
   - **组合模式页面管理**架构清晰可扩展
   - **配置兼容性保持**策略成功实施

**本架构设计文档**为SmartVault项目提供了完整的技术指导，确保项目能够在保持架构规范性的前提下，灵活应对功能需求的变化和扩展。通过严格遵循本文档的技术指导，可以确保项目的长期技术健康和可维护性。

**重构经验总结**：设置对话框重构项目成功验证了架构设计的有效性，特别是功能切片和组合模式在解决超长代码文件问题方面的优势。这为后续类似重构提供了成功的实践模板。

### 10.4 代码质量控制指导

**文件大小控制标准**：
- 🟢 **1500行以下**：优秀状态，无需特殊关注
- 🟡 **1500-2000行**：良好状态，建议监控
- 🔴 **2000行以上**：需要关注，优先考虑模块化重构

**模块化重构原则**：
1. **功能切片**：按业务逻辑分离模块
2. **组合优于继承**：使用组合模式降低耦合
3. **接口统一**：提供清晰的扩展模式
4. **配置兼容**：确保重构不影响用户数据
5. **测试驱动**：重构过程保持功能完整性

**组合模式架构示例**：

```python
# 主窗口核心架构
class MainWindowCore(QMainWindow):
    def __init__(self):
        # 核心服务初始化
        self.file_service = FileService()
        self.backup_service = get_backup_service()

        # 功能管理器组合
        self.backup_manager = BackupManager(self)
        self.clipboard_handler = ClipboardHandler(self)

        # 信号连接和委托
        self.clipboard_service.duplicate_found.connect(
            self.clipboard_handler.on_clipboard_duplicate_found
        )
```

**开发指导原则**：
- **功能优先**：优先实现功能需求，适度优化架构
- **渐进式重构**：避免大规模重构，采用渐进式改进
- **模块独立**：新功能优先在独立模块实现
- **向后兼容**：保持API接口的向后兼容性
