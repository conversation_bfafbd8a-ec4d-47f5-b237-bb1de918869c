"""
文件网格视图
"""

from PySide6.QtWidgets import QListView, QMenu, QAbstractItemView, QStyledItemDelegate, QStyle
from PySide6.QtCore import Qt, Signal, QSize, QRect
from PySide6.QtGui import QPainter, QFont, QFontMetrics, QPen, QColor
from smartvault.ui.models.file_grid_model import FileGridModel


class FileGridItemDelegate(QStyledItemDelegate):
    """文件网格项委托"""

    def __init__(self, parent=None):
        """初始化委托

        Args:
            parent: 父对象
        """
        super().__init__(parent)
        self.item_size = QSize(120, 100)

    def sizeHint(self, option, index):
        """返回项目大小提示

        Args:
            option: 样式选项
            index: 模型索引

        Returns:
            QSize: 项目大小
        """
        return self.item_size

    def paint(self, painter, option, index):
        """绘制项目

        Args:
            painter: 绘制器
            option: 样式选项
            index: 模型索引
        """
        painter.save()

        # 获取绘制区域
        rect = option.rect

        # 设置背景 - 缩小选中区域高度，留出缝隙
        margin = 2  # 上下各留2像素缝隙
        adjusted_rect = rect.adjusted(0, margin, 0, -margin)

        if option.state & QStyle.State_Selected:
            painter.fillRect(adjusted_rect, QColor(209, 209, 209, 180))  # 选中状态背景 - 柔和灰色
        elif option.state & QStyle.State_MouseOver:
            painter.fillRect(adjusted_rect, QColor(232, 232, 232, 120))  # 悬停状态背景 - 更浅的灰色

        # 绘制图标
        icon = index.data(Qt.DecorationRole)
        if icon:
            icon_size = 48
            icon_rect = QRect(
                rect.x() + (rect.width() - icon_size) // 2,
                rect.y() + 10,
                icon_size,
                icon_size
            )
            icon.paint(painter, icon_rect)

        # 绘制文件名
        text = index.data(Qt.DisplayRole)
        if text:
            font = QFont()
            font.setPointSize(9)
            painter.setFont(font)

            # 计算文本区域
            text_rect = QRect(
                rect.x() + 5,
                rect.y() + 65,
                rect.width() - 10,
                rect.height() - 70
            )

            # 设置文本颜色
            if option.state & QStyle.State_Selected:
                painter.setPen(QColor(51, 51, 51))  # 深灰色文本，在浅灰背景上清晰可见
            else:
                painter.setPen(QColor(0, 0, 0))

            # 绘制文本（支持换行）
            fm = QFontMetrics(font)
            elided_text = fm.elidedText(text, Qt.ElideRight, text_rect.width())
            painter.drawText(text_rect, Qt.AlignCenter | Qt.TextWordWrap, elided_text)

        # 绘制边框
        if option.state & QStyle.State_Selected:
            painter.setPen(QPen(QColor(160, 160, 160), 2))  # 中等灰色边框
            painter.drawRect(adjusted_rect.adjusted(1, 1, -1, -1))

        painter.restore()


class FileGridView(QListView):
    """文件网格视图"""

    # 自定义信号
    file_activated = Signal(str)  # 文件激活信号
    file_selected = Signal(str)   # 文件选中信号

    def __init__(self, parent=None):
        """初始化视图

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 设置视图属性
        self.setViewMode(QListView.IconMode)
        self.setResizeMode(QListView.Adjust)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        self.setUniformItemSizes(True)
        self.setSpacing(10)

        # 禁止编辑
        self.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # 创建数据模型
        self.model = FileGridModel()
        self.setModel(self.model)

        # 设置委托
        self.delegate = FileGridItemDelegate()
        self.setItemDelegate(self.delegate)

        # 连接信号
        self.doubleClicked.connect(self.on_double_clicked)
        self.clicked.connect(self.on_clicked)

        # 拖拽相关属性
        self.drag_start_position = None
        
        # 性能优化：预创建右键菜单组件，避免每次创建导致的卡顿
        # 传入主窗口的服务实例，避免重复创建服务
        from smartvault.ui.components.note_menu import NoteMenu
        from smartvault.ui.components.quick_tag_menu import QuickTagMenu
        
        # 通过parent()方法向上查找主窗口
        main_window = self
        while main_window and not hasattr(main_window, 'file_service'):
            main_window = main_window.parent()
            
        if main_window and hasattr(main_window, 'file_service') and hasattr(main_window, 'tag_service'):
            self._note_menu = NoteMenu(self, main_window.file_service)
            self._quick_tag_menu = QuickTagMenu(self, main_window.tag_service)
        else:
            self._note_menu = NoteMenu(self)
            self._quick_tag_menu = QuickTagMenu(self)
        
        # 连接信号
        self._note_menu.notes_changed.connect(self._on_tags_changed)
        self._quick_tag_menu.tags_changed.connect(self._on_tags_changed)

    def on_double_clicked(self, index):
        """处理双击事件

        Args:
            index: 项目索引
        """
        # 获取文件ID
        file_id = self.model.data(index, FileGridModel.FileIdRole)
        if file_id:
            # 发送信号
            self.file_activated.emit(file_id)

            # 查找主窗口对象并调用打开文件方法
            main_window = self
            while main_window and not hasattr(main_window, 'on_open_file'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'on_open_file'):
                main_window.on_open_file(file_id)

    def on_clicked(self, index):
        """处理单击事件

        Args:
            index: 项目索引
        """
        # 获取文件ID
        file_id = self.model.data(index, FileGridModel.FileIdRole)
        if file_id:
            self.file_selected.emit(file_id)

    def show_context_menu(self, position):
        """显示上下文菜单

        Args:
            position: 菜单位置
        """
        # 获取选中的项目
        indexes = self.selectedIndexes()
        if not indexes:
            return

        # 创建上下文菜单
        menu = QMenu(self)

        # 添加菜单项
        open_action = menu.addAction("打开")
        show_in_explorer_action = menu.addAction("在系统资源管理器中显示")
        menu.addSeparator()

        # 编辑备注菜单项（置顶，使用频率最高）
        # 性能优化：使用预创建的组件，避免重复创建导致卡顿
        file_ids = [self.model.data(index, self.model.FileIdRole) for index in indexes]
        note_action = self._note_menu.create_action(menu, file_ids)
        menu.addAction(note_action)

        # 快速标签菜单
        # 性能优化：使用预创建的组件，避免重复创建导致卡顿
        tag_submenu = self._quick_tag_menu.create_menu(menu, file_ids)
        menu.addMenu(tag_submenu)

        menu.addSeparator()

        # 标签管理
        manage_tags_action = menu.addAction("添加标签")
        clear_tags_action = menu.addAction("清除标签")
        menu.addSeparator()

        # 中转文件夹操作
        staging_action = None
        if len(indexes) == 1:
            # 单个文件时，检查当前状态并显示相应操作
            file_id = self.model.data(indexes[0], self.model.FileIdRole)
            if file_id:
                # 获取文件信息以检查中转状态
                main_window = self
                while main_window and not hasattr(main_window, 'file_service'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'file_service'):
                    try:
                        file_info = main_window.file_service.get_file_by_id(file_id)
                        if file_info:
                            staging_status = file_info.get("staging_status", "normal")
                            if staging_status == "staging":
                                staging_action = menu.addAction("📤 移至智能文件库")
                            else:
                                staging_action = menu.addAction("📥 移入中转文件夹")
                    except Exception as e:
                        print(f"检查文件中转状态失败: {e}")
        else:
            # 多个文件时，显示批量操作
            staging_to_action = menu.addAction("📥 批量移入中转文件夹")
            staging_from_action = menu.addAction("📤 批量移至智能文件库")

        menu.addSeparator()

        # 添加"移动到文件夹"菜单
        move_to_folder_menu = self._create_move_to_folder_menu(menu, indexes)
        if move_to_folder_menu:
            menu.addMenu(move_to_folder_menu)

        # 添加"添加到文件夹"菜单
        add_to_folder_menu = self._create_add_to_folder_menu(menu, indexes)
        if add_to_folder_menu:
            menu.addMenu(add_to_folder_menu)

        rename_action = menu.addAction("重命名")
        move_action = menu.addAction("移动到...")
        export_action = menu.addAction("📤 导出到...")
        menu.addSeparator()
        delete_action = menu.addAction("删除文件")

        # 显示菜单并获取选择的操作
        action = menu.exec(self.mapToGlobal(position))

        # 处理选择的操作
        if action == open_action:
            # 只打开第一个选中的文件
            first_index = indexes[0]
            file_id = self.model.data(first_index, FileGridModel.FileIdRole)
            if file_id:
                self.file_activated.emit(file_id)

                # 查找主窗口对象并调用打开文件方法
                main_window = self
                while main_window and not hasattr(main_window, 'on_open_file'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'on_open_file'):
                    main_window.on_open_file(file_id)

        elif action == show_in_explorer_action:
            # 在系统资源管理器中显示文件
            first_index = indexes[0]
            file_id = self.model.data(first_index, FileGridModel.FileIdRole)
            if file_id:
                self._show_file_in_explorer(file_id)

        elif action == manage_tags_action:
            # 管理标签
            file_ids = []
            for index in indexes:
                file_id = self.model.data(index, FileGridModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if file_ids:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'on_manage_file_tags'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'on_manage_file_tags'):
                    main_window.on_manage_file_tags(file_ids)

        elif action == clear_tags_action:
            # 清除标签
            file_ids = []
            for index in indexes:
                file_id = self.model.data(index, FileGridModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if file_ids:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'on_clear_file_tags'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'on_clear_file_tags'):
                    main_window.on_clear_file_tags(file_ids)

        # 处理中转文件夹操作
        elif 'staging_action' in locals() and action == staging_action:
            # 单个文件的中转状态切换
            file_id = self.model.data(indexes[0], FileGridModel.FileIdRole)
            if file_id:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'file_service'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'file_service'):
                    try:
                        success = main_window.file_service.toggle_staging_status(file_id)
                        if success:
                            # 刷新视图
                            main_window._load_files_silently()
                            # 获取文件信息以显示状态
                            file_info = main_window.file_service.get_file_by_id(file_id)
                            if file_info:
                                staging_status = file_info.get("staging_status", "normal")
                                if staging_status == "staging":
                                    main_window.show_status_message("文件已移入中转文件夹", True)
                                else:
                                    main_window.show_status_message("文件已移至智能文件库，当前显示：全部文件", True)
                        else:
                            main_window.show_status_message("操作失败", False)
                    except Exception as e:
                        print(f"切换文件中转状态失败: {e}")
                        main_window.show_status_message(f"操作失败: {e}", False)

        elif 'staging_to_action' in locals() and action == staging_to_action:
            # 批量移入中转文件夹
            file_ids = []
            for index in indexes:
                file_id = self.model.data(index, FileGridModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if file_ids:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'file_service'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'file_service'):
                    try:
                        result = main_window.file_service.batch_move_to_staging(file_ids)
                        success_count = len(result['success'])
                        failed_count = len(result['failed'])

                        if success_count > 0:
                            main_window._load_files_silently()
                            main_window.show_status_message(f"成功移入 {success_count} 个文件到中转文件夹", True)

                        if failed_count > 0:
                            main_window.show_status_message(f"{failed_count} 个文件移入失败", False)
                    except Exception as e:
                        print(f"批量移入中转文件夹失败: {e}")
                        main_window.show_status_message(f"批量操作失败: {e}", False)

        elif 'staging_from_action' in locals() and action == staging_from_action:
            # 批量移出中转文件夹
            file_ids = []
            for index in indexes:
                file_id = self.model.data(index, FileGridModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if file_ids:
                # 查找主窗口对象
                main_window = self
                while main_window and not hasattr(main_window, 'file_service'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'file_service'):
                    try:
                        result = main_window.file_service.batch_move_from_staging(file_ids)
                        success_count = len(result['success'])
                        failed_count = len(result['failed'])

                        if success_count > 0:
                            main_window._load_files_silently()
                            main_window.show_status_message(f"成功移至智能文件库 {success_count} 个文件，当前显示：全部文件", True)

                        if failed_count > 0:
                            main_window.show_status_message(f"{failed_count} 个文件移出失败", False)
                    except Exception as e:
                        print(f"批量移出中转文件夹失败: {e}")
                        main_window.show_status_message(f"批量操作失败: {e}", False)

        elif action == export_action:
            # 导出文件
            file_ids = []
            for index in indexes:
                file_id = self.model.data(index, FileGridModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            # 查找主窗口对象
            main_window = self
            while main_window and not hasattr(main_window, 'on_export_files'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'on_export_files'):
                main_window.on_export_files(file_ids)

        elif action == delete_action:
            # 删除文件
            file_ids = []
            for index in indexes:
                file_id = self.model.data(index, FileGridModel.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            # 查找主窗口对象
            main_window = self
            while main_window and not hasattr(main_window, 'on_delete_file'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'on_delete_file'):
                main_window.on_delete_file(file_ids)

    def set_files(self, files):
        """设置文件列表

        Args:
            files: 文件信息字典列表
        """
        self.model.setFiles(files)

    def add_file(self, file):
        """添加文件

        Args:
            file: 文件信息字典
        """
        self.model.addFile(file)

    def append_files(self, files):
        """批量添加文件到现有列表

        Args:
            files: 文件列表
        """
        for file in files:
            self.model.addFile(file)

    def remove_file(self, file_id):
        """从视图中移除文件

        Args:
            file_id: 文件ID
        """
        self.model.removeFile(file_id)

    def get_selected_file_ids(self):
        """获取选中的文件ID列表

        Returns:
            list: 文件ID列表
        """
        file_ids = []
        for index in self.selectedIndexes():
            file_id = self.model.data(index, FileGridModel.FileIdRole)
            if file_id:
                file_ids.append(file_id)
        return file_ids

    def clear_selection(self):
        """清除选择"""
        self.clearSelection()

    def select_all(self):
        """选择全部"""
        self.selectAll()

    def get_file_count(self):
        """获取文件数量

        Returns:
            int: 文件数量
        """
        return self.model.rowCount()

    def _create_move_to_folder_menu(self, parent_menu, indexes):
        """创建"移动到文件夹"子菜单

        Args:
            parent_menu: 父菜单
            indexes: 选中的文件索引列表

        Returns:
            QMenu: 子菜单对象，如果没有可用文件夹则返回None
        """
        try:
            # 获取主窗口和标签服务
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window or not hasattr(main_window, 'tag_service'):
                return None

            # 获取自定义文件夹列表
            folder_tags = main_window.tag_service.get_folder_tags()

            if not folder_tags:
                return None

            # 创建子菜单
            from PySide6.QtWidgets import QMenu
            from PySide6.QtGui import QAction
            move_to_folder_menu = QMenu("移动到文件夹", parent_menu)

            # 获取选中的文件ID
            file_ids = []
            for index in indexes:
                file_id = self.model.data(index, self.model.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if not file_ids:
                return None

            # 为每个文件夹创建菜单项
            for folder in folder_tags:
                action = QAction(folder['name'], move_to_folder_menu)
                # 使用functools.partial避免lambda闭包问题
                from functools import partial
                action.triggered.connect(
                    partial(self._move_files_to_folder, file_ids, folder['id'])
                )
                move_to_folder_menu.addAction(action)

            # 添加分隔符和"新建文件夹"选项
            move_to_folder_menu.addSeparator()
            new_folder_action = QAction("+ 新建文件夹", move_to_folder_menu)
            new_folder_action.triggered.connect(
                partial(self._create_folder_and_move_files, file_ids)
            )
            move_to_folder_menu.addAction(new_folder_action)

            return move_to_folder_menu

        except Exception as e:
            print(f"创建移动到文件夹菜单失败: {e}")
            return None

    def _move_files_to_folder(self, file_ids, folder_tag_id):
        """将文件移动到指定文件夹

        Args:
            file_ids: 文件ID列表
            folder_tag_id: 文件夹标签ID
        """
        try:
            # 获取主窗口和服务
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window:
                return

            # 批量添加标签关联
            success_count = 0
            for file_id in file_ids:
                if main_window.tag_service.add_tag_to_file(file_id, folder_tag_id):
                    success_count += 1

                    # 如果文件在中转文件夹，移出中转状态
                    if hasattr(main_window, 'file_service'):
                        file_info = main_window.file_service.get_file_by_id(file_id)
                        if file_info and file_info.get("staging_status") == "staging":
                            main_window.file_service.move_from_staging(file_id)

            # 刷新视图
            if hasattr(main_window, '_load_files_silently'):
                main_window._load_files_silently()

            # 显示结果
            if success_count > 0:
                folder_tag = main_window.tag_service.get_tag_by_id(folder_tag_id)
                folder_name = folder_tag['name'] if folder_tag else '文件夹'
                main_window.show_status_message(
                    f"成功将 {success_count}/{len(file_ids)} 个文件移动到 {folder_name}",
                    True
                )
            else:
                main_window.show_status_message("移动文件失败", False)

        except Exception as e:
            print(f"移动文件到文件夹失败: {e}")
            # 获取主窗口显示错误
            main_window = self
            while main_window and not hasattr(main_window, 'show_status_message'):
                main_window = main_window.parent()
            if main_window:
                main_window.show_status_message(f"移动文件失败: {e}", False)

    def _create_folder_and_move_files(self, file_ids):
        """创建新文件夹并移动文件

        Args:
            file_ids: 文件ID列表
        """
        try:
            from PySide6.QtWidgets import QInputDialog, QMessageBox

            # 获取主窗口
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window:
                return

            # 输入文件夹名称
            folder_name, ok = QInputDialog.getText(
                self, "新建文件夹", "请输入文件夹名称:"
            )

            if not ok or not folder_name.strip():
                return

            folder_name = folder_name.strip()

            # 创建文件夹标签
            folder_tag_id = main_window.tag_service.create_folder_tag(folder_name)

            # 移动文件到新文件夹
            self._move_files_to_folder(file_ids, folder_tag_id)

            # 刷新导航面板
            if hasattr(main_window, 'navigation_panel'):
                main_window.navigation_panel.refresh_folder_tree()

        except Exception as e:
            print(f"创建文件夹并移动文件失败: {e}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"创建文件夹失败: {e}")

    def _on_tags_changed(self):
        """处理标签变化事件"""
        # 刷新文件网格中的标签和备注显示
        if hasattr(self.model, 'refresh_file_data'):
            self.model.refresh_file_data()
        elif hasattr(self.model, 'file_tags_cache'):
            self.model.file_tags_cache.clear()
            # 刷新网格视图
            self.model.layoutChanged.emit()

        # 查找主窗口并通知标签变化
        main_window = self
        while main_window and not hasattr(main_window, 'on_tags_changed'):
            main_window = main_window.parent()

        if main_window and hasattr(main_window, 'on_tags_changed'):
            main_window.on_tags_changed()

    def _show_file_in_explorer(self, file_id):
        """在系统资源管理器中显示文件

        Args:
            file_id: 文件ID
        """
        try:
            # 获取文件服务
            main_window = self
            while main_window and not hasattr(main_window, 'file_service'):
                main_window = main_window.parent()

            if not main_window or not hasattr(main_window, 'file_service'):
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "无法获取文件服务")
                return

            # 获取文件信息
            file_info = main_window.file_service.get_file_by_id(file_id)
            if not file_info:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "文件信息不存在")
                return

            # 确定文件路径
            if file_info["entry_type"] == "link":
                file_path = file_info["original_path"]
            else:
                file_path = file_info["library_path"]

            # 检查文件是否存在
            import os
            if not os.path.exists(file_path):
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "文件不存在", f"文件物理路径不存在：\n{file_path}")
                return

            # 在系统资源管理器中显示文件
            import subprocess
            import platform

            system = platform.system()
            if system == "Windows":
                # Windows系统使用explorer命令，使用原始字符串避免转义问题
                normalized_path = os.path.normpath(file_path)
                # 使用列表形式传递参数，避免shell解析问题
                subprocess.run(['explorer', '/select,', normalized_path], check=False)
            elif system == "Darwin":  # macOS
                # macOS系统使用open命令
                subprocess.run(['open', '-R', file_path], check=True)
            elif system == "Linux":
                # Linux系统使用xdg-open命令
                subprocess.run(['xdg-open', os.path.dirname(file_path)], check=True)
            else:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "不支持的系统", f"当前系统 {system} 不支持此功能")

        except subprocess.CalledProcessError as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"打开资源管理器失败：{e}")
        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"显示文件失败：{e}")

    def _create_add_to_folder_menu(self, parent_menu, indexes):
        """创建"添加到文件夹"子菜单（不移出中转状态）

        Args:
            parent_menu: 父菜单
            indexes: 选中的文件索引列表

        Returns:
            QMenu: 子菜单对象，如果没有可用文件夹则返回None
        """
        try:
            # 获取主窗口和标签服务
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window or not hasattr(main_window, 'tag_service'):
                return None

            # 获取自定义文件夹列表
            folder_tags = main_window.tag_service.get_folder_tags()

            if not folder_tags:
                return None

            # 创建子菜单
            from PySide6.QtWidgets import QMenu
            from PySide6.QtGui import QAction
            add_to_folder_menu = QMenu("添加到文件夹", parent_menu)

            # 获取选中的文件ID
            file_ids = []
            for index in indexes:
                file_id = self.model.data(index, self.model.FileIdRole)
                if file_id:
                    file_ids.append(file_id)

            if not file_ids:
                return None

            # 为每个文件夹创建菜单项
            for folder in folder_tags:
                action = QAction(folder['name'], add_to_folder_menu)
                # 使用functools.partial避免lambda闭包问题
                from functools import partial
                action.triggered.connect(
                    partial(self._add_files_to_folder, file_ids, folder['id'])
                )
                add_to_folder_menu.addAction(action)

            # 添加分隔符和"新建文件夹"选项
            add_to_folder_menu.addSeparator()
            new_folder_action = QAction("+ 新建文件夹", add_to_folder_menu)
            new_folder_action.triggered.connect(
                partial(self._create_folder_and_add_files, file_ids)
            )
            add_to_folder_menu.addAction(new_folder_action)

            return add_to_folder_menu

        except Exception as e:
            print(f"创建添加到文件夹菜单失败: {e}")
            return None

    def _add_files_to_folder(self, file_ids, folder_tag_id):
        """将文件添加到指定文件夹（不移出中转状态）

        Args:
            file_ids: 文件ID列表
            folder_tag_id: 文件夹标签ID
        """
        try:
            # 获取主窗口和服务
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window:
                return

            # 批量添加标签关联（不移出中转状态）
            success_count = 0
            for file_id in file_ids:
                if main_window.tag_service.add_tag_to_file(file_id, folder_tag_id):
                    success_count += 1

            # 刷新视图
            if hasattr(main_window, '_load_files_silently'):
                main_window._load_files_silently()

            # 显示结果
            if success_count > 0:
                folder_tag = main_window.tag_service.get_tag_by_id(folder_tag_id)
                folder_name = folder_tag['name'] if folder_tag else '文件夹'
                main_window.show_status_message(
                    f"成功将 {success_count}/{len(file_ids)} 个文件添加到 {folder_name}",
                    True
                )
            else:
                main_window.show_status_message("添加文件失败", False)

        except Exception as e:
            print(f"添加文件到文件夹失败: {e}")
            # 获取主窗口显示错误
            main_window = self
            while main_window and not hasattr(main_window, 'show_status_message'):
                main_window = main_window.parent()
            if main_window:
                main_window.show_status_message(f"添加文件失败: {e}", False)

    def _create_folder_and_add_files(self, file_ids):
        """创建新文件夹并添加文件（不移出中转状态）

        Args:
            file_ids: 文件ID列表
        """
        try:
            from PySide6.QtWidgets import QInputDialog, QMessageBox

            # 获取主窗口
            main_window = self
            while main_window and not hasattr(main_window, 'tag_service'):
                main_window = main_window.parent()

            if not main_window:
                return

            # 输入文件夹名称
            folder_name, ok = QInputDialog.getText(
                self, "新建文件夹", "请输入文件夹名称:"
            )

            if not ok or not folder_name.strip():
                return

            folder_name = folder_name.strip()

            # 创建文件夹标签
            folder_tag_id = main_window.tag_service.create_folder_tag(folder_name)

            # 添加文件到新文件夹
            self._add_files_to_folder(file_ids, folder_tag_id)

            # 刷新导航面板
            if hasattr(main_window, 'navigation_panel'):
                main_window.navigation_panel.refresh_folder_tree()

        except Exception as e:
            print(f"创建文件夹并添加文件失败: {e}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"创建文件夹失败: {e}")

    def mousePressEvent(self, event):
        """鼠标按下事件 - 支持拖拽和多选"""
        if event.button() == Qt.LeftButton:
            index = self.indexAt(event.pos())
            if index.isValid():
                print(f"🖱️ 网格鼠标按下: 行{index.row()}")

                # 检查当前文件是否已经被选中
                current_selected = self.get_selected_file_ids()
                current_file_id = self.model.data(index, FileGridModel.FileIdRole)
                is_already_selected = current_file_id in current_selected

                print(f"📋 当前选中文件数: {len(current_selected)}, 点击文件已选中: {is_already_selected}")

                # 检查是否真正点击在文件名文字上
                is_on_filename_text = self._is_click_on_filename_text(index, event.pos())

                # 拖拽逻辑：
                # 1. 点击在文件名文字上：允许文件拖拽
                # 2. 点击在非文件名文字区域但已选中文件：允许文件拖拽
                # 3. 点击在非文件名文字区域且未选中：允许框选
                if is_on_filename_text:
                    # 点击在文件名文字上，允许文件拖拽
                    self.drag_start_position = event.pos()
                    print(f"📁 文件名文字点击，允许文件拖拽")
                elif is_already_selected:
                    # 非文件名文字区域但是已选中文件，允许文件拖拽
                    self.drag_start_position = event.pos()
                    print(f"🎯 已选中文件的非文字区域，允许文件拖拽 ({len(current_selected)}个文件)")
                else:
                    # 非文件名文字区域且未选中，允许框选
                    self.drag_start_position = None
                    print(f"🔄 非文件名文字区域未选中文件，允许框选")
            else:
                # 空白区域，允许框选
                self.drag_start_position = None
                print(f"🔄 空白区域点击，允许框选")

        super().mousePressEvent(event)

        # 输出当前选中的项目数量
        selected_count = len(self.get_selected_file_ids())
        print(f"📊 当前选中文件数量: {selected_count}")

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 触发拖拽或框选"""
        if not (event.buttons() & Qt.LeftButton):
            return

        # 如果没有设置拖拽起始位置，让父类处理（框选）
        if not self.drag_start_position:
            super().mouseMoveEvent(event)
            return

        # 检查是否移动了足够的距离来开始拖拽
        from PySide6.QtWidgets import QApplication
        if ((event.pos() - self.drag_start_position).manhattanLength() <
            QApplication.startDragDistance()):
            return

        # 获取拖拽起始位置的项目
        index = self.indexAt(self.drag_start_position)
        if not index.isValid():
            # 如果起始位置无效，让父类处理
            super().mouseMoveEvent(event)
            return

        print(f"🎯 网格触发文件拖拽: 行{index.row()}")
        self.startDrag(index)

    def _is_click_on_filename_text(self, index, click_pos):
        """检查点击位置是否在文件名文字上

        Args:
            index: 网格项索引
            click_pos: 点击位置

        Returns:
            bool: 是否点击在文件名文字上
        """
        try:
            # 获取项目矩形区域
            item_rect = self.visualRect(index)

            # 获取文件名文本
            filename = self.model.data(index, Qt.DisplayRole)
            if not filename:
                return False

            # 计算相对于项目的点击位置
            relative_pos = click_pos - item_rect.topLeft()

            # 根据FileGridItemDelegate的绘制逻辑：
            # - 图标区域：y < 65
            # - 文件名区域：y >= 65
            if relative_pos.y() < 65:
                # 点击在图标区域，不是文件名文字
                print(f"🔍 网格文字检测: 点击在图标区域 (y={relative_pos.y()})")
                return False

            # 在文件名区域，进一步检查是否在文字上
            # 计算文本宽度
            font_metrics = QFontMetrics(self.font())
            text_width = font_metrics.horizontalAdvance(filename)

            # 文件名区域的中心对齐，计算文字的实际位置
            item_width = item_rect.width()
            text_left = (item_width - text_width) // 2
            text_right = text_left + text_width

            # 检查是否在文字的水平范围内
            is_on_text = text_left <= relative_pos.x() <= text_right

            print(f"🔍 网格文字检测: 文件名='{filename}', 文本宽度={text_width}, 点击位置=({relative_pos.x()}, {relative_pos.y()}), 在文字上={is_on_text}")

            return is_on_text

        except Exception as e:
            print(f"❌ 网格文件名文字检测失败: {e}")
            # 出错时默认返回True，保持原有行为
            return True

    def startDrag(self, index):
        """开始拖拽操作"""
        try:
            # 获取选中的文件ID
            file_ids = self.get_selected_file_ids()
            if not file_ids:
                return

            print(f"📁 网格拖拽有效项目: 行{index.row()}")

            # 创建拖拽数据
            from PySide6.QtCore import QMimeData
            from PySide6.QtGui import QDrag

            mime_data = QMimeData()

            # 设置文件ID数据（用于内部拖拽）
            mime_data.setText(",".join(file_ids))

            # 创建拖拽对象
            drag = QDrag(self)
            drag.setMimeData(mime_data)

            # 执行拖拽
            drop_action = drag.exec(Qt.MoveAction | Qt.CopyAction)

            print(f"🎯 网格拖拽完成: {drop_action}")

        except Exception as e:
            print(f"❌ 网格拖拽失败: {e}")
            import traceback
            traceback.print_exc()
