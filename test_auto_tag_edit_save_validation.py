#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动标签规则编辑保存修复验证测试

验证修复后的自动标签编辑保存功能是否正常工作
"""

import sys
import os
import tempfile
import json
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from smartvault.services.auto_tag_service import AutoTagService, AutoTagRule, ConditionType, ConditionGroup, Condition, LogicOperator


def test_fixed_auto_tag_edit_save():
    """测试修复后的自动标签编辑保存功能"""
    print("\n=== 测试修复后的自动标签编辑保存功能 ===")
    sys.stdout.flush()
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_config_path = f.name
        initial_config = {
            "auto_tags": {
                "enabled": True,
                "enable_ai": False,
                "rules": []
            }
        }
        json.dump(initial_config, f, ensure_ascii=False, indent=4)
    
    try:
        # 备份原配置路径
        from smartvault.utils import config as config_module
        original_get_config_path = config_module.get_config_path
        config_module.get_config_path = lambda: temp_config_path
        
        # 创建自动标签服务
        service = AutoTagService()
        service.load_rules_from_config(initial_config)
        
        # 1. 添加一个多条件规则
        print("\n1. 添加多条件规则...")
        condition1 = Condition(type=ConditionType.FILE_EXTENSION, value="pdf")
        condition2 = Condition(type=ConditionType.FILE_SIZE, value=">1048576")  # 大于1MB
        condition_group = ConditionGroup(
            operator=LogicOperator.AND,
            conditions=[condition1, condition2]
        )
        
        original_rule = AutoTagRule(
            id=str(uuid.uuid4()),
            name="大PDF文档",
            tag_names=["文档", "PDF", "大文件"],
            enabled=True,
            priority=10,
            condition_group=condition_group
        )
        
        service.add_rule(original_rule)
        print(f"添加规则: {original_rule.name}")
        sys.stdout.flush()
        
        # 2. 编辑规则
        print("\n2. 编辑规则...")
        edited_rule = AutoTagRule(
            id=original_rule.id,
            name="编辑后的大PDF文档",
            tag_names=["文档", "PDF", "大文件", "重要"],
            enabled=True,
            priority=15,
            condition_group=condition_group
        )
        
        service.update_rule(original_rule.id, edited_rule)
        print(f"编辑规则: {edited_rule.name}")
        sys.stdout.flush()
        
        # 3. 模拟修复后的设置页面保存行为
        print("\n3. 模拟修复后的设置页面保存...")
        
        # 创建一个模拟的设置页面
        class MockAutoTagSettingsPage:
            def __init__(self):
                self.enable_auto_tags_check_checked = True
                self.enable_ai_auto_tags_check_checked = False
            
            def get_auto_tag_service(self):
                return service
            
            def save_settings(self):
                """使用修复后的保存逻辑"""
                # 获取自动标签服务实例
                auto_tag_service = self.get_auto_tag_service()
                
                if auto_tag_service:
                    try:
                        # 调用服务的内部方法来获取规则的配置格式数据
                        from smartvault.utils.config import load_config
                        current_config = load_config()
                        auto_tags_config = current_config.get("auto_tags", {})
                        
                        # 只更新启用状态，保持现有规则不变
                        return {
                            "enabled": self.enable_auto_tags_check_checked,
                            "enable_ai": self.enable_ai_auto_tags_check_checked,
                            "rules": auto_tags_config.get("rules", [])
                        }
                    except Exception as e:
                        print(f"获取自动标签规则配置失败: {e}")
                        return {"enabled": True, "enable_ai": False, "rules": []}
                
                return {"enabled": True, "enable_ai": False, "rules": []}
        
        # 使用修复后的保存逻辑
        mock_page = MockAutoTagSettingsPage()
        saved_settings = mock_page.save_settings()
        
        print(f"保存的设置: {json.dumps(saved_settings, ensure_ascii=False, indent=2)}")
        sys.stdout.flush()
        
        # 4. 验证修复效果
        print("\n4. 验证修复效果...")
        saved_rules = saved_settings.get("rules", [])
        
        if saved_rules:
            saved_rule = saved_rules[0]
            has_condition_group = "condition_group" in saved_rule
            rule_name = saved_rule.get("name")
            rule_tags = saved_rule.get("tag_names", [])
            
            print(f"保存的规则名称: {rule_name}")
            print(f"保存的规则标签: {rule_tags}")
            print(f"是否包含condition_group: {has_condition_group}")
            sys.stdout.flush()
            
            # 检查编辑的内容是否保存
            name_correct = rule_name == "编辑后的大PDF文档"
            tags_correct = "重要" in rule_tags
            
            if has_condition_group and name_correct and tags_correct:
                print("[OK] 修复成功：多条件信息和编辑内容都正确保存！")
                sys.stdout.flush()
                return True
            elif has_condition_group:
                print("[PARTIAL] 多条件信息保存正确，但编辑内容可能有问题")
                sys.stdout.flush()
                return False
            else:
                print("[ERROR] 修复失败：多条件信息仍然丢失")
                sys.stdout.flush()
                return False
        else:
            print("[ERROR] 没有保存任何规则")
            sys.stdout.flush()
            return False
        
    finally:
        # 恢复原配置路径
        config_module.get_config_path = original_get_config_path
        # 清理临时文件
        try:
            os.unlink(temp_config_path)
        except:
            pass


def test_integration_with_settings_dialog():
    """测试与设置对话框的集成"""
    print("\n=== 测试与设置对话框的集成 ===")
    
    print("集成测试要点：")
    print("1. 编辑规则后，规则应该立即保存到配置文件")
    print("2. 设置对话框的save_settings不应该覆盖已编辑的规则")
    print("3. 多条件规则的完整性应该得到保持")
    print("4. 简单规则和复杂规则都应该正确处理")
    
    print("\n修复要点：")
    print("1. AutoTagSettingsPage.save_settings() 现在使用配置文件中的规则数据")
    print("2. 不再从表格重新构建规则，避免信息丢失")
    print("3. 只更新启用状态，保持规则完整性")
    print("4. 提供回退机制以确保兼容性")


if __name__ == "__main__":
    # 运行修复验证测试
    success = test_fixed_auto_tag_edit_save()
    
    # 集成测试说明
    test_integration_with_settings_dialog()
    
    if success:
        print("\n[SUCCESS] 自动标签编辑保存问题已修复！")
    else:
        print("\n[FAILED] 修复可能不完整，需要进一步调试")