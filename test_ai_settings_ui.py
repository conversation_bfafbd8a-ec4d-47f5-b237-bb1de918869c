#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI设置页面UI组件集成测试

测试AI设置页面各UI组件的创建、信号连接和数据流
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

def test_ai_settings_page_creation():
    """测试AI设置页面创建"""
    print("\n=== 测试AI设置页面创建 ===")
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        from smartvault.services.ai.ai_manager import AIManager
        
        # 创建AI设置页面
        print("1. 创建AI设置页面...")
        ai_page = AISettingsPage()
        print("   ✅ AI设置页面创建成功")
        
        # 检查组件是否正确初始化
        print("\n2. 检查组件初始化...")
        components = [
            ('status_widget', '状态显示组件'),
            ('features_widget', '功能设置组件'),
            ('learning_widget', '行为学习组件'),
            ('rules_widget', '自适应规则组件'),
            ('stats_widget', '统计显示组件')
        ]
        
        for attr_name, display_name in components:
            if hasattr(ai_page, attr_name) and getattr(ai_page, attr_name) is not None:
                print(f"   ✅ {display_name}初始化成功")
            else:
                print(f"   ❌ {display_name}初始化失败")
                return False
        
        # 测试AI管理器设置
        print("\n3. 测试AI管理器设置...")
        ai_manager = AIManager()
        ai_page.set_ai_manager(ai_manager)
        print("   ✅ AI管理器设置成功")
        
        # 测试配置加载
        print("\n4. 测试配置加载...")
        test_config = {
            'ai': {
                'enabled': True,
                'stage': 'rule_based',
                'features': {
                    'project_detection': {'enabled': True},
                    'series_detection': {'enabled': True},
                    'behavior_learning': {'enabled': True},
                    'smart_suggestions': {'enabled': True}
                }
            }
        }
        
        ai_page.load_settings(test_config)
        print("   ✅ 配置加载成功")
        
        # 测试配置保存
        print("\n5. 测试配置保存...")
        saved_config = ai_page.save_settings()
        if 'ai' in saved_config and saved_config['ai']:
            print("   ✅ 配置保存成功")
            print(f"   保存的配置: enabled={saved_config['ai'].get('enabled')}")
        else:
            print("   ❌ 配置保存失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ AI设置页面创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_signals():
    """测试组件信号连接"""
    print("\n=== 测试组件信号连接 ===")
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        from smartvault.services.ai.ai_manager import AIManager
        
        # 创建页面和管理器
        ai_page = AISettingsPage()
        ai_manager = AIManager()
        ai_page.set_ai_manager(ai_manager)
        
        # 测试信号连接
        print("1. 检查信号连接...")
        
        # 检查各组件的信号是否正确连接
        signal_tests = []
        
        # 功能组件配置变化信号
        if hasattr(ai_page.features_widget, 'config_changed'):
            print("   ✅ 功能组件配置变化信号存在")
            signal_tests.append(True)
        else:
            print("   ❌ 功能组件配置变化信号缺失")
            signal_tests.append(False)
        
        # 学习组件配置变化信号
        if hasattr(ai_page.learning_widget, 'config_changed'):
            print("   ✅ 学习组件配置变化信号存在")
            signal_tests.append(True)
        else:
            print("   ❌ 学习组件配置变化信号缺失")
            signal_tests.append(False)
        
        # 状态更新请求信号
        if hasattr(ai_page.status_widget, 'status_update_requested'):
            print("   ✅ 状态更新请求信号存在")
            signal_tests.append(True)
        else:
            print("   ❌ 状态更新请求信号缺失")
            signal_tests.append(False)
        
        return all(signal_tests)
        
    except Exception as e:
        print(f"   ❌ 组件信号连接测试失败: {e}")
        return False

def test_component_data_flow():
    """测试组件数据流"""
    print("\n=== 测试组件数据流 ===")
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.ai_main_page import AISettingsPage
        from smartvault.services.ai.ai_manager import AIManager
        
        # 创建页面和管理器
        ai_page = AISettingsPage()
        ai_manager = AIManager()
        ai_page.set_ai_manager(ai_manager)
        
        # 测试配置管理器设置
        print("1. 测试配置管理器设置...")
        if ai_page.ai_config_manager and ai_page.ai_config_manager._ai_manager:
            print("   ✅ 配置管理器AI管理器引用设置成功")
        else:
            print("   ❌ 配置管理器AI管理器引用设置失败")
            return False
        
        # 测试各组件的配置管理器设置
        print("\n2. 测试各组件配置管理器设置...")
        components = [
            (ai_page.status_widget, '状态组件'),
            (ai_page.features_widget, '功能组件'),
            (ai_page.learning_widget, '学习组件'),
            (ai_page.rules_widget, '规则组件'),
            (ai_page.stats_widget, '统计组件')
        ]
        
        for component, name in components:
            if hasattr(component, 'config_manager') and component.config_manager:
                print(f"   ✅ {name}配置管理器设置成功")
            else:
                print(f"   ❌ {name}配置管理器设置失败")
                return False
        
        # 测试状态刷新
        print("\n3. 测试状态刷新...")
        try:
            ai_page._refresh_all_status()
            print("   ✅ 状态刷新执行成功")
        except Exception as e:
            print(f"   ⚠️ 状态刷新执行异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 组件数据流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """测试各个组件的独立功能"""
    print("\n=== 测试各个组件独立功能 ===")
    
    try:
        from smartvault.ui.dialogs.settings.pages.ai.components.ai_status_widget import AIStatusWidget
        from smartvault.ui.dialogs.settings.pages.ai.components.ai_features_widget import AIFeaturesWidget
        from smartvault.ui.dialogs.settings.pages.ai.utils.ai_config_helper import AIConfigManager
        from smartvault.services.ai.ai_manager import AIManager
        
        # 创建配置管理器和AI管理器
        config_manager = AIConfigManager()
        ai_manager = AIManager()
        config_manager.set_ai_manager(ai_manager)
        
        # 测试状态组件
        print("1. 测试状态组件...")
        status_widget = AIStatusWidget()
        status_widget.set_config_manager(config_manager)
        
        test_config = {'enabled': True, 'stage': 'rule_based'}
        status_widget.load_config(test_config)
        print("   ✅ 状态组件加载配置成功")
        
        # 测试功能组件
        print("\n2. 测试功能组件...")
        features_widget = AIFeaturesWidget()
        features_widget.set_config_manager(config_manager)
        
        test_config = {
            'enabled': True,
            'features': {
                'project_detection': {'enabled': True, 'min_files': 3},
                'series_detection': {'enabled': True, 'min_files': 2}
            }
        }
        
        features_widget.load_config(test_config)
        print("   ✅ 功能组件加载配置成功")
        
        saved_config = features_widget.save_config()
        if saved_config and 'enabled' in saved_config:
            print("   ✅ 功能组件保存配置成功")
        else:
            print("   ❌ 功能组件保存配置失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 各个组件独立功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("AI设置页面UI组件集成测试")
    print("=" * 50)
    
    # 创建QApplication（UI测试需要）
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_ai_settings_page_creation())
    test_results.append(test_component_signals())
    test_results.append(test_component_data_flow())
    test_results.append(test_individual_components())
    
    # 汇总测试结果
    print("\n" + "=" * 50)
    print("UI测试结果汇总:")
    
    test_names = [
        "AI设置页面创建",
        "组件信号连接",
        "组件数据流",
        "各个组件独立功能"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(test_results)} 项UI测试通过")
    
    if passed == len(test_results):
        print("\n🎉 所有UI测试通过！AI设置页面UI组件功能正常。")
    else:
        print(f"\n⚠️ 有 {len(test_results) - passed} 项UI测试失败，需要修复。")
    
    # 退出应用
    QTimer.singleShot(100, app.quit)
    
    return passed == len(test_results)

if __name__ == "__main__":
    main()
