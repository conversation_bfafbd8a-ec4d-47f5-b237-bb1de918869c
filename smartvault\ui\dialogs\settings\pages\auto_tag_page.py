"""
自动标签设置页面
从原 settings_dialog.py 中的 create_auto_tag_tab 方法迁移而来
"""

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QCheckBox,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QDialog
)
from typing import Tuple
from ..base.base_page import BaseSettingsPage


class AutoTagSettingsPage(BaseSettingsPage):
    """自动标签设置页面"""

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)

        # 启用自动标签组
        enable_group = QGroupBox("自动标签功能")
        enable_layout = QVBoxLayout(enable_group)

        # 启用自动标签
        self.enable_auto_tags_check = QCheckBox("启用自动标签功能")
        self.enable_auto_tags_check.setToolTip("启用后，系统将根据设定的规则自动为文件添加标签")
        enable_layout.addWidget(self.enable_auto_tags_check)

        # 说明文字
        info_label = QLabel("自动标签功能可以根据文件类型、路径模式、大小等条件自动为文件添加标签，减少手动操作。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666666; font-size: 12px;")
        enable_layout.addWidget(info_label)

        layout.addWidget(enable_group)

        # 自动标签规则组
        rules_group = QGroupBox("自动标签规则")
        rules_layout = QVBoxLayout(rules_group)

        # 规则表格
        self.auto_tag_rules_table = QTableWidget()
        self.auto_tag_rules_table.setColumnCount(4)
        self.auto_tag_rules_table.setHorizontalHeaderLabels([
            "规则名称", "条件类型", "条件值", "标签"
        ])

        # 设置表格属性
        header = self.auto_tag_rules_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 150)  # 规则名称列宽
        header.resizeSection(1, 100)  # 条件类型列宽
        header.resizeSection(2, 200)  # 条件值列宽

        self.auto_tag_rules_table.setSelectionBehavior(QTableWidget.SelectRows)
        rules_layout.addWidget(self.auto_tag_rules_table)

        # 规则管理按钮
        rules_buttons_layout = QHBoxLayout()

        self.add_auto_tag_rule_button = QPushButton("添加规则")
        self.add_auto_tag_rule_button.clicked.connect(self.on_add_auto_tag_rule)
        rules_buttons_layout.addWidget(self.add_auto_tag_rule_button)

        self.add_advanced_auto_tag_rule_button = QPushButton("添加高级规则")
        self.add_advanced_auto_tag_rule_button.clicked.connect(self.on_add_advanced_auto_tag_rule)
        rules_buttons_layout.addWidget(self.add_advanced_auto_tag_rule_button)

        self.edit_auto_tag_rule_button = QPushButton("编辑规则")
        self.edit_auto_tag_rule_button.clicked.connect(self.on_edit_auto_tag_rule)
        rules_buttons_layout.addWidget(self.edit_auto_tag_rule_button)

        self.delete_auto_tag_rule_button = QPushButton("删除规则")
        self.delete_auto_tag_rule_button.clicked.connect(self.on_delete_auto_tag_rule)
        rules_buttons_layout.addWidget(self.delete_auto_tag_rule_button)

        rules_buttons_layout.addStretch()
        rules_layout.addLayout(rules_buttons_layout)

        layout.addWidget(rules_group)

        # AI辅助标签组
        ai_group = QGroupBox("AI辅助标签")
        ai_layout = QVBoxLayout(ai_group)

        self.enable_ai_auto_tags_check = QCheckBox("启用AI自动标签建议")
        self.enable_ai_auto_tags_check.setEnabled(True)  # 启用AI功能
        self.enable_ai_auto_tags_check.setToolTip("AI将分析文件内容并建议合适的标签，包括项目标签、系列标签等智能识别")
        ai_layout.addWidget(self.enable_ai_auto_tags_check)

        # AI功能按钮
        ai_buttons_layout = QHBoxLayout()

        self.test_ai_button = QPushButton("测试AI功能")
        self.test_ai_button.setToolTip("测试AI标签建议功能")
        self.test_ai_button.clicked.connect(self.on_test_ai_function)
        ai_buttons_layout.addWidget(self.test_ai_button)

        self.ai_settings_button = QPushButton("AI设置")
        self.ai_settings_button.setToolTip("配置AI功能详细设置")
        self.ai_settings_button.clicked.connect(self.on_ai_settings)
        ai_buttons_layout.addWidget(self.ai_settings_button)

        ai_buttons_layout.addStretch()
        ai_layout.addLayout(ai_buttons_layout)

        ai_info_label = QLabel("AI功能可以智能分析文件内容并建议合适的标签，包括项目文件识别、系列文件检测、行为模式学习等。")
        ai_info_label.setWordWrap(True)
        ai_info_label.setStyleSheet("color: #666666; font-size: 12px;")
        ai_layout.addWidget(ai_info_label)

        layout.addWidget(ai_group)

        layout.addStretch()

    def load_settings(self, config: dict):
        """从配置加载设置到UI控件

        Args:
            config: 配置字典
        """
        self.config = config
        auto_tag_config = config.get("auto_tags", {})

        # 启用自动标签
        self.enable_auto_tags_check.setChecked(auto_tag_config.get("enabled", True))

        # AI辅助标签
        self.enable_ai_auto_tags_check.setChecked(auto_tag_config.get("enable_ai", False))

        # 加载现有规则
        self.load_auto_tag_rules()

    def save_settings(self) -> dict:
        """从UI控件保存设置到配置

        Returns:
            dict: 自动标签设置字典
        """
        # 获取自动标签服务实例
        auto_tag_service = self.get_auto_tag_service()
        
        if auto_tag_service:
            # 使用服务中的完整规则数据，而不是从表格重新构建
            # 这样可以保持规则的完整性，包括 condition_group 等复杂结构
            try:
                # 调用服务的内部方法来获取规则的配置格式数据
                # 这确保了所有规则信息（包括多条件）都被正确保存
                from smartvault.utils.config import load_config
                current_config = load_config()
                auto_tags_config = current_config.get("auto_tags", {})
                
                # 只更新启用状态，保持现有规则不变
                return {
                    "enabled": self.enable_auto_tags_check.isChecked(),
                    "enable_ai": self.enable_ai_auto_tags_check.isChecked(),
                    "rules": auto_tags_config.get("rules", [])
                }
            except Exception as e:
                print(f"获取自动标签规则配置失败: {e}")
                # 如果获取失败，回退到原来的方法
                pass
        
        # 回退方案：从表格中读取（可能丢失复杂规则信息）
        rules = []
        for row in range(self.auto_tag_rules_table.rowCount()):
            name_item = self.auto_tag_rules_table.item(row, 0)
            type_item = self.auto_tag_rules_table.item(row, 1)
            value_item = self.auto_tag_rules_table.item(row, 2)
            tags_item = self.auto_tag_rules_table.item(row, 3)

            if name_item and type_item and value_item and tags_item:
                rule = {
                    "name": name_item.text(),
                    "condition_type": type_item.text(),
                    "condition_value": value_item.text(),
                    "tag_names": [tag.strip() for tag in tags_item.text().split(",") if tag.strip()]
                }
                rules.append(rule)

        return {
            "enabled": self.enable_auto_tags_check.isChecked(),
            "enable_ai": self.enable_ai_auto_tags_check.isChecked(),
            "rules": rules
        }

    def validate_settings(self) -> Tuple[bool, str]:
        """验证设置有效性

        Returns:
            tuple: (是否有效, 错误信息)
        """
        # 验证规则的有效性
        for row in range(self.auto_tag_rules_table.rowCount()):
            name_item = self.auto_tag_rules_table.item(row, 0)
            if not name_item or not name_item.text().strip():
                return False, f"第{row+1}行规则名称不能为空"

        return True, ""

    def reset_to_defaults(self):
        """重置为默认设置"""
        self.enable_auto_tags_check.setChecked(True)
        self.enable_ai_auto_tags_check.setChecked(False)
        self.auto_tag_rules_table.setRowCount(0)

    def get_page_title(self) -> str:
        """获取页面标题

        Returns:
            str: 页面标题
        """
        return "自动标签"

    def load_auto_tag_rules(self):
        """加载自动标签规则"""
        try:
            # 优先从自动标签服务获取规则
            auto_tag_service = self.get_auto_tag_service()
            if auto_tag_service:
                rules = auto_tag_service.get_all_rules()
                self.auto_tag_rules_table.setRowCount(len(rules))
                
                for row, rule in enumerate(rules):
                    # 规则名称
                    self.auto_tag_rules_table.setItem(row, 0, QTableWidgetItem(rule.name))
                    
                    # 条件类型和值（区分简单规则和复杂规则）
                    if hasattr(rule, 'condition_group') and rule.condition_group:
                        # 多条件规则
                        self.auto_tag_rules_table.setItem(row, 1, QTableWidgetItem("多条件"))
                        self.auto_tag_rules_table.setItem(row, 2, QTableWidgetItem(rule.get_description()))
                    else:
                        # 单条件规则
                        condition_type = rule.condition_type.value if rule.condition_type else ""
                        self.auto_tag_rules_table.setItem(row, 1, QTableWidgetItem(condition_type))
                        self.auto_tag_rules_table.setItem(row, 2, QTableWidgetItem(rule.condition_value or ""))
                    
                    # 标签
                    tags_text = ", ".join(rule.tag_names) if rule.tag_names else ""
                    self.auto_tag_rules_table.setItem(row, 3, QTableWidgetItem(tags_text))
            else:
                # 回退到从配置文件读取
                rules = self.config.get("auto_tags", {}).get("rules", [])
                self.auto_tag_rules_table.setRowCount(len(rules))

                for row, rule in enumerate(rules):
                    # 规则名称
                    self.auto_tag_rules_table.setItem(row, 0, QTableWidgetItem(rule.get("name", "")))

                    # 条件类型
                    self.auto_tag_rules_table.setItem(row, 1, QTableWidgetItem(rule.get("condition_type", "")))

                    # 条件值
                    self.auto_tag_rules_table.setItem(row, 2, QTableWidgetItem(rule.get("condition_value", "")))

                    # 标签
                    tag_names = rule.get("tag_names", [])
                    tags_text = ", ".join(tag_names) if tag_names else ""
                    self.auto_tag_rules_table.setItem(row, 3, QTableWidgetItem(tags_text))

        except Exception as e:
            print(f"加载自动标签规则失败: {e}")

    def on_add_auto_tag_rule(self):
        """添加自动标签规则"""
        from PySide6.QtWidgets import QMessageBox

        try:
            from smartvault.ui.dialogs.auto_tag_rule_dialog import AutoTagRuleDialog

            dialog = AutoTagRuleDialog(self)
            if dialog.exec() == QDialog.Accepted:
                rule = dialog.get_rule()
                if rule:
                    # 添加到自动标签服务
                    auto_tag_service = self.get_auto_tag_service()
                    if auto_tag_service:
                        auto_tag_service.add_rule(rule)
                        # 刷新表格显示
                        self.load_auto_tag_rules()
                    else:
                        QMessageBox.warning(self, "错误", "无法获取自动标签服务")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加自动标签规则失败: {e}")

    def on_add_advanced_auto_tag_rule(self):
        """添加高级自动标签规则（多条件）"""
        from PySide6.QtWidgets import QMessageBox

        try:
            from smartvault.ui.dialogs.multi_condition_auto_tag_dialog import MultiConditionAutoTagDialog

            dialog = MultiConditionAutoTagDialog(self)
            if dialog.exec() == QDialog.Accepted:
                rule = dialog.get_rule()
                if rule:
                    # 添加到自动标签服务
                    auto_tag_service = self.get_auto_tag_service()
                    if auto_tag_service:
                        auto_tag_service.add_rule(rule)
                        # 刷新表格显示
                        self.load_auto_tag_rules()
                    else:
                        QMessageBox.warning(self, "错误", "无法获取自动标签服务")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加高级自动标签规则失败: {e}")

    def on_edit_auto_tag_rule(self):
        """编辑自动标签规则"""
        from PySide6.QtWidgets import QMessageBox

        current_row = self.auto_tag_rules_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请先选择要编辑的规则")
            return

        try:
            # 获取选中规则的信息
            name_item = self.auto_tag_rules_table.item(current_row, 0)
            if not name_item:
                QMessageBox.warning(self, "错误", "无法获取规则信息")
                return
            
            rule_name = name_item.text()
            
            # 获取auto_tag_service实例
            auto_tag_service = self.get_auto_tag_service()
            if not auto_tag_service:
                QMessageBox.warning(self, "错误", "无法获取自动标签服务")
                return
            
            # 从服务中获取完整的规则对象
            rules = auto_tag_service.get_all_rules()
            target_rule = None
            for rule in rules:
                if rule.name == rule_name:
                    target_rule = rule
                    break
            
            if not target_rule:
                QMessageBox.warning(self, "错误", "找不到对应的规则")
                return
            
            # 判断规则类型并打开相应的编辑对话框
            if hasattr(target_rule, 'condition_group') and target_rule.condition_group:
                # 高级规则（多条件）
                from smartvault.ui.dialogs.multi_condition_auto_tag_dialog import MultiConditionAutoTagDialog
                dialog = MultiConditionAutoTagDialog(self, target_rule)
            else:
                # 简单规则（单条件）
                from smartvault.ui.dialogs.auto_tag_rule_dialog import AutoTagRuleDialog
                dialog = AutoTagRuleDialog(self, target_rule)
            
            if dialog.exec() == QDialog.Accepted:
                edited_rule = dialog.get_rule()
                if edited_rule:
                    # 更新规则
                    auto_tag_service.update_rule(edited_rule.id, edited_rule)
                    # 刷新表格显示
                    self.load_auto_tag_rules()
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑规则失败: {e}")

    def on_delete_auto_tag_rule(self):
        """删除自动标签规则"""
        from PySide6.QtWidgets import QMessageBox

        current_row = self.auto_tag_rules_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请先选择要删除的规则")
            return

        name_item = self.auto_tag_rules_table.item(current_row, 0)
        rule_name = name_item.text() if name_item else "未知规则"

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除自动标签规则「{rule_name}」吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 从自动标签服务中删除规则
            auto_tag_service = self.get_auto_tag_service()
            if auto_tag_service:
                rules = auto_tag_service.get_all_rules()
                for rule in rules:
                    if rule.name == rule_name:
                        auto_tag_service.remove_rule(rule.id)
                        break
                # 刷新表格显示
                self.load_auto_tag_rules()
            else:
                # 回退到直接删除表格行
                self.auto_tag_rules_table.removeRow(current_row)

    def on_test_ai_function(self):
        """测试AI功能"""
        from PySide6.QtWidgets import QMessageBox, QInputDialog

        try:
            # 获取AI管理器实例
            ai_manager = self.get_ai_manager()
            if not ai_manager:
                QMessageBox.warning(self, "警告", "AI管理器未初始化，请重启程序后再试")
                return

            # 获取AI状态
            status = ai_manager.get_status()
            if not status['enabled']:
                QMessageBox.information(self, "提示", "AI功能未启用，请先启用AI功能")
                return

            # 让用户输入测试文件名
            filename, ok = QInputDialog.getText(
                self, "测试AI功能",
                "请输入要测试的文件名（包含扩展名）：",
                text="test_project_v1.py"
            )

            if ok and filename:
                # 构造测试文件信息
                import os
                name, ext = os.path.splitext(filename)
                test_file_info = {
                    'name': name,
                    'extension': ext,
                    'full_name': filename,
                    'path': f"/test/path/{filename}",
                    'folder_path': "/test/path"
                }

                # 获取AI建议
                suggestions = ai_manager.suggest_tags(test_file_info)

                # 显示结果
                if suggestions:
                    result_text = f"文件「{filename}」的AI标签建议：\n\n"
                    for i, tag in enumerate(suggestions, 1):
                        result_text += f"{i}. {tag}\n"

                    # 获取项目检测结果
                    project_info = ai_manager.detect_project_files("/test/path")
                    if project_info:
                        result_text += f"\n项目检测结果：\n"
                        result_text += f"项目名称：{project_info.get('project_name', '未知')}\n"
                        result_text += f"置信度：{project_info.get('confidence', 0):.2f}\n"

                    QMessageBox.information(self, "AI测试结果", result_text)
                else:
                    QMessageBox.information(self, "AI测试结果", f"文件「{filename}」暂无AI标签建议")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"AI功能测试失败：{e}")

    def on_ai_settings(self):
        """打开AI设置页面"""
        try:
            # 获取设置对话框实例
            settings_dialog = self.get_settings_dialog()
            if settings_dialog:
                # 切换到AI设置页面
                settings_dialog.switch_to_page('ai')
            else:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "无法访问AI设置页面，请通过主菜单的设置选项打开")

        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"打开AI设置失败：{e}")

    def get_ai_manager(self):
        """获取AI管理器实例"""
        try:
            # 通过父窗口获取主窗口，再获取AI管理器
            main_window = self.get_main_window()
            if hasattr(main_window, 'ai_manager'):
                return main_window.ai_manager
            return None
        except:
            return None

    def get_settings_dialog(self):
        """获取设置对话框实例"""
        widget = self
        while widget.parent():
            widget = widget.parent()
            # 检查是否是设置对话框
            if hasattr(widget, 'switch_to_page') and hasattr(widget, 'tab_widget'):
                return widget
        return None

    def get_main_window(self):
        """获取主窗口实例"""
        widget = self
        while widget.parent():
            widget = widget.parent()
            if hasattr(widget, 'ai_manager'):
                return widget
        return None
    
    def get_auto_tag_service(self):
        """获取自动标签服务实例"""
        try:
            # 通过父窗口获取主窗口，再获取自动标签服务
            main_window = self.get_main_window()
            if hasattr(main_window, 'auto_tag_service'):
                return main_window.auto_tag_service
            return None
        except:
            return None