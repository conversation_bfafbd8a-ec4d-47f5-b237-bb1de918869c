#!/usr/bin/env python3
"""
自动标签服务模块
"""

import os
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from enum import Enum


class ConditionType(Enum):
    """条件类型枚举"""
    FILE_EXTENSION = "file_extension"  # 文件扩展名
    FILE_NAME_PATTERN = "file_name_pattern"  # 文件名模式
    FILE_PATH_PATTERN = "file_path_pattern"  # 文件路径模式
    FILE_SIZE_RANGE = "file_size_range"  # 文件大小范围
    FILE_TYPE = "file_type"  # 文件类型（图片、文档、视频等）
    FILE_SIZE = "file_size"  # 文件大小（单个值或比较）
    FILE_NAME_REGEX = "file_name_regex"  # 文件名正则表达式（严格模式）


class LogicOperator(Enum):
    """逻辑操作符枚举"""
    AND = "and"  # 与
    OR = "or"   # 或


@dataclass
class Condition:
    """单个条件"""
    type: ConditionType
    value: str

    def matches(self, file_info: Dict[str, Any]) -> bool:
        """检查文件是否匹配此条件"""
        try:
            if self.type == ConditionType.FILE_EXTENSION:
                return self._match_file_extension(file_info)
            elif self.type == ConditionType.FILE_NAME_PATTERN:
                return self._match_file_name_pattern(file_info)
            elif self.type == ConditionType.FILE_PATH_PATTERN:
                return self._match_file_path_pattern(file_info)
            elif self.type == ConditionType.FILE_SIZE_RANGE:
                return self._match_file_size_range(file_info)
            elif self.type == ConditionType.FILE_TYPE:
                return self._match_file_type(file_info)
            elif self.type == ConditionType.FILE_SIZE:
                return self._match_file_size(file_info)
            elif self.type == ConditionType.FILE_NAME_REGEX:
                return self._match_file_name_regex(file_info)
            else:
                return False
        except Exception as e:
            print(f"条件匹配失败: {e}")
            return False

    def _match_file_extension(self, file_info: Dict[str, Any]) -> bool:
        """匹配文件扩展名"""
        file_name = file_info.get("name", "")
        if not file_name:
            return False

        # 获取文件扩展名
        _, ext = os.path.splitext(file_name)
        ext = ext.lower().lstrip('.')

        # 条件值可以是多个扩展名，用逗号分隔
        target_extensions = [e.strip().lower().lstrip('.') for e in self.value.split(',')]
        return ext in target_extensions

    def _match_file_name_pattern(self, file_info: Dict[str, Any]) -> bool:
        """匹配文件名模式"""
        file_name = file_info.get("name", "")
        if not file_name:
            return False

        try:
            # 使用正则表达式匹配
            pattern = re.compile(self.value, re.IGNORECASE)
            return bool(pattern.search(file_name))
        except re.error:
            # 如果不是有效的正则表达式，使用简单的字符串包含匹配
            return self.value.lower() in file_name.lower()

    def _match_file_path_pattern(self, file_info: Dict[str, Any]) -> bool:
        """匹配文件路径模式"""
        file_path = file_info.get("original_path", "") or file_info.get("library_path", "")
        if not file_path:
            return False

        try:
            # 使用正则表达式匹配
            pattern = re.compile(self.value, re.IGNORECASE)
            return bool(pattern.search(file_path))
        except re.error:
            # 如果不是有效的正则表达式，使用简单的字符串包含匹配
            return self.value.lower() in file_path.lower()

    def _match_file_size_range(self, file_info: Dict[str, Any]) -> bool:
        """匹配文件大小范围"""
        file_size = file_info.get("size", 0)
        if file_size <= 0:
            return False

        try:
            # 条件值格式：min-max（单位：字节）或 min-max MB/KB/GB
            condition = self.value.strip()

            # 解析大小范围
            if '-' in condition:
                parts = condition.split('-', 1)
                min_size_str = parts[0].strip()
                max_size_str = parts[1].strip()

                min_size = self._parse_size(min_size_str)
                max_size = self._parse_size(max_size_str)

                return min_size <= file_size <= max_size
            else:
                # 单个值，表示最小大小
                min_size = self._parse_size(condition)
                return file_size >= min_size

        except Exception:
            return False

    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串为字节数"""
        size_str = size_str.strip().upper()

        # 提取数字和单位
        import re
        match = re.match(r'(\d+(?:\.\d+)?)\s*([KMGT]?B?)', size_str)
        if not match:
            return int(size_str)  # 假设是字节数

        number = float(match.group(1))
        unit = match.group(2)

        # 转换为字节
        multipliers = {
            '': 1, 'B': 1,
            'K': 1024, 'KB': 1024,
            'M': 1024**2, 'MB': 1024**2,
            'G': 1024**3, 'GB': 1024**3,
            'T': 1024**4, 'TB': 1024**4
        }

        return int(number * multipliers.get(unit, 1))

    def _match_file_type(self, file_info: Dict[str, Any]) -> bool:
        """匹配文件类型"""
        file_name = file_info.get("name", "")
        if not file_name:
            return False

        _, ext = os.path.splitext(file_name)
        ext = ext.lower().lstrip('.')

        # 预定义的文件类型映射
        file_type_mappings = {
            "图片": ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp", "svg"],
            "文档": ["doc", "docx", "pdf", "txt", "rtf", "odt", "pages"],
            "表格": ["xls", "xlsx", "csv", "ods", "numbers"],
            "演示": ["ppt", "pptx", "odp", "key"],
            "视频": ["mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v"],
            "音频": ["mp3", "wav", "flac", "aac", "ogg", "wma", "m4a"],
            "压缩": ["zip", "rar", "7z", "tar", "gz", "bz2", "xz"],
            "代码": ["py", "js", "html", "css", "java", "cpp", "c", "h", "php", "rb", "go", "rs"],
            "可执行": ["exe", "msi", "dmg", "pkg", "deb", "rpm", "app"]
        }

        target_type = self.value.strip()
        extensions = file_type_mappings.get(target_type, [])
        return ext in extensions

    def _match_file_size(self, file_info: Dict[str, Any]) -> bool:
        """匹配文件大小（支持比较操作符）"""
        file_size = file_info.get("size", 0)
        if file_size <= 0:
            return False

        try:
            condition = self.value.strip()

            # 支持比较操作符：>, <, >=, <=, =
            import re
            match = re.match(r'([><=]+)\s*(.+)', condition)
            if match:
                operator = match.group(1)
                size_str = match.group(2)
                target_size = self._parse_size(size_str)

                if operator == '>':
                    return file_size > target_size
                elif operator == '<':
                    return file_size < target_size
                elif operator == '>=':
                    return file_size >= target_size
                elif operator == '<=':
                    return file_size <= target_size
                elif operator == '=' or operator == '==':
                    # 允许一定的误差范围（±1KB）
                    return abs(file_size - target_size) <= 1024
            else:
                # 没有操作符，默认为等于
                target_size = self._parse_size(condition)
                return abs(file_size - target_size) <= 1024

        except Exception:
            return False

    def _match_file_name_regex(self, file_info: Dict[str, Any]) -> bool:
        """匹配文件名正则表达式（严格模式）"""
        file_name = file_info.get("name", "")
        if not file_name:
            return False

        try:
            # 严格的正则表达式匹配，不做容错处理
            pattern = re.compile(self.value)
            return bool(pattern.search(file_name))
        except re.error as e:
            print(f"正则表达式错误: {e}")
            return False


@dataclass
class ConditionGroup:
    """条件组（支持嵌套）"""
    operator: LogicOperator = LogicOperator.AND
    conditions: List[Any] = field(default_factory=list)  # 使用Any避免循环引用

    def matches(self, file_info: Dict[str, Any]) -> bool:
        """检查文件是否匹配此条件组"""
        if not self.conditions:
            return True

        if self.operator == LogicOperator.AND:
            return all(condition.matches(file_info) for condition in self.conditions)
        elif self.operator == LogicOperator.OR:
            return any(condition.matches(file_info) for condition in self.conditions)
        else:
            return False


@dataclass
class AutoTagRule:
    """自动标签规则（支持多条件组合）"""

    id: str
    name: str
    tag_names: List[str]
    enabled: bool = True
    priority: int = 0  # 优先级，数字越大优先级越高

    # 新的条件组合结构
    condition_group: Optional[ConditionGroup] = None

    # 兼容旧版本的单条件结构
    condition_type: Optional[ConditionType] = None
    condition_value: Optional[str] = None

    def matches(self, file_info: Dict[str, Any]) -> bool:
        """检查文件是否匹配此规则

        Args:
            file_info: 文件信息字典，包含name, path, size等

        Returns:
            bool: 是否匹配
        """
        if not self.enabled:
            return False

        try:
            # 优先使用新的条件组合结构
            if self.condition_group:
                return self.condition_group.matches(file_info)

            # 兼容旧版本的单条件结构
            elif self.condition_type and self.condition_value is not None:
                single_condition = Condition(type=self.condition_type, value=self.condition_value)
                return single_condition.matches(file_info)

            else:
                return False

        except Exception as e:
            print(f"规则匹配失败: {e}")
            return False

    def get_description(self) -> str:
        """获取规则的文字描述"""
        if self.condition_group:
            return self._describe_condition_group(self.condition_group)
        elif self.condition_type and self.condition_value is not None:
            return self._describe_single_condition(self.condition_type, self.condition_value)
        else:
            return "无条件"

    def _describe_condition_group(self, group: ConditionGroup, level: int = 0) -> str:
        """描述条件组"""
        # 防止无限递归
        if level > 10:
            return "条件过于复杂"

        if not group.conditions:
            return "无条件"

        operator_text = "且" if group.operator == LogicOperator.AND else "或"

        descriptions = []
        for condition in group.conditions:
            if isinstance(condition, ConditionGroup):
                desc = self._describe_condition_group(condition, level + 1)
                if level > 0:
                    descriptions.append(f"({desc})")
                else:
                    descriptions.append(desc)
            else:
                desc = self._describe_single_condition(condition.type, condition.value)
                descriptions.append(desc)

        return f" {operator_text} ".join(descriptions)

    def _describe_single_condition(self, condition_type: ConditionType, condition_value: str) -> str:
        """描述单个条件"""
        from smartvault.utils.condition_utils import describe_single_condition
        return describe_single_condition(condition_type, condition_value)


class AutoTagService:
    """自动标签服务"""

    def __init__(self):
        """初始化服务"""
        self.rules: List[AutoTagRule] = []
        self.enabled = True
        self.ai_enabled = False  # AI功能启用状态
        self.ai_manager = None  # AI管理器引用

    def load_rules_from_config(self, config: Dict[str, Any]) -> None:
        """从配置加载规则

        Args:
            config: 配置字典
        """
        auto_tag_config = config.get("auto_tags", {})
        self.enabled = auto_tag_config.get("enabled", True)
        self.ai_enabled = auto_tag_config.get("enable_ai", False)

        rules_data = auto_tag_config.get("rules", [])
        self.rules = []

        for i, rule_data in enumerate(rules_data):
            try:
                # 处理条件组结构
                condition_group = None
                if "condition_group" in rule_data:
                    condition_group = self._dict_to_condition_group(rule_data["condition_group"])
                
                rule = AutoTagRule(
                    id=rule_data.get("id", f"rule_{i}"),
                    name=rule_data.get("name", f"规则{i+1}"),
                    condition_type=ConditionType(rule_data.get("condition_type", "file_extension")) if "condition_type" in rule_data else None,
                    condition_value=rule_data.get("condition_value", "") if "condition_value" in rule_data else None,
                    tag_names=rule_data.get("tag_names", []),
                    enabled=rule_data.get("enabled", True),
                    priority=rule_data.get("priority", 0),
                    condition_group=condition_group
                )
                self.rules.append(rule)
            except Exception as e:
                print(f"加载自动标签规则失败: {e}")

        # 按优先级排序
        self.rules.sort(key=lambda r: r.priority, reverse=True)

    def set_ai_manager(self, ai_manager):
        """设置AI管理器

        Args:
            ai_manager: AI管理器实例
        """
        self.ai_manager = ai_manager

    def get_auto_tags_for_file(self, file_info: Dict[str, Any]) -> List[str]:
        """获取文件的自动标签（集成AI建议）

        Args:
            file_info: 文件信息字典

        Returns:
            List[str]: 标签名称列表
        """
        if not self.enabled:
            return []

        auto_tags = []

        # 1. 获取基于规则的标签（现有逻辑）
        for rule in self.rules:
            if rule.matches(file_info):
                auto_tags.extend(rule.tag_names)

        # 2. 获取AI建议标签（新增）
        if self.ai_enabled and self.ai_manager and self.ai_manager.is_available():
            try:
                ai_tags = self.ai_manager.suggest_tags(file_info)
                auto_tags.extend(ai_tags)
            except Exception as e:
                print(f"AI标签建议失败: {e}")

        # 3. 去重并保持顺序（规则标签优先级更高）
        seen = set()
        unique_tags = []
        for tag in auto_tags:
            if tag not in seen:
                seen.add(tag)
                unique_tags.append(tag)

        return unique_tags

    def get_rule_based_tags(self, file_info: Dict[str, Any]) -> List[str]:
        """仅获取基于规则的标签（不包含AI建议）

        Args:
            file_info: 文件信息字典

        Returns:
            List[str]: 基于规则的标签列表
        """
        if not self.enabled:
            return []

        auto_tags = []

        for rule in self.rules:
            if rule.matches(file_info):
                auto_tags.extend(rule.tag_names)

        # 去重并保持顺序
        seen = set()
        unique_tags = []
        for tag in auto_tags:
            if tag not in seen:
                seen.add(tag)
                unique_tags.append(tag)

        return unique_tags

    def get_ai_suggested_tags(self, file_info: Dict[str, Any]) -> List[str]:
        """仅获取AI建议的标签

        Args:
            file_info: 文件信息字典

        Returns:
            List[str]: AI建议的标签列表
        """
        if not self.ai_enabled or not self.ai_manager or not self.ai_manager.is_available():
            return []

        try:
            return self.ai_manager.suggest_tags(file_info)
        except Exception as e:
            print(f"AI标签建议失败: {e}")
            return []

    def add_rule(self, rule: AutoTagRule) -> None:
        """添加规则

        Args:
            rule: 规则对象
        """
        self.rules.append(rule)
        # 重新排序
        self.rules.sort(key=lambda r: r.priority, reverse=True)
        # 保存配置到文件
        self._save_rules_to_config()

    def remove_rule(self, rule_id: str) -> bool:
        """移除规则

        Args:
            rule_id: 规则ID

        Returns:
            bool: 是否成功移除
        """
        for i, rule in enumerate(self.rules):
            if rule.id == rule_id:
                del self.rules[i]
                # 保存配置到文件
                self._save_rules_to_config()
                return True
        return False

    def get_rule(self, rule_id: str) -> Optional[AutoTagRule]:
        """获取规则

        Args:
            rule_id: 规则ID

        Returns:
            Optional[AutoTagRule]: 规则对象，如果不存在则返回None
        """
        for rule in self.rules:
            if rule.id == rule_id:
                return rule
        return None

    def update_rule(self, rule_id: str, updated_rule: AutoTagRule) -> bool:
        """更新规则

        Args:
            rule_id: 规则ID
            updated_rule: 更新后的规则

        Returns:
            bool: 是否成功更新
        """
        for i, rule in enumerate(self.rules):
            if rule.id == rule_id:
                self.rules[i] = updated_rule
                # 重新排序
                self.rules.sort(key=lambda r: r.priority, reverse=True)
                # 保存配置到文件
                self._save_rules_to_config()
                return True
        return False

    def get_all_rules(self) -> List[AutoTagRule]:
        """获取所有规则

        Returns:
            List[AutoTagRule]: 所有规则列表
        """
        return self.rules.copy()

    def _save_rules_to_config(self) -> None:
        """保存规则到配置文件"""
        try:
            from smartvault.utils.config import load_config, save_config
            
            # 加载当前配置
            config = load_config()
            
            # 更新自动标签规则配置
            if "auto_tags" not in config:
                config["auto_tags"] = {}
            
            # 转换规则为配置格式
            rules_data = []
            for rule in self.rules:
                rule_data = {
                    "id": rule.id,
                    "name": rule.name,
                    "tag_names": rule.tag_names,
                    "enabled": rule.enabled,
                    "priority": rule.priority
                }
                
                # 处理新版本的条件组结构
                if hasattr(rule, 'condition_group') and rule.condition_group:
                    rule_data["condition_group"] = self._condition_group_to_dict(rule.condition_group)
                else:
                    # 兼容旧版本的单条件结构
                    rule_data["condition_type"] = rule.condition_type.value if rule.condition_type else "file_extension"
                    rule_data["condition_value"] = rule.condition_value or ""
                
                rules_data.append(rule_data)
            
            config["auto_tags"]["rules"] = rules_data
            config["auto_tags"]["enabled"] = self.enabled
            config["auto_tags"]["enable_ai"] = self.ai_enabled
            
            # 保存配置
            save_config(config)
            
        except Exception as e:
            print(f"保存自动标签规则配置失败: {e}")
    
    def _condition_group_to_dict(self, condition_group: ConditionGroup) -> Dict[str, Any]:
        """将条件组转换为字典格式"""
        result = {
            "operator": condition_group.operator.value,
            "conditions": []
        }
        
        for condition in condition_group.conditions:
            if isinstance(condition, ConditionGroup):
                # 递归处理嵌套条件组
                result["conditions"].append(self._condition_group_to_dict(condition))
            elif isinstance(condition, Condition):
                # 处理单个条件
                result["conditions"].append({
                    "type": condition.type.value,
                    "value": condition.value
                })
        
        return result
    
    def _dict_to_condition_group(self, condition_dict: Dict[str, Any]) -> ConditionGroup:
        """将字典格式转换为条件组对象"""
        operator = LogicOperator(condition_dict.get("operator", "and"))
        condition_group = ConditionGroup(operator=operator)
        
        for condition_data in condition_dict.get("conditions", []):
            if "operator" in condition_data:
                # 嵌套条件组
                nested_group = self._dict_to_condition_group(condition_data)
                condition_group.conditions.append(nested_group)
            else:
                # 单个条件
                condition = Condition(
                    type=ConditionType(condition_data.get("type", "file_extension")),
                    value=condition_data.get("value", "")
                )
                condition_group.conditions.append(condition)
        
        return condition_group
