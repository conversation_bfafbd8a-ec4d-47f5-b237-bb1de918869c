#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Core拆分模块自动化测试运行器
执行所有与Core拆分相关的测试用例
"""

import unittest
import sys
import os
from datetime import datetime
import traceback

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def run_test_suite():
    """运行完整的测试套件"""
    print("="*80)
    print("SmartVault Core拆分模块自动化测试")
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 测试模块列表
    test_modules = [
        'tests.test_monitor_handler',
        'tests.test_ai_integration_handler', 
        'tests.test_core_integration'
    ]
    
    # 加载测试模块
    loaded_tests = 0
    failed_imports = []
    
    for module_name in test_modules:
        try:
            print(f"加载测试模块: {module_name}")
            module_suite = loader.loadTestsFromName(module_name)
            suite.addTest(module_suite)
            test_count = module_suite.countTestCases()
            loaded_tests += test_count
            print(f"  ✅ 成功加载 {test_count} 个测试用例")
        except Exception as e:
            failed_imports.append((module_name, str(e)))
            print(f"  ❌ 加载失败: {e}")
    
    print(f"\n总共加载了 {loaded_tests} 个测试用例")
    
    if failed_imports:
        print("\n⚠️ 以下模块加载失败:")
        for module, error in failed_imports:
            print(f"  - {module}: {error}")
    
    if loaded_tests == 0:
        print("\n❌ 没有可执行的测试用例")
        return False
    
    print("\n" + "="*80)
    print("开始执行测试...")
    print("="*80)
    
    # 运行测试
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        buffer=True
    )
    
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "="*80)
    print("测试结果摘要")
    print("="*80)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    success = total_tests - failures - errors - skipped
    
    print(f"总测试数: {total_tests}")
    print(f"成功: {success}")
    print(f"失败: {failures}")
    print(f"错误: {errors}")
    print(f"跳过: {skipped}")
    
    success_rate = (success / total_tests * 100) if total_tests > 0 else 0
    print(f"成功率: {success_rate:.1f}%")
    
    # 详细失败信息
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback_str in result.failures:
            print(f"  - {test}")
            print(f"    {traceback_str.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback_str else 'Unknown failure'}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback_str in result.errors:
            print(f"  - {test}")
            print(f"    {traceback_str.split('Exception:')[-1].strip() if 'Exception:' in traceback_str else 'Unknown error'}")
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 返回测试是否全部通过
    return failures == 0 and errors == 0


def generate_test_report(success):
    """生成测试报告"""
    report_content = f"""
# Core拆分模块测试报告

**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试概述

本次测试针对SmartVault项目的Core拆分模块进行全面验证，包括:

### 已完成的拆分模块
- ✅ **监控处理器 (MonitorHandler)**: 文件监控事件处理、重复文件处理、批量处理
- ✅ **AI集成处理器 (AIIntegrationHandler)**: AI标签建议、用户反馈处理

### 测试覆盖范围
1. **单元测试**: 各处理器类的独立功能测试
2. **集成测试**: 处理器与主窗口的集成验证
3. **信号连接测试**: 事件处理和信号传递验证
4. **错误处理测试**: 异常情况的处理能力验证

## 测试结果

**整体状态**: {'✅ 通过' if success else '❌ 失败'}

### 测试用例执行情况
- 监控处理器单元测试
- AI集成处理器单元测试  
- Core集成测试

## 下一步计划

### 待完成的拆分任务
- [ ] **数据加载器 (DataLoader)**: 数据加载与分页模块拆分
- [ ] 完整的功能回归测试
- [ ] 性能基准测试

### 建议
1. 继续完成第三阶段的数据加载器拆分
2. 增加更多的边界条件测试
3. 添加性能测试用例
4. 完善错误处理机制

---
*本报告由自动化测试系统生成*
"""
    
    # 保存报告
    report_file = f"core_split_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n📄 测试报告已保存到: {report_file}")


def main():
    """主函数"""
    try:
        # 检查测试环境
        print("检查测试环境...")
        
        # 检查必要的模块是否存在
        required_files = [
            'smartvault/ui/main_window/monitor_handler.py',
            'smartvault/ui/main_window/ai_integration_handler.py',
            'tests/test_monitor_handler.py',
            'tests/test_ai_integration_handler.py',
            'tests/test_core_integration.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            print("❌ 以下必要文件缺失:")
            for file_path in missing_files:
                print(f"  - {file_path}")
            print("\n请确保所有拆分模块和测试文件都已创建")
            return False
        
        print("✅ 测试环境检查通过")
        
        # 运行测试套件
        success = run_test_suite()
        
        # 生成测试报告
        generate_test_report(success)
        
        if success:
            print("\n🎉 所有测试通过！Core拆分模块工作正常")
            return True
        else:
            print("\n⚠️ 部分测试失败，请检查相关模块")
            return False
            
    except Exception as e:
        print(f"\n💥 测试运行过程中发生错误: {e}")
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)