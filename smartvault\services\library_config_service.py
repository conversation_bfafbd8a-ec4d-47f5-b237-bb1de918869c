"""
SmartVault 文件库配置服务
实现配置信息存储在文件库内，支持多文件库独立配置
"""

import os
import json
import shutil
from smartvault.utils.config import normalize_path


class LibraryConfigService:
    """文件库配置服务类"""

    def __init__(self, library_path=None):
        self.library_path = library_path
        self.config_cache = {}

    def get_library_config_path(self, library_path=None):
        """获取文件库配置文件路径

        Args:
            library_path: 文件库路径，如果为None则使用当前库路径

        Returns:
            str: 配置文件路径
        """
        if library_path is None:
            library_path = self.library_path

        if not library_path:
            raise ValueError("文件库路径未设置")

        config_dir = os.path.join(library_path, "config")
        os.makedirs(config_dir, exist_ok=True)
        return os.path.join(config_dir, "library_config.json")

    def get_default_library_config(self):
        """获取默认文件库配置"""
        return {
            # 文件库基本信息
            "library_info": {
                "name": "SmartVault文件库",
                "created_at": "",
                "version": "1.0.0",
                "description": ""
            },

            # UI设置
            "ui": {
                "default_view_mode": "table",
                "default_page_size": 100,
                "theme": "system",
                "show_preview": True,
                "thumbnail_size": 128,
                "window_geometry": None,
                "splitter_state": None
            },

            # 搜索设置
            "search": {
                "default_scope": "database",
                "max_results": 1000,
                "case_sensitive": False,
                "whole_word": False,
                "search_history": []
            },

            # 文件操作设置
            "file_operations": {
                "default_entry_type": "link",
                "delete_physical_files": True,
                "auto_generate_thumbnails": True,
                "duplicate_handling": "prompt"
            },

            # 监控设置
            "monitor": {
                "auto_start": True,
                "startup_delay_check": True,
                "delay_check_seconds": 5,
                "smart_duplicate_handling": True,
                "batch_error_display": True,
                "event_interval_ms": 500,
                "monitor_folders": []
            },

            # 备份设置
            "backup": {
                "enabled": True,
                "interval_hours": 24,
                "max_backups": 7,
                "backup_on_startup": True,
                "backup_on_shutdown": False,  # 默认禁用关闭时备份，提高用户体验
                "compress": False
            },

            # 标签设置
            "tags": {
                "auto_tag_rules": [],
                "default_tag_colors": [
                    "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4",
                    "#FFEAA7", "#DDA0DD", "#98D8C8", "#F7DC6F"
                ],
                "show_tag_count": True
            },

            # 高级设置
            "advanced": {
                "enable_logging": True,
                "log_level": "INFO",
                "log_file_size_mb": 10,
                "database_cache_size": 10000,
                "enable_ai_features": False
            },

            # AI功能设置 - 迁移到用户库配置
            "ai": {
                "enabled": False,  # AI功能启用状态
                "stage": "rule_based",  # AI能力阶段: rule_based, ml_basic, deep_learning
                "models": {
                    "text_classifier": {
                        "enabled": False,
                        "model_path": "",
                        "confidence_threshold": 0.6
                    },
                    "content_analyzer": {
                        "enabled": False,
                        "model_path": "",
                        "max_content_length": 10000
                    }
                },
                "features": {
                    "project_detection": {
                        "enabled": True,
                        "min_files": 3,
                        "confidence_threshold": 0.7
                    },
                    "series_detection": {
                        "enabled": True,
                        "min_files": 2,
                        "confidence_threshold": 0.6
                    },
                    "behavior_learning": {
                        "enabled": True,
                        "learning_rate": 0.1,
                        "max_patterns": 1000
                    },
                    "smart_suggestions": {
                        "enabled": True,
                        "max_suggestions": 8,
                        "merge_with_rules": True
                    },
                    "ml_basic": {
                        "enabled": False,
                        "model_path": "",
                        "confidence_threshold": 0.6
                    }
                },
                "performance": {
                    "async_processing": True,
                    "cache_enabled": True,
                    "cache_size_mb": 50,
                    "timeout_seconds": 30
                }
            }
        }

    def load_library_config(self, library_path=None):
        """加载文件库配置

        Args:
            library_path: 文件库路径

        Returns:
            dict: 配置字典
        """
        if library_path is None:
            library_path = self.library_path

        # 检查缓存
        if library_path in self.config_cache:
            return self.config_cache[library_path]

        try:
            config_path = self.get_library_config_path(library_path)

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 合并默认配置，确保所有配置项都存在
                default_config = self.get_default_library_config()
                config = self._merge_config_recursive(config, default_config)
            else:
                # 创建默认配置
                config = self.get_default_library_config()
                config["library_info"]["created_at"] = self._get_current_timestamp()
                self.save_library_config(config, library_path)

            # 缓存配置
            self.config_cache[library_path] = config
            return config

        except Exception as e:
            print(f"加载文件库配置失败: {e}")
            return self.get_default_library_config()

    def save_library_config(self, config, library_path=None):
        """保存文件库配置

        Args:
            config: 配置字典
            library_path: 文件库路径
        """
        if library_path is None:
            library_path = self.library_path

        try:
            config_path = self.get_library_config_path(library_path)

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)

            # 更新缓存
            self.config_cache[library_path] = config
            print(f"✅ 文件库配置已保存: {config_path}")

        except Exception as e:
            print(f"保存文件库配置失败: {e}")

    def switch_library(self, new_library_path):
        """切换到新的文件库

        Args:
            new_library_path: 新文件库路径

        Returns:
            dict: 新文件库的配置
        """
        self.library_path = normalize_path(new_library_path)
        
        # 确保新文件库的目录结构完整
        self._ensure_library_structure(new_library_path)
        
        # 加载配置并确保AI配置完整
        config = self.load_library_config()
        self._ensure_ai_config_complete(config, new_library_path)
        
        return config

    def create_library_structure(self, library_path, library_name="SmartVault文件库"):
        """创建完整的文件库结构

        Args:
            library_path: 文件库路径
            library_name: 文件库名称

        Returns:
            tuple: (success, message)
        """
        try:
            # 创建基本目录结构
            directories = [
                "data",        # 数据库文件
                "config",      # 配置文件
                "backups",     # 备份文件
                "logs",        # 日志文件
                "temp",        # 临时文件
                "files",       # 用户文件（如果使用copy/move模式）
                "ai_models"    # AI模型文件（用户库分离模式）
            ]

            for dir_name in directories:
                dir_path = os.path.join(library_path, dir_name)
                os.makedirs(dir_path, exist_ok=True)
                print(f"✅ 创建目录: {dir_path}")

            # 创建文件库配置
            config = self.get_default_library_config()
            config["library_info"]["name"] = library_name
            config["library_info"]["created_at"] = self._get_current_timestamp()

            self.save_library_config(config, library_path)

            # 创建说明文件
            self._create_readme_file(library_path, library_name)

            # 创建.smartvault标识文件
            self._create_library_marker(library_path)

            # 迁移现有AI模型文件（如果存在）
            self._migrate_ai_models(library_path)

            return True, f"文件库 '{library_name}' 创建成功"

        except Exception as e:
            return False, f"创建文件库失败: {e}"

    def validate_library(self, library_path):
        """验证是否为有效的SmartVault文件库

        Args:
            library_path: 文件库路径

        Returns:
            bool: 是否为有效文件库
        """
        try:
            # 检查标识文件
            marker_file = os.path.join(library_path, ".smartvault")
            if not os.path.exists(marker_file):
                return False

            # 检查基本目录结构
            required_dirs = ["data", "config"]
            for dir_name in required_dirs:
                dir_path = os.path.join(library_path, dir_name)
                if not os.path.exists(dir_path):
                    return False

            # 确保AI相关目录存在
            self._ensure_ai_directories(library_path)

            # 检查配置文件
            config_path = self.get_library_config_path(library_path)
            if not os.path.exists(config_path):
                # 尝试创建默认配置
                config = self.get_default_library_config()
                self.save_library_config(config, library_path)
            else:
                # 验证并更新现有配置
                self._validate_and_update_config(library_path)

            return True

        except Exception as e:
            print(f"验证文件库失败: {e}")
            return False

    def migrate_global_config_to_library(self, library_path):
        """将全局配置迁移到文件库配置

        Args:
            library_path: 文件库路径
        """
        try:
            # 加载全局配置
            from smartvault.utils.config import load_config
            global_config = load_config()

            # 加载文件库配置
            library_config = self.load_library_config(library_path)

            # 迁移相关配置项
            migration_mapping = {
                "ui": "ui",
                "search": "search",
                "monitor": "monitor",
                "advanced": "advanced"
            }

            for global_key, library_key in migration_mapping.items():
                if global_key in global_config:
                    library_config[library_key].update(global_config[global_key])

            # 迁移其他设置
            if "default_entry_type" in global_config:
                library_config["file_operations"]["default_entry_type"] = global_config["default_entry_type"]

            if "delete_physical_files" in global_config:
                library_config["file_operations"]["delete_physical_files"] = global_config["delete_physical_files"]

            # 保存迁移后的配置
            self.save_library_config(library_config, library_path)
            print("✅ 全局配置已迁移到文件库配置")

        except Exception as e:
            print(f"配置迁移失败: {e}")

    def _merge_config_recursive(self, config, default_config):
        """递归合并配置"""
        for key, value in default_config.items():
            if key not in config:
                config[key] = value
            elif isinstance(value, dict) and isinstance(config[key], dict):
                config[key] = self._merge_config_recursive(config[key], value)
        return config

    def _get_current_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

    def _create_readme_file(self, library_path, library_name):
        """创建说明文件"""
        readme_content = f"""# {library_name}

这是一个SmartVault智能文件库。

## 目录结构

- `data/` - 数据库文件
- `config/` - 配置文件
- `backups/` - 自动备份文件
- `logs/` - 日志文件
- `temp/` - 临时文件
- `files/` - 用户文件（复制/移动模式）

## 重要提醒

⚠️ 请不要手动删除或修改此文件夹中的文件，这可能导致数据丢失。

📁 此文件库包含您的所有文件索引和配置信息。

💾 建议定期备份整个文件库文件夹。

---
创建时间: {self._get_current_timestamp()}
SmartVault版本: 1.0.0
"""

        readme_path = os.path.join(library_path, "README.md")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)

    def _create_library_marker(self, library_path):
        """创建文件库标识文件"""
        marker_content = {
            "type": "smartvault_library",
            "version": "1.0.0",
            "created_at": self._get_current_timestamp()
        }

        marker_path = os.path.join(library_path, ".smartvault")
        with open(marker_path, 'w', encoding='utf-8') as f:
            json.dump(marker_content, f, indent=2)

    def _ensure_library_structure(self, library_path):
        """确保文件库目录结构完整"""
        try:
            directories = [
                "data", "config", "backups", "logs", "temp", "files", "ai_models"
            ]
            
            for dir_name in directories:
                dir_path = os.path.join(library_path, dir_name)
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path, exist_ok=True)
                    print(f"✅ 补充创建目录: {dir_path}")
        except Exception as e:
            print(f"确保文件库结构失败: {e}")

    def _ensure_ai_directories(self, library_path):
        """确保AI相关目录存在"""
        try:
            ai_dirs = ["ai_models"]
            for dir_name in ai_dirs:
                dir_path = os.path.join(library_path, dir_name)
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path, exist_ok=True)
                    print(f"✅ 创建AI目录: {dir_path}")
        except Exception as e:
            print(f"创建AI目录失败: {e}")

    def _migrate_ai_models(self, library_path):
        """迁移现有AI模型文件到新文件库"""
        try:
            from smartvault.utils.config import get_app_data_dir
            
            new_model_dir = os.path.join(library_path, "ai_models")
            os.makedirs(new_model_dir, exist_ok=True)
            new_model_path = os.path.join(new_model_dir, "smartvault_ml_model.pkl")
            
            # 如果目标模型文件已存在，跳过
            if os.path.exists(new_model_path):
                print(f"✅ AI模型文件已存在: {new_model_path}")
                self._update_ai_model_config(library_path)
                return
            
            model_copied = False
            
            # 1. 首先检查旧版全局模型文件位置
            old_model_path = os.path.join(get_app_data_dir(), "smartvault_ml_model.pkl")
            if os.path.exists(old_model_path):
                shutil.copy2(old_model_path, new_model_path)
                print(f"✅ AI模型文件已从全局位置迁移: {old_model_path} -> {new_model_path}")
                model_copied = True
            
            # 2. 如果全局位置没有，尝试从当前库复制（如果当前库不是目标库）
            if not model_copied and hasattr(self, 'library_path') and self.library_path:
                current_model_path = os.path.join(self.library_path, "ai_models", "smartvault_ml_model.pkl")
                if os.path.exists(current_model_path) and os.path.normpath(self.library_path) != os.path.normpath(library_path):
                    shutil.copy2(current_model_path, new_model_path)
                    print(f"✅ AI模型文件已从当前库复制: {current_model_path} -> {new_model_path}")
                    model_copied = True
            
            # 3. 如果还是没有，尝试从已知的库位置复制
            if not model_copied:
                known_library_paths = [
                    "D:/temp4/SmartVault_Lib",  # 已知的旧库位置
                    os.path.join(get_app_data_dir(), "SmartVault_Lib"),  # 默认库位置
                ]
                
                for known_path in known_library_paths:
                    if os.path.exists(known_path) and os.path.normpath(known_path) != os.path.normpath(library_path):
                        known_model_path = os.path.join(known_path, "ai_models", "smartvault_ml_model.pkl")
                        if os.path.exists(known_model_path):
                            shutil.copy2(known_model_path, new_model_path)
                            print(f"✅ AI模型文件已从已知库复制: {known_model_path} -> {new_model_path}")
                            model_copied = True
                            break
            
            # 4. 如果仍然没有模型文件，创建一个默认的空模型文件占位符
            if not model_copied:
                self._create_default_model_file(new_model_path)
                print(f"✅ 已创建默认AI模型文件: {new_model_path}")
                model_copied = True
            
            # 更新配置中的模型路径
            if model_copied:
                self._update_ai_model_config(library_path)
                
        except Exception as e:
            print(f"迁移AI模型失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _create_default_model_file(self, model_path):
        """创建默认的AI模型文件"""
        try:
            # 创建一个简单的默认模型文件
            # 这里可以是一个预训练的基础模型或者占位符
            import pickle
            
            # 创建一个基础的模型结构
            default_model = {
                'model_type': 'basic_ml',
                'version': '1.0.0',
                'created_at': self._get_current_timestamp(),
                'features': [],
                'labels': [],
                'model_data': None,  # 实际的模型数据将在训练时填充
                'metadata': {
                    'description': 'SmartVault基础机器学习模型',
                    'status': 'placeholder',
                    'requires_training': True
                }
            }
            
            with open(model_path, 'wb') as f:
                pickle.dump(default_model, f)
                
        except Exception as e:
            print(f"创建默认模型文件失败: {e}")
    
    def _update_ai_model_config(self, library_path):
        """更新AI模型配置"""
        try:
            config = self.load_library_config(library_path)
            if "ai" in config and "features" in config["ai"] and "ml_basic" in config["ai"]["features"]:
                # 更新模型路径为相对路径
                config["ai"]["features"]["ml_basic"]["model_path"] = "smartvault_ml_model.pkl"
                # 启用ML基础功能
                config["ai"]["features"]["ml_basic"]["enabled"] = True
                # 启用AI功能
                config["ai"]["enabled"] = True
                
                self.save_library_config(config, library_path)
                print("✅ 已更新配置中的AI模型路径和启用状态")
        except Exception as e:
            print(f"更新AI模型配置失败: {e}")

    def _validate_and_update_config(self, library_path):
        """验证并更新现有配置"""
        try:
            config = self.load_library_config(library_path)
            default_config = self.get_default_library_config()
            
            # 检查是否需要更新AI配置
            updated = False
            if "ai" not in config:
                config["ai"] = default_config["ai"]
                updated = True
                print("✅ 添加缺失的AI配置")
            else:
                # 递归合并AI配置，确保所有新字段都存在
                ai_updated = self._merge_config_recursive(config["ai"], default_config["ai"])
                if ai_updated != config["ai"]:
                    config["ai"] = ai_updated
                    updated = True
                    print("✅ 更新AI配置字段")
            
            if updated:
                self.save_library_config(config, library_path)
                
        except Exception as e:
            print(f"验证和更新配置失败: {e}")

    def _ensure_ai_config_complete(self, config, library_path):
        """确保AI配置完整"""
        try:
            # 检查AI模型路径配置
            if "ai" in config and "features" in config["ai"] and "ml_basic" in config["ai"]["features"]:
                ml_config = config["ai"]["features"]["ml_basic"]
                model_path = ml_config.get("model_path", "")
                
                # 如果模型路径是相对路径，确保模型文件存在
                if model_path and not os.path.isabs(model_path):
                    full_model_path = os.path.join(library_path, "ai_models", model_path)
                    if not os.path.exists(full_model_path):
                        # 尝试从其他位置复制模型文件
                        self._migrate_ai_models(library_path)
                        
        except Exception as e:
            print(f"确保AI配置完整失败: {e}")

    def _merge_config_recursive(self, current_config, default_config):
        """递归合并配置，确保所有默认字段都存在"""
        if not isinstance(current_config, dict) or not isinstance(default_config, dict):
            return default_config
        
        merged = current_config.copy()
        
        for key, default_value in default_config.items():
            if key not in merged:
                merged[key] = default_value
            elif isinstance(default_value, dict) and isinstance(merged[key], dict):
                merged[key] = self._merge_config_recursive(merged[key], default_value)
        
        return merged


# 全局服务实例
_library_config_service = None

def get_library_config_service(library_path=None):
    """获取文件库配置服务实例"""
    global _library_config_service
    if _library_config_service is None:
        _library_config_service = LibraryConfigService(library_path)
    elif library_path and library_path != _library_config_service.library_path:
        _library_config_service.switch_library(library_path)
    return _library_config_service
