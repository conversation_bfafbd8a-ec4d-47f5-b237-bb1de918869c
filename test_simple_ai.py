#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试新库AI功能
"""

import sys
import os

# 添加项目路径
sys.path.append('d:/PythonProjects2/SmartVault3')

from smartvault.services.library_config_service import LibraryConfigService

print('=== 测试新库AI功能 ===')

service = LibraryConfigService()
config = service.switch_library('d:/PythonProjects2/SmartVault3/smartvault/SmartVault_Lib')

print(f'AI启用: {config.get("ai", {}).get("enabled", False)}')

ml_config = config.get('ai', {}).get('features', {}).get('ml_basic', {})
print(f'ML启用: {ml_config.get("enabled", False)}')
print(f'模型路径: {ml_config.get("model_path", "")}')

model_file = os.path.join('d:/PythonProjects2/SmartVault3/smartvault/SmartVault_Lib', 'ai_models', 'smartvault_ml_model.pkl')
print(f'模型文件存在: {os.path.exists(model_file)}')

if os.path.exists(model_file):
    file_size = os.path.getsize(model_file)
    print(f'模型文件大小: {file_size} bytes')

print('测试完成')