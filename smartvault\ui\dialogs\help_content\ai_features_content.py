# -*- coding: utf-8 -*-
"""
SmartVault AI功能帮助内容
"""

AI_FEATURES_CONTENT = {
    "AI功能概述": """# AI功能概述

## 🤖 SmartVault AI功能介绍

SmartVault集成了先进的AI技术，为您的文件管理提供智能化支持。AI功能采用分阶段实现策略，确保稳定性和实用性。

### 🎯 AI功能特点

- **智能标签建议**：基于文件名、类型、内容自动推荐合适的标签
- **项目识别**：自动识别编程项目、文档项目等不同类型
- **系列检测**：识别相关文件系列，如教程、版本等
- **行为学习**：从您的操作习惯中学习，提供个性化建议
- **自适应优化**：根据使用效果自动优化建议规则

### 📈 AI发展阶段

**第一阶段：智能规则引擎** ✅ 已完成
- 基于规则的智能标签建议
- 项目类型自动识别
- 文件系列检测
- 用户行为学习

**第二阶段：自适应规则引擎** ✅ 已完成
- 规则性能自动优化
- 动态规则调整
- 个性化建议改进

**第三阶段：机器学习引擎** ⏳ 规划中
- 轻量级ML模型集成
- 更精准的标签预测
- 内容语义分析

**第四阶段：深度学习引擎** ⏳ 未来规划
- 高级AI模型支持
- 多模态内容理解
- 智能文档分析

### 🔧 AI功能配置

AI功能可以在"设置 → AI功能"页面进行配置：

1. **启用/禁用AI功能**：总开关控制
2. **功能模块设置**：选择启用的AI功能
3. **性能参数调整**：根据需要调整AI参数
4. **学习模式配置**：设置行为学习选项

### 💡 使用建议

- **初次使用**：建议先启用基础功能，熟悉后再开启高级选项
- **性能考虑**：AI功能会消耗一定系统资源，可根据设备性能调整
- **隐私保护**：所有AI处理都在本地进行，不会上传您的文件内容
- **持续优化**：AI会根据您的使用习惯不断改进建议质量

### ⚠️ 注意事项

- AI功能需要一定的学习时间才能提供最佳建议
- 建议定期查看AI建议的准确性，提供反馈帮助改进
- 如遇到性能问题，可以临时关闭AI功能
- AI建议仅供参考，最终决策权在您手中
""",

    "智能标签建议": """# 智能标签建议

## 🏷️ 智能标签建议功能

智能标签建议是SmartVault AI功能的核心特性，能够根据文件特征自动推荐合适的标签，大大提高文件管理效率。

### 🎯 功能特点

**多维度分析**
- 文件名模式识别
- 文件类型特征分析
- 文件夹结构理解
- 历史标签模式学习

**智能建议算法**
- 基于规则的快速匹配
- 模式识别和相似度计算
- 用户习惯学习和适应
- 上下文感知建议

### 🔍 建议来源

**1. 文件名分析**
- 关键词提取：从文件名中识别重要关键词
- 模式匹配：识别常见的命名模式
- 版本检测：识别版本号、日期等信息

**2. 文件类型识别**
- 扩展名映射：根据文件扩展名推荐类型标签
- 格式特征：识别文档、图片、代码等不同格式
- 用途推断：根据文件类型推断可能用途

**3. 项目检测**
- 编程项目：识别Python、Java、Web等项目类型
- 文档项目：识别报告、手册、教程等文档类型
- 媒体项目：识别图片集、视频系列等媒体内容

**4. 系列识别**
- 相关文件：识别属于同一系列的文件
- 版本管理：识别不同版本的文件
- 分集内容：识别教程、课程等分集内容

### 🚀 使用方法

**智能标签建议面板**
1. **位置**：主界面右侧的"🤖 智能标签建议"面板
2. **触发**：选择单个文件时自动显示AI建议
3. **显示内容**：
   - 当前选中的文件名
   - AI建议的标签列表
   - 每个标签的可信度（进度条显示）
   - 接受/拒绝按钮

**操作方式**
1. **单个标签操作**：
   - 点击"✓ 接受"按钮应用标签到文件
   - 点击"✗ 拒绝"按钮忽略该建议

2. **批量操作**：
   - 点击"✓ 全部接受"应用所有建议标签
   - 点击"✗ 全部拒绝"忽略所有建议

3. **可信度参考**：
   - 绿色（80%+）：高可信度，建议接受
   - 橙色（60-80%）：中等可信度，谨慎考虑
   - 红色（60%以下）：低可信度，建议拒绝

**自动建议触发**
1. 添加新文件时自动应用AI标签（如果启用）
2. 选择文件时在右侧面板显示建议
3. 批量处理时获取多文件建议

**其他触发方式**
1. 在文件右键菜单中选择"AI标签建议"
2. 在标签管理对话框中点击"AI建议"
3. 使用快捷键触发智能建议

### ⚙️ 配置选项

**建议数量**
- 最大建议数：控制每次显示的建议数量
- 置信度阈值：只显示高置信度的建议
- 去重策略：避免重复或相似的建议

**学习模式**
- 启用行为学习：从您的标签操作中学习
- 学习速度：控制AI适应您习惯的速度
- 模式记忆：记住常用的标签模式

**性能设置**
- 异步处理：在后台生成建议，不影响界面响应
- 缓存策略：缓存常用建议，提高响应速度
- 超时设置：避免AI处理时间过长

### 💡 优化建议

**提高准确性**
- 使用规范的文件命名
- 保持文件夹结构清晰
- 及时反馈建议质量

**个性化定制**
- 经常使用的标签会被优先推荐
- AI会学习您的标签习惯
- 可以手动调整建议权重

**效率提升**
- 批量处理时使用AI建议
- 建立标签模板供AI学习
- 定期清理无用标签

### 🔧 故障排除

**建议不准确**
- 检查文件命名是否规范
- 确认AI功能已正确启用
- 尝试重新训练AI模型

**响应速度慢**
- 检查系统资源使用情况
- 调整AI性能设置
- 清理AI缓存数据

**功能无法使用**
- 确认AI总开关已启用
- 检查AI管理器初始化状态
- 查看错误日志获取详细信息
""",

    "AI设置配置": """# AI设置配置

## ⚙️ AI功能设置指南

SmartVault的AI功能提供了丰富的配置选项，您可以根据需要和设备性能进行个性化设置。

### 🎛️ 基础设置

**AI功能总开关**
- 位置：设置 → AI功能 → 启用AI功能
- 作用：控制所有AI功能的启用/禁用
- 建议：首次使用建议先启用，体验后再决定

**AI阶段选择**
- 智能规则引擎：基础AI功能，推荐启用
- 自适应规则引擎：高级优化功能
- 机器学习引擎：未来功能，当前不可用

### 🔧 功能模块配置

**项目检测设置**
- 启用状态：是否启用项目自动识别
- 最小文件数：识别项目所需的最少文件数量
- 置信度阈值：项目识别的可信度要求

**系列检测设置**
- 启用状态：是否启用文件系列识别
- 最小文件数：识别系列所需的最少文件数量
- 置信度阈值：系列识别的可信度要求

**行为学习设置**
- 启用状态：是否从用户操作中学习
- 学习速率：AI适应用户习惯的速度
- 最大模式数：保存的行为模式数量上限

**智能建议设置**
- 启用状态：是否提供智能标签建议
- 最大建议数：每次显示的建议数量
- 与规则合并：是否与自动标签规则合并

### 📊 性能配置

**处理性能**
- 异步处理：在后台处理AI任务，不阻塞界面
- 缓存启用：缓存AI结果，提高响应速度
- 缓存大小：AI缓存占用的内存大小
- 超时时间：AI处理的最大等待时间

**资源控制**
- CPU使用限制：限制AI功能的CPU占用
- 内存使用限制：限制AI功能的内存占用
- 并发任务数：同时处理的AI任务数量

### 🎯 高级设置

**模型配置**
- 文本分类器：用于内容分析的AI模型
- 内容分析器：用于文档内容理解的模型
- 模型路径：自定义AI模型文件位置
- 置信度阈值：模型预测的可信度要求

**学习参数**
- 学习率：AI学习新模式的速度
- 遗忘因子：旧模式的衰减速度
- 更新频率：模型更新的时间间隔
- 样本权重：不同类型样本的重要性

### 🔍 状态监控

**AI状态显示**
- 启用状态：当前AI功能是否启用
- 运行阶段：当前AI所处的发展阶段
- 初始化状态：AI管理器的初始化情况
- 错误信息：如有错误会显示详细信息

**功能状态**
- 智能规则引擎：✅ 可用 / ❌ 不可用
- 自适应规则引擎：✅ 可用 / ❌ 不可用
- 机器学习引擎：❌ 未启用（当前版本）

**性能统计**
- 处理任务数：AI已处理的任务总数
- 平均响应时间：AI处理任务的平均耗时
- 缓存命中率：缓存使用效率
- 错误率：AI处理失败的比例

### 🧪 测试功能

**AI功能测试**
- 测试按钮：验证AI功能是否正常工作
- 测试结果：显示AI标签建议示例
- 性能测试：检查AI响应速度
- 错误诊断：识别AI功能问题

**建议质量评估**
- 准确性测试：评估建议标签的准确性
- 相关性测试：评估建议与文件的相关性
- 多样性测试：评估建议的多样性
- 用户满意度：收集用户对建议的反馈

### 💾 配置管理

**配置保存**
- 自动保存：修改设置后自动保存
- 手动保存：点击保存按钮确认修改
- 配置验证：检查设置的有效性
- 错误提示：无效设置的错误提示

**配置重置**
- 重置为默认：恢复所有AI设置为默认值
- 部分重置：只重置特定模块的设置
- 备份配置：保存当前配置为备份
- 导入配置：从备份文件恢复配置

### 🚨 故障排除

**常见问题**
- AI功能无法启用：检查系统要求和依赖
- 建议质量差：调整学习参数和阈值设置
- 响应速度慢：优化性能设置和缓存配置
- 内存占用高：减少缓存大小和并发任务数

**诊断步骤**
1. 检查AI总开关是否启用
2. 查看AI状态显示是否正常
3. 运行AI功能测试
4. 检查错误日志
5. 尝试重置配置

**获取帮助**
- 查看详细错误信息
- 检查系统日志
- 联系技术支持
- 参考在线文档
"""
}
